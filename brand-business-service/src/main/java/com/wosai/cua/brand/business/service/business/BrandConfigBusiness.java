package com.wosai.cua.brand.business.service.business;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.citic.CiticConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.fuiou.FuiouConfigDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.SmsTemplateMethodEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandSmsTemplateConfigDOService;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppOpenResultNoticeRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.TemplateConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BrandConfigBusiness {

    private final BrandDomainService brandDomainService;

    private final BrandConfigDomainService brandConfigDomainService;

    private final MerchantBusinessOpenClient merchantBusinessOpenClient;

    private final MerchantCenterClient merchantCenterClient;

    private final BrandSmsTemplateConfigDOService brandSmsTemplateConfigService;

    @Value("${devCode.brandPaymentHub}")
    private String brandPaymentHubDevCode;

    private final ApolloConfig apolloConfig;

    @Autowired
    public BrandConfigBusiness(BrandDomainService brandDomainService, BrandConfigDomainService brandConfigDomainService, MerchantBusinessOpenClient merchantBusinessOpenClient, MerchantCenterClient merchantCenterClient, BrandSmsTemplateConfigDOService brandSmsTemplateConfigService, ApolloConfig apolloConfig) {
        this.brandDomainService = brandDomainService;
        this.brandConfigDomainService = brandConfigDomainService;
        this.merchantBusinessOpenClient = merchantBusinessOpenClient;
        this.merchantCenterClient = merchantCenterClient;
        this.brandSmsTemplateConfigService = brandSmsTemplateConfigService;
        this.apolloConfig = apolloConfig;
    }

    public Boolean createBrandConfigWithNotify(ConfigDTO configDTO) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(configDTO.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        Boolean result = createBrandConfig(brandModule, configDTO);
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        merchantBusinessOpenClient.noticeAppOpenResult(new AppOpenResultNoticeRequest()
                .setMerchantId(merchantInfoQueryResult.getMerchantId())
                .setDevCode(brandPaymentHubDevCode)
                .setStatus(AppInfoStatusEnum.SUCCESS));
        return result;
    }

    public Boolean createBrandConfig(BrandModule brandModule, ConfigDTO configDTO) {
        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        if (Objects.nonNull(brandConfigModule)) {
            switch (brandModule.getFundManagementCompanyCode()) {
                case MY_BANK:
                    this.updateMyBankConfig(configDTO, brandConfigModule);
                    break;
                case PAB:
                    this.updatePabConfig(configDTO, brandConfigModule);
                    break;
                case CITIC:
                    this.updateCiticConfig(brandConfigModule, configDTO, JSON.parseObject(brandConfigModule.getConfig(), CiticBankConfigModule.class));
                    break;
                case FUIOU:
                    this.updateFuiouConfig(brandConfigModule, configDTO, JSON.parseObject(brandConfigModule.getConfig(), FuiouConfigModule.class));
                    break;
                default:
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
            }
            return true;
        }
        brandConfigModule = new BrandConfigModule();
        switch (brandModule.getFundManagementCompanyCode()) {
            case MY_BANK:
                brandConfigModule.setBrandId(configDTO.getBrandId());
                brandConfigModule.setChannelId(configDTO.getMyBankConfig().getAppId());
                brandConfigModule.setChannelType(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
                MyBankConfigModule myBankConfigModule = this.getMyBankConfigModule(configDTO);
                brandConfigModule.setConfig(JSON.toJSONString(myBankConfigModule));
                brandConfigDomainService.updateBrandConfig(brandConfigModule);
                break;
            case PAB:
                this.updatePabConfig(brandConfigModule, configDTO, new PabConfigModule());
                break;
            case CITIC:
                this.updateCiticConfig(brandConfigModule, configDTO, new CiticBankConfigModule());
                break;
            case FUIOU:
                this.updateFuiouConfig(brandConfigModule, configDTO, new FuiouConfigModule());
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND_CONFIG);
        }
        return true;
    }

    private void updateMyBankConfig(ConfigDTO configDTO, BrandConfigModule brandConfigModule) {
        MyBankConfigModule myBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class);
        brandConfigModule.setChannelId(configDTO.getMyBankConfig().getAppId());
        brandConfigModule.setChannelType(FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        myBankConfigModule.setPublicKey(configDTO.getMyBankConfig().getPublicKey());
        myBankConfigModule.setAllowLogin(!Objects.nonNull(configDTO.getMyBankConfig().getAllowLogin()) || configDTO.getMyBankConfig().getAllowLogin());
        myBankConfigModule.setAppId(configDTO.getMyBankConfig().getAppId());
        myBankConfigModule.setOpenTopUpAccount(Objects.nonNull(configDTO.getMyBankConfig().getOpenTopUpAccount()) && configDTO.getMyBankConfig().getOpenTopUpAccount());
        myBankConfigModule.setIsvOrgId(configDTO.getMyBankConfig().getIsvOrgId());
        myBankConfigModule.setIsvPrivateKey(configDTO.getMyBankConfig().getIsvPrivateKey());
        myBankConfigModule.setIsvPublicKey(configDTO.getMyBankConfig().getIsvPublicKey());
        brandConfigModule.setConfig(JSON.toJSONString(myBankConfigModule));
        brandConfigDomainService.updateBrandConfig(brandConfigModule);
    }

    private void updatePabConfig(BrandConfigModule brandConfigModule, ConfigDTO configDTO, PabConfigModule pabConfigModule) {
        brandConfigModule.setBrandId(configDTO.getBrandId());
        brandConfigModule.setChannelId(configDTO.getPabConfig().getPartnerId());
        brandConfigModule.setChannelType(FundManagementCompanyEnum.PAB.getFundManagementCompanyCode());
        pabConfigModule.setAllowLogin(!Objects.nonNull(configDTO.getPabConfig().getAllowLogin()) || configDTO.getPabConfig().getAllowLogin());
        pabConfigModule.setMeiTuanAppid(StringUtils.isBlank(configDTO.getPabConfig().getMeiTuanAppid()) ? pabConfigModule.getMeiTuanAppid() : configDTO.getPabConfig().getMeiTuanAppid());
        pabConfigModule.setMeiTuanSecret(StringUtils.isBlank(configDTO.getPabConfig().getMeiTuanSecret()) ? pabConfigModule.getMeiTuanSecret() : configDTO.getPabConfig().getMeiTuanSecret());
        pabConfigModule.setPartnerId(StringUtils.isBlank(configDTO.getPabConfig().getPartnerId()) ? pabConfigModule.getPartnerId() : configDTO.getPabConfig().getPartnerId());
        pabConfigModule.setNeedSpecialTreatment(Objects.nonNull(configDTO.getPabConfig().getNeedSpecialTreatment()) ? configDTO.getPabConfig().getNeedSpecialTreatment() : pabConfigModule.getNeedSpecialTreatment());
        brandConfigModule.setConfig(JSON.toJSONString(pabConfigModule));
        brandConfigDomainService.updateBrandConfig(brandConfigModule);
    }

    private void updatePabConfig(ConfigDTO configDTO, BrandConfigModule brandConfigModule) {
        updatePabConfig(brandConfigModule, configDTO, JSON.parseObject(brandConfigModule.getConfig(), PabConfigModule.class));
    }

    private void updateFuiouConfig(BrandConfigModule brandConfigModule, ConfigDTO configDTO, FuiouConfigModule configModule) {
        brandConfigModule.setBrandId(configDTO.getBrandId());
        brandConfigModule.setChannelId(configDTO.getFuiouConfig().getMerchantNo());
        brandConfigModule.setChannelType(FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode());
        FuiouConfigModule fuiouConfigModule = this.getFuiouConfigModule(configModule, configDTO);
        brandConfigModule.setConfig(JSON.toJSONString(fuiouConfigModule));
        brandConfigDomainService.updateBrandConfig(brandConfigModule);
    }

    private void updateCiticConfig(BrandConfigModule brandConfigModule, ConfigDTO configDTO, CiticBankConfigModule citicConfigModule) {
        brandConfigModule.setBrandId(configDTO.getBrandId());
        brandConfigModule.setChannelId(configDTO.getCiticConfig().getMerchantId());
        brandConfigModule.setChannelType(FundManagementCompanyEnum.CITIC.getFundManagementCompanyCode());
        this.getCiticBankConfigModule(citicConfigModule, configDTO);
        brandConfigModule.setConfig(JSON.toJSONString(citicConfigModule));
        brandConfigDomainService.updateBrandConfig(brandConfigModule);
    }

    private FuiouConfigModule getFuiouConfigModule(FuiouConfigModule fuiouConfigModule, ConfigDTO configDTO) {
        FuiouConfigDTO fuiouConfig = configDTO.getFuiouConfig();
        if (Objects.isNull(fuiouConfig)) {
            return fuiouConfigModule;
        }
        fuiouConfigModule.setAllowLogin(!Objects.nonNull(fuiouConfig.getAllowLogin()) || fuiouConfig.getAllowLogin());
        fuiouConfigModule.setPublicKey(StringUtils.isBlank(fuiouConfig.getPublicKey()) ? fuiouConfigModule.getPublicKey() : fuiouConfig.getPublicKey());
        fuiouConfigModule.setPrivateKey(StringUtils.isBlank(fuiouConfig.getPrivateKey()) ? fuiouConfigModule.getPrivateKey() : fuiouConfig.getPrivateKey());
        fuiouConfigModule.setMerchantNo(StringUtils.isBlank(fuiouConfig.getMerchantNo()) ? fuiouConfigModule.getMerchantNo() : fuiouConfig.getMerchantNo());
        fuiouConfigModule.setFyPrivateKey(StringUtils.isBlank(fuiouConfig.getFyPrivateKey()) ? fuiouConfigModule.getFyPrivateKey() : fuiouConfig.getFyPrivateKey());
        fuiouConfigModule.setFyPublicKey(StringUtils.isBlank(fuiouConfig.getFyPublicKey()) ? fuiouConfigModule.getFyPublicKey() : fuiouConfig.getFyPublicKey());
        fuiouConfigModule.setMerchantDockingMode(Objects.nonNull(fuiouConfig.getMerchantDockingMode()) ? fuiouConfig.getMerchantDockingMode().getCode() : fuiouConfigModule.getMerchantDockingMode());
        fuiouConfigModule.setDepositStrategy(Objects.nonNull(fuiouConfig.getDepositStrategy()) ? fuiouConfig.getDepositStrategy().getDepositStrategy() : fuiouConfigModule.getDepositStrategy());
        fuiouConfigModule.setMeiTuanAppid(StringUtils.isBlank(fuiouConfig.getMeiTuanAppid()) ? fuiouConfigModule.getMeiTuanAppid() : fuiouConfig.getMeiTuanAppid());
        fuiouConfigModule.setMeiTuanSecret(StringUtils.isBlank(fuiouConfig.getMeiTuanSecret()) ? fuiouConfigModule.getMeiTuanSecret() : fuiouConfig.getMeiTuanSecret());
        fuiouConfigModule.setNeedSpecialTreatment(Objects.nonNull(fuiouConfig.getNeedSpecialTreatment()) ? fuiouConfig.getNeedSpecialTreatment() : fuiouConfigModule.getNeedSpecialTreatment());
        return fuiouConfigModule;
    }

    private void getCiticBankConfigModule(CiticBankConfigModule citicConfigModule, ConfigDTO configDTO) {
        CiticConfigDTO citicConfig = configDTO.getCiticConfig();
        if (Objects.isNull(citicConfig)) {
            return;
        }
        citicConfigModule.setMerchantId(StringUtils.isEmpty(citicConfig.getMerchantId()) ? citicConfigModule.getMerchantId() : citicConfig.getMerchantId());
        citicConfigModule.setPrivateKey(StringUtils.isBlank(citicConfig.getPrivateKey()) ? citicConfigModule.getPrivateKey() : citicConfig.getPrivateKey());
        citicConfigModule.setPrivateKeyPassword(StringUtils.isBlank(citicConfig.getPrivateKeyPassword()) ? citicConfigModule.getPrivateKeyPassword() : citicConfig.getPrivateKeyPassword());
        citicConfigModule.setPublicKey(StringUtils.isBlank(citicConfig.getPublicKey()) ? citicConfigModule.getPublicKey() : citicConfig.getPublicKey());
        citicConfigModule.setAllowLogin(!Objects.nonNull(citicConfig.getAllowLogin()) || citicConfig.getAllowLogin());
        citicConfigModule.setAllowLogin(!Objects.nonNull(citicConfig.getOpenTransfer()) || citicConfig.getOpenTransfer());
        citicConfigModule.setMeiTuanAppid(StringUtils.isBlank(citicConfig.getMeiTuanAppid()) ? citicConfigModule.getMeiTuanAppid() : citicConfig.getMeiTuanAppid());
        citicConfigModule.setMeiTuanSecret(StringUtils.isBlank(citicConfig.getMeiTuanSecret()) ? citicConfigModule.getMeiTuanSecret() : citicConfig.getMeiTuanSecret());
        citicConfigModule.setNeedSpecialTreatment(Objects.nonNull(citicConfig.getNeedSpecialTreatment()) ? citicConfig.getNeedSpecialTreatment() : citicConfigModule.getNeedSpecialTreatment());
    }

    private MyBankConfigModule getMyBankConfigModule(ConfigDTO configDTO) {
        MyBankConfigModule myBankConfigModule = new MyBankConfigModule();
        myBankConfigModule.setAppId(configDTO.getMyBankConfig().getAppId());
        myBankConfigModule.setIsvOrgId(configDTO.getMyBankConfig().getIsvOrgId());
        myBankConfigModule.setIsvPrivateKey(configDTO.getMyBankConfig().getIsvPrivateKey());
        myBankConfigModule.setIsvPublicKey(configDTO.getMyBankConfig().getIsvPublicKey());
        myBankConfigModule.setPublicKey(configDTO.getMyBankConfig().getPublicKey());
        myBankConfigModule.setAllowLogin(!Objects.nonNull(configDTO.getMyBankConfig().getAllowLogin()) || configDTO.getMyBankConfig().getAllowLogin());
        myBankConfigModule.setOpenTopUpAccount(Objects.nonNull(configDTO.getMyBankConfig().getOpenTopUpAccount()) && configDTO.getMyBankConfig().getOpenTopUpAccount());
        myBankConfigModule.setMeiTuanAppid(StringUtils.isBlank(configDTO.getMyBankConfig().getMeiTuanAppid()) ? myBankConfigModule.getMeiTuanAppid() : configDTO.getMyBankConfig().getMeiTuanAppid());
        myBankConfigModule.setMeiTuanSecret(StringUtils.isBlank(configDTO.getMyBankConfig().getMeiTuanSecret()) ? myBankConfigModule.getMeiTuanSecret() : configDTO.getMyBankConfig().getMeiTuanSecret());
        myBankConfigModule.setNeedSpecialTreatment(Objects.nonNull(configDTO.getMyBankConfig().getNeedSpecialTreatment()) ? configDTO.getMyBankConfig().getNeedSpecialTreatment() : myBankConfigModule.getNeedSpecialTreatment());
        return myBankConfigModule;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBrandSmsTemplateConfig(String brandId,SmsTemplateMethodEnum method, List<TemplateConfigDTO> templateConfigList) {
        if (CollectionUtils.isEmpty(templateConfigList)) {
            return;
        }
        switch (method){
            case SMS:
                List<TemplateConfigDTO> defaultTemplateConfigs = JSON.parseArray(apolloConfig.getSmsTemplateConfig(), TemplateConfigDTO.class);
                Map<String, TemplateConfigDTO> dtoMap = defaultTemplateConfigs.stream().collect(Collectors.toMap(TemplateConfigDTO::getTemplateCode, Function.identity()));
                brandSmsTemplateConfigService.saveBrandSmsTemplateConfig(brandId, TemplateConfigModule.getTemplateConfigList(templateConfigList, dtoMap));
                break;
            default:
                break;
        }
    }

    public List<TemplateConfigDTO> getBrandSmsTemplateConfig(String brandId, SmsTemplateMethodEnum method, List<String> templateCodes) {
        return TemplateConfigModule.convertTemplateConfigDtoByModule(brandSmsTemplateConfigService.getBrandSmsTemplateConfig(brandId, method, templateCodes));
    }

    private List<TemplateConfigDTO> completeTemplateConfigList(List<TemplateConfigDTO> templateConfigList, String apolloString) {
        Map<String, String> codeValueMap = templateConfigList.stream().collect(Collectors.toMap(TemplateConfigDTO::getTemplateCode, TemplateConfigDTO::getTextContent, (v1, v2) -> v1));
        return JSON.parseArray(apolloString, TemplateConfigDTO.class).stream().map(templateConfigDTO -> {
            String context = codeValueMap.get(templateConfigDTO.getTemplateCode());
            templateConfigDTO.setTextContent(Objects.nonNull(context) ? context : "");
            return templateConfigDTO;
        }).collect(Collectors.toList());
    }
}
