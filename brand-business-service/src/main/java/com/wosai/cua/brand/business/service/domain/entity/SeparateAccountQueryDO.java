package com.wosai.cua.brand.business.service.domain.entity;

import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import lombok.Data;

import java.util.List;

@Data
@SensitiveClass
public class SeparateAccountQueryDO {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户名称
     */
    private String accountName;


    /**
     * 子账号开通状态：HAVE_NOT_OPENED-未开通，IN_OPENNING-开通中，OPENED-已开通，OPENING_FAILURE-开通失败，UNDER_REVIEW-审核中，TO_BE_ACTIVATED-待激活
     */
    private String accountOpenStatus;

    /**
     * 结算卡绑定状态：NOT_BOUND-未绑卡，UNACTIVATED-未激活，ACTIVATED-已激活
     */
    private String settleCardStatus;

    /**
     * 已选择的ID
     */
    private List<Long> selectedIds;

    /**
     * 排除的ID
     */
    private List<Long> excludeIds;

    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String idNumber;

    /**
     * 证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照，99-统一社会信用代码
     */
    private String idType;
}
