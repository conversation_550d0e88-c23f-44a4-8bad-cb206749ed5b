package com.wosai.cua.brand.business.service.facade.impl.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.annotations.MaskClass;
import com.wosai.cua.brand.business.api.annotations.MaskMethod;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppOpenSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageSeparateAccountInfoRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageSeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppOpenSeparateAccountResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppSeparateAccountFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountBusinessV2;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountCardBusinessV2;
import com.wosai.cua.brand.business.service.domain.entity.QueryPage;
import com.wosai.cua.brand.business.service.module.separate.account.OpenSeparateAccountModule;
import com.wosai.cua.brand.business.service.module.separate.account.OpenSeparateAccountResultModule;
import com.wosai.cua.brand.business.service.module.separate.account.PageSeparateAccountQueryModule;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
@MaskClass
public class AppSeparateAccountFacadeImpl implements AppSeparateAccountFacade {

    private final SeparateAccountBusinessV2 separateAccountBusiness;

    private final SeparateAccountCardBusinessV2 separateAccountCardBusiness;

    private final BrandBusiness brandBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    @Autowired
    public AppSeparateAccountFacadeImpl(SeparateAccountBusinessV2 separateAccountBusiness, SeparateAccountCardBusinessV2 separateAccountCardBusiness, BrandBusiness brandBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness) {
        this.separateAccountBusiness = separateAccountBusiness;
        this.separateAccountCardBusiness = separateAccountCardBusiness;
        this.brandBusiness = brandBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
    }

    @Override
    @MaskMethod
    public PageSeparateAccountInfoResponseDTO pageSeparateAccountInfo(AppPageSeparateAccountInfoRequestDTO request) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = brandBusiness.getBrandInfoListByMerchantId(request.getTokenMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        request.setBrandId(brandSimpleInfoList.get(0).getBrandId());
        QueryPage<SeparateAccountModule> separateAccountModulePage = separateAccountBusiness.pageSeparateAccountInfo(new PageSeparateAccountQueryModule(request));
        return new PageSeparateAccountInfoResponseDTO(separateAccountModulePage.getTotal(), SeparateAccountModule.fromModuleList(separateAccountModulePage.getRecords()));
    }

    @Override
    public AppOpenSeparateAccountResponseDTO openSeparateAccount(AppOpenSeparateAccountDTO request) {
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(request.getBrandId(), false);
        if (brandDetailInfo == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        request.checkParams();
        request.checkSettlementCardParams(brandDetailInfo.getFundManagementCompanyCode().getFundManagementCompanyCode());
        if (Objects.nonNull(request.getSettlementCard()) && Objects.nonNull(request.getSettlementCard().getType()) && request.getSettlementCard().getType() == 1) {
            BankInfoResponseDTO bankInfoByCardNumber = brandMerchantBankAccountBusiness.getBankInfoByCardNumber(request.getSettlementCard().getCardNumber());
            if (Objects.nonNull(bankInfoByCardNumber)) {
                request.getSettlementCard().setOpeningNumber(bankInfoByCardNumber.getCnaps());
                request.getSettlementCard().setBankName(bankInfoByCardNumber.getBank());
                request.getSettlementCard().setBranchBank(bankInfoByCardNumber.getName());
            }
        }
        OpenSeparateAccountResultModule openSeparateAccountResultModule = separateAccountBusiness.openSeparateAccount(OpenSeparateAccountModule.fromDTO(request), request.getBrandId(), brandDetailInfo.getFundManagementCompanyCode());
        return AppOpenSeparateAccountResponseDTO.builder().result(openSeparateAccountResultModule.getResult()).failReason(openSeparateAccountResultModule.getFailReason()).accountNumber(openSeparateAccountResultModule.getAccountNumber()).build();
    }

    @Override
    public boolean deleteSeparateAccount(AppDeleteSeparateAccountCardDTO request) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = brandBusiness.getBrandInfoListByMerchantId(request.getTokenMerchantId(), false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        SeparateAccountModule accountInfoByAccountNumber = separateAccountBusiness.getSeparateAccountInfoByAccountNumber(brandSimpleInfoList.get(0).getBrandId(), request.getAccountNumber());
        if (accountInfoByAccountNumber == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }

        return separateAccountBusiness.deleteSeparateAccount(brandSimpleInfoList.get(0).getBrandId(), request.getAccountNumber());
    }
}
