package com.wosai.cua.brand.business.service.domain.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveMethod;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantBankCardMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantBankCardDO;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.bank.BankInfoBeanModule;
import com.wosai.upay.bank.info.api.dto.BankInfoResponse;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@SensitiveClass
@Slf4j
public class BankCardDomainServiceImpl implements BankCardDomainService {

    @Autowired
    private BrandMerchantBankCardMapper brandMerchantBankCardMapper;

    @Autowired
    private BankInfoService bankInfoService;

    @Autowired
    protected CryptHelper cryptHelper;

    @Override
    @SensitiveMethod
    public Long createBankCard(BankCardModule bankCardModule) {
        BrandMerchantBankCardDO brandMerchantBankCardDo = JSON.parseObject(JSON.toJSONString(bankCardModule), BrandMerchantBankCardDO.class);
        brandMerchantBankCardMapper.insert(brandMerchantBankCardDo);
        bankCardModule.setId(brandMerchantBankCardDo.getId());
        return brandMerchantBankCardDo.getId();
    }

    @Override
    @SensitiveMethod
    public BankCardModule getBankCardModuleById(String bankCardId) {
        BrandMerchantBankCardDO brandMerchantBankCard = brandMerchantBankCardMapper.getBrandMerchantBankCardByCardId(bankCardId);
        if (Objects.isNull(brandMerchantBankCard)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        return JSON.parseObject(JSON.toJSONString(brandMerchantBankCard), BankCardModule.class);
    }

    @Override
    @SensitiveMethod
    public int updateBankCard(BankCardModule bankCardModule) {
        BrandMerchantBankCardDO brandMerchantBankCardDo = JSON.parseObject(JSON.toJSONString(bankCardModule), BrandMerchantBankCardDO.class);
        brandMerchantBankCardDo.setUpdatedTime(new Date());
        int updateCount = brandMerchantBankCardMapper.updateById(brandMerchantBankCardDo);
        bankCardModule.setReservedMobileNumber(cryptHelper.decrypt(bankCardModule.getReservedMobileNumber()));
        return updateCount;
    }

    @Override
    public void updateAllBankCardIsNotDefault(String brandId, String merchantId) {
        brandMerchantBankCardMapper.updateAllBankCardIsNotDefault(brandId, merchantId);
    }

    @Override
    @SensitiveMethod
    public BankCardModule getDefaultBankCardModule(String brandId, String merchantId) {
        BrandMerchantBankCardDO defaultCard = brandMerchantBankCardMapper.getDefaultCard(brandId, merchantId);
        if (Objects.isNull(defaultCard)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(defaultCard), BankCardModule.class);
    }

    @Override
    @SensitiveMethod
    public List<BankCardModule> getDefaultBankCardModules(String brandId, List<String> merchantIds) {
        List<BrandMerchantBankCardDO> defaultCards = brandMerchantBankCardMapper.getDefaultCardsByMerchantIds(brandId, merchantIds);
        if (CollectionUtils.isEmpty(defaultCards)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(defaultCards), BankCardModule.class);
    }

    @Override
    @SensitiveMethod
    public List<BankCardModule> getBankCardModulesByCarIdList(String brandId, String merchantId, List<String> cardIds, Integer status, Integer isDefault) {
        List<BrandMerchantBankCardDO> modulesByCarIdList = brandMerchantBankCardMapper.getBankCardModulesByCarIdList(brandId, merchantId, cardIds, status, isDefault);
        if (CollectionUtils.isEmpty(modulesByCarIdList)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(modulesByCarIdList), BankCardModule.class);
    }

    @Override
    @SensitiveMethod
    public int batchCreateBankCards(List<BankCardModule> bankCardModules) {
        if (CollectionUtils.isEmpty(bankCardModules)) {
            return 0;
        }
        List<BrandMerchantBankCardDO> brandMerchantBankCards = JSON.parseArray(JSON.toJSONString(bankCardModules), BrandMerchantBankCardDO.class);
        brandMerchantBankCards.removeIf(brandMerchantBankCardDO -> StringUtils.isBlank(brandMerchantBankCardDO.getReservedMobileNumber()));
        return brandMerchantBankCardMapper.batchInsertBankCards(brandMerchantBankCards);
    }

    @Override
    public BankInfoBeanModule getBankInfoModuleByBankName(String bankName) {
        HashMap<String, String> bankInfoRequest = Maps.newHashMap();
        bankInfoRequest.put("branch_name", bankName);
        Map bankInfo = bankInfoService.getBankInfo(bankInfoRequest);
        if (MapUtils.isEmpty(bankInfo)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(bankInfo), BankInfoBeanModule.class);
    }

    @Override
    public BankInfoBeanModule getBankInfoModuleByOpeningNumber(String openingNumber) {
        BankInfoResponse bankInfoByOpenNumber = bankInfoService.getBankInfoByOpenNumber(openingNumber);
        if (Objects.nonNull(bankInfoByOpenNumber)) {
            return JSON.parseObject(JSON.toJSONString(bankInfoByOpenNumber), BankInfoBeanModule.class);
        }
        return null;
    }

    @Override
    public List<String> deleteBankCardByMerchantIds(String brandId, List<String> merchantIds) {
        LambdaQueryWrapper<BrandMerchantBankCardDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BrandMerchantBankCardDO::getBrandId, brandId);
        queryWrapper.in(BrandMerchantBankCardDO::getMerchantId, merchantIds);
        List<BrandMerchantBankCardDO> cardList = brandMerchantBankCardMapper.selectList(queryWrapper);
        LambdaUpdateWrapper<BrandMerchantBankCardDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(BrandMerchantBankCardDO::getBrandId, brandId);
        updateWrapper.in(BrandMerchantBankCardDO::getMerchantId, merchantIds);
        updateWrapper.set(BrandMerchantBankCardDO::getDeleted, 1);
        brandMerchantBankCardMapper.update(updateWrapper);
        return cardList.stream().map(BrandMerchantBankCardDO::getBankCardId).collect(Collectors.toList());
    }

    @Override
    @SensitiveMethod
    public List<BankCardModule> getBankCardListByBrandId(String brandId, Integer pageSize, Long startId) {
        List<BrandMerchantBankCardDO> bankCardListByBrandId = brandMerchantBankCardMapper.getBankCardListByBrandId(brandId, pageSize, startId);
        if (CollectionUtils.isEmpty(bankCardListByBrandId)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(JSON.toJSONString(bankCardListByBrandId), BankCardModule.class);
    }
}
