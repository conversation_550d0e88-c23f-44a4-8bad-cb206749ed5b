package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.BusinessOpenAddMerchantContext;
import com.wosai.cua.brand.business.service.excel.data.BusinessOpenAddMicroMerchantOpData;
import com.wosai.cua.brand.business.service.excel.data.BusinessOpenAddNoMicroMerchantOpData;
import com.wosai.cua.brand.business.service.excel.model.BusinessOpenBasicModel;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.BankInfoClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.StoreExtAndPicturesQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.MerchantUserClient;
import com.wosai.cua.brand.business.service.externalservice.paybusinessopen.PayBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model.LadderFeeRateModel;
import com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model.PayComboModel;
import com.wosai.cua.brand.business.service.externalservice.paybusinessopen.model.TradeComboDetailModel;
import com.wosai.cua.brand.business.service.externalservice.salespoi.SalesPoiClient;
import com.wosai.cua.brand.business.service.externalservice.salessystem.SalesSystemClient;
import com.wosai.cua.brand.business.service.externalservice.salessystem.model.SalesUserModel;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.data.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/22
 */
@Slf4j
public abstract class AbstractAddMerchantHandler<C extends BusinessOpenAddMerchantContext, T> extends AbstractImportExcelHandler<C, T> {
    @Autowired
    protected BrandDomainService brandDomainService;
    @Autowired
    protected SalesPoiClient salesPoiClient;
    @Autowired
    protected BankInfoClient bankInfoClient;
    @Autowired
    protected MerchantUserClient merchantUserClient;
    @Autowired
    protected MerchantCenterClient merchantCenterClient;
    @Autowired
    protected SalesSystemClient salesSystemClient;
    @Autowired
    protected PayBusinessOpenClient payBusinessOpenClient;
    @Autowired
    protected MerchantContractJobClient merchantContractJobClient;
    @Autowired
    protected MerchantBusinessOpenClient merchantBusinessOpenClient;
    @Qualifier("redisClusterTemplate")
    @Autowired
    protected StringRedisTemplate redisClusterTemplate;
    @Value("${appid.indirect}")
    protected String indirectAppId;
    @Value("${appid.paymentHub}")
    protected String paymentHubAppId;
    @Value("${appid.brandPaymentHub}")
    protected String brandPaymentHubAppId;

    @Override
    public void preCheck(BusinessOpenAddMerchantContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (WosaiStringUtils.isEmpty(brandModule.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_FIND);
        }
        context.bindBrandModule(brandModule);
        // 业务校验，是否主商户是否开通间连扫码或者是收付通
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        List<AppInfoOpenResult> appInfoOpenResults = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId());
        if (Objects.equals(true, context.getBrandBatchAddMerchantsDTO().getOpenIndirect())) {
            Optional<AppInfoOpenResult> brandIndirectOpenResult = appInfoOpenResults.stream().filter(r -> indirectAppId.equals(r.getAppId()) && r.getStatus().equals(AppInfoStatusEnum.SUCCESS)).findFirst();
            if (!brandIndirectOpenResult.isPresent()) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_INDIRECT);
            }
        }
        if (Objects.equals(true, context.getBrandBatchAddMerchantsDTO().getOpenPaymentHub())) {
            Optional<AppInfoOpenResult> brandPaymentHubOpenResult = appInfoOpenResults.stream().filter(r -> brandPaymentHubAppId.equals(r.getAppId()) && r.getStatus().equals(AppInfoStatusEnum.SUCCESS)).findFirst();
            if (!brandPaymentHubOpenResult.isPresent()) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_PAYMENT_HUB);
            }
        }
        if (Objects.equals(true, context.getBrandBatchAddMerchantsDTO().getOpenIndirect())) {
            String acquirer = merchantContractJobClient.queryMerchantAcquirer(brandModule.getMerchantSn());
            if (!AcquirerTypeEnum.LKL.getValue().equals(acquirer) && !AcquirerTypeEnum.HAI_KE.getValue().equals(acquirer)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_ACQUIRER);
            }
        }
        StoreExtAndPicturesQueryResult latestStoreExtAndPicturesByMerchantId = merchantCenterClient.findLatestStoreExtAndPicturesByMerchantId(merchantInfoQueryResult.getMerchantId());
        if (Objects.isNull(latestStoreExtAndPicturesByMerchantId.getBrandPhoto()) || Objects.isNull(latestStoreExtAndPicturesByMerchantId.getIndoorMaterialPhoto()) || Objects.isNull(latestStoreExtAndPicturesByMerchantId.getOutdoorMaterialPhoto())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MAIN_MERCHANT_PICTURES);
        }
        context.bindStoreExtPicturesQueryResult(latestStoreExtAndPicturesByMerchantId);

        SalesUserModel salesUser = salesSystemClient.queryUser(context.getKeeperUserId());
        context.bindOperator(salesUser);

        Boolean laterSupplyPhoto = context.getBrandBatchAddMerchantsDTO().getLaterSupplyPhoto();
        if (Objects.equals(false, laterSupplyPhoto) && WosaiStringUtils.isEmpty(context.getBrandBatchAddMerchantsDTO().getPhotoZip())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_PHOTO_FILE);
        }
    }

    @Override
    protected void doHandleData(BusinessOpenAddMerchantContext context, BrandSubTaskModule brandSubTaskModule) {
        File photoFile = context.initPhotoFile();
        T data = JSON.parseObject(brandSubTaskModule.getSubTaskContext(), getDataClass());
        try {
            doOpenBusiness(data, context, photoFile);
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", brandSubTaskModule.getId(), e);
            // 将异常信息写入data对象，如果后续扩展，需要修改此处
            if (data instanceof BusinessOpenAddMicroMerchantOpData) {
                ((BusinessOpenAddMicroMerchantOpData) data).setIndirectResult("失败:" + e.getMessage());
            }
            if (data instanceof BusinessOpenAddNoMicroMerchantOpData) {
                ((BusinessOpenAddNoMicroMerchantOpData) data).setIndirectResult("失败:" + e.getMessage());
            }
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败:" + e.getMessage())));
        }
    }

    protected Map<String, Object> assembleMerchantConfig(BusinessOpenAddMerchantContext context, BusinessOpenBasicModel basicModel) {
        Map<String, Object> appInfo = new HashMap<>();
        List<PayComboModel> combos = payBusinessOpenClient.queryPayCombos(context.getSalesUser().getOrganizationId(), WosaiMapUtils.getString(basicModel.getMerchant(), "industry"));
        if (WosaiCollectionUtils.isEmpty(combos)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.TRADE_COMBO_NOT_FIND);
        }
        appInfo.put("trade_combo_id", combos.get(0).getId());
        List<TradeComboDetailModel> records = combos.get(0).getRecords();
        List<Map<String, Object>> merchantConfig = new ArrayList<>();
        for (TradeComboDetailModel record : records) {
            if (WosaiCollectionUtils.isNotEmpty(record.getLadderFeeRates())) {
                merchantConfig.add(CollectionUtil.hashMap("payway", record.getPayway(), "ladder_fee_rates", getLadderFeeRates(record.getLadderFeeRates()), "status", 1));
            } else {
                merchantConfig.add(CollectionUtil.hashMap("payway", record.getPayway(), "rate", getFeeRate(record), "status", 1));
            }
        }
        appInfo.put("merchant_config", merchantConfig);
        return appInfo;
    }

    private List<Map> getLadderFeeRates(List<LadderFeeRateModel> ladderFeeRateModels) {
        List<Map> ladderFeeRates = new ArrayList<>();
        for (LadderFeeRateModel ladderFeeRateModel : ladderFeeRateModels) {
            if (WosaiStringUtils.isNotEmpty(ladderFeeRateModel.getFeeRateDefaultValue())) {
                ladderFeeRates.add(CollectionUtil.hashMap("min", ladderFeeRateModel.getMin(), "max", ladderFeeRateModel.getMax(), "rate", ladderFeeRateModel.getFeeRateDefaultValue()));
            } else {
                if (Objects.equals(ladderFeeRateModel.getFeeRateMin(), ladderFeeRateModel.getFeeRateMax())) {
                    ladderFeeRates.add(CollectionUtil.hashMap("min", ladderFeeRateModel.getMin(), "max", ladderFeeRateModel.getMax(), "rate", ladderFeeRateModel.getFeeRateMin()));
                } else if (Objects.equals(ladderFeeRateModel.getFeeRateMinValue(), ladderFeeRateModel.getFeeRateMaxValue())) {
                    ladderFeeRates.add(CollectionUtil.hashMap("min", ladderFeeRateModel.getMin(), "max", ladderFeeRateModel.getMax(), "rate", ladderFeeRateModel.getFeeRateMinValue()));
                } else {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.TRADE_COMBO_NOT_FIND);
                }
            }
        }
        return ladderFeeRates;
    }

    private String getFeeRate(TradeComboDetailModel record) {
        if (WosaiStringUtils.isNotEmpty(record.getFeeRateDefaultValue())) {
            return record.getFeeRateDefaultValue();
        } else {
            if (Objects.equals(record.getFeeRateMin(), record.getFeeRateMax())) {
                return record.getFeeRateMin();
            } else if (Objects.equals(record.getFeeRateMinValue(), record.getFeeRateMaxValue())) {
                return record.getFeeRateMinValue();
            }
            throw new BrandBusinessException(BrandBusinessExceptionEnum.TRADE_COMBO_NOT_FIND);
        }
    }

    @Override
    public int getLimitQuantity(BusinessOpenAddMerchantContext context) {
        // 如果有附件的话，限制 100条
        if (WosaiStringUtils.isNotEmpty(context.getBrandBatchAddMerchantsDTO().getPhotoZip())) {
            return 100;
        }
        return 2000;
    }

    /**
     * 对数据做真正的处理
     *
     * @param data      数据
     * @param context   上下文
     * @param photoFile 照片数据
     */
    protected abstract void doOpenBusiness(T data, BusinessOpenAddMerchantContext context, File photoFile);

}
