package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

/**
 * 品牌导入excel的sheet页枚举
 * <AUTHOR>
 */

@Getter
public enum BrandImportSheetEnum {
    /**
     * 个人、小微商户
     */
    PERSONAL(0,19),
    /**
     * 个体工商户
     */
    INDIVIDUAL_BUSINESS(1,24),
    /**
     * 企业
     */
    COMPANY(2,26),
    /**
     * 收钱吧商户
     */
    ALREADY_EXIST_MERCHANT(3,12),
    /**
     * 归集商户
     */
    POOLED_MERCHANT(0,4),
    ;

    private final int sheet;

    private final int errorMsgColNum;

    BrandImportSheetEnum(int sheet, int errorMsgColNum) {
        this.sheet = sheet;
        this.errorMsgColNum = errorMsgColNum;
    }

}
