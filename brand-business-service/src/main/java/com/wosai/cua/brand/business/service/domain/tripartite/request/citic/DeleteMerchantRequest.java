package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeleteMerchantRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {

    /**
     * 用户编号
     * 是否必填：是
     * 银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    @XmlElement(name = "USER_ID")
    private String userId;

    @XmlElement(name = "USER_NM")
    private String userName;

    @XmlElement(name = "OPERATE_TYPE")
    private String operateType;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_DELETE_MERCHANT;
    }

    public DeleteMerchantRequest() {
        super();
        super.setTransCode("********");
    }

    public static DeleteMerchantRequest builder(CiticBankConfigModule configModule, BrandMerchantModule brandMerchantModule){
        DeleteMerchantRequest request = new DeleteMerchantRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(brandMerchantModule.getMemberId());
        request.setUserName(brandMerchantModule.getMerchantName());
        request.setUserId(brandMerchantModule.getMemberId());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + brandMerchantModule.getMerchantSn().substring(brandMerchantModule.getMerchantSn().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        return request;
    }

    public static DeleteMerchantRequest builder(CiticBankConfigModule configModule, SeparateAccountDO separateAccountDO){
        DeleteMerchantRequest request = new DeleteMerchantRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(separateAccountDO.getSubAccountNo());
        request.setUserName(separateAccountDO.getAccountName());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + separateAccountDO.getAccountNumber().substring(separateAccountDO.getAccountNumber().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        return request;
    }
}
