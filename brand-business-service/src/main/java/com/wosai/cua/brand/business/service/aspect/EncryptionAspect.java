package com.wosai.cua.brand.business.service.aspect;

import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.annotations.MaskField;
import com.wosai.cua.brand.business.api.annotations.MaskMethod;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.annotations.SensitiveMethod;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class EncryptionAspect {

    @Autowired
    private CryptHelper cryptHelper;

    private static final Map<String, Consumer<Object>> HANDLE_DECODE_FUNCTIONS_MAP = Maps.newConcurrentMap();
    private static final Map<String, Consumer<Object>> HANDLE_HIDDEN_FUNCTIONS_MAP = Maps.newConcurrentMap();

    private static final Map<String, Consumer<Object>> HANDLE_ENCRYPT_FUNCTIONS_MAP = Maps.newConcurrentMap();

    private static final String KEY_LIST = "LIST";
    private static final String KEY_OBJECT = "OBJECT";

    @PostConstruct
    public void initFunctions() {
        HANDLE_DECODE_FUNCTIONS_MAP.put(KEY_LIST, this::handleDecodeList);
        HANDLE_DECODE_FUNCTIONS_MAP.put(KEY_OBJECT, this::handleDecodeObjField);
        HANDLE_ENCRYPT_FUNCTIONS_MAP.put(KEY_LIST, this::handleEncryptList);
        HANDLE_ENCRYPT_FUNCTIONS_MAP.put(KEY_OBJECT, this::handleEncryptObjField);
        HANDLE_HIDDEN_FUNCTIONS_MAP.put(KEY_LIST, this::handleHiddenList);
        HANDLE_HIDDEN_FUNCTIONS_MAP.put(KEY_OBJECT, this::handleHiddenObjField);
    }

    @Before("execution(* com.wosai.cua.brand.business.service.domain.service.*.*(..)) " +
            "&& @within(com.wosai.cua.brand.business.service.annotations.SensitiveClass)")
    public void encryptField(JoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        if (!method.isAnnotationPresent(SensitiveMethod.class)) {
            return;
        }
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (Objects.nonNull(arg) && arg.getClass().isAnnotationPresent(SensitiveClass.class)) {
                this.handleEncryptObj(arg);
            }
        }
    }

    @AfterReturning(pointcut = "execution(* com.wosai.cua.brand.business.service.domain.service.*.*(..)) " +
            "&& @within(com.wosai.cua.brand.business.service.annotations.SensitiveClass)", returning = "result")
    public void decodeField(JoinPoint joinPoint, Object result) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        if (!method.isAnnotationPresent(SensitiveMethod.class)) {
            return;
        }
        this.handleDecodeObj(result);
    }

    private void handleEncryptObj(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }
        if (Collection.class.isAssignableFrom(obj.getClass())) {
            HANDLE_ENCRYPT_FUNCTIONS_MAP.get(KEY_LIST).accept(obj);
        } else if (Map.class.isAssignableFrom(obj.getClass())) {
            // 是map
        } else {
            HANDLE_ENCRYPT_FUNCTIONS_MAP.get(KEY_OBJECT).accept(obj);
        }
    }

    private void handleEncryptList(Object object) {
        if (Objects.isNull(object)) {
            return;
        }
        List<Object> collections = (List<Object>) object;
        collections.forEach(this::handleEncryptObj);
    }

    private void handleEncryptObjField(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }
        Class<?> aClass = obj.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(SensitiveField.class)) {
                try {
                    field.setAccessible(true);
                    if (Collection.class.isAssignableFrom(field.getDeclaringClass()) || Map.class.isAssignableFrom(aClass)) {
                        this.handleEncryptObj(field.get(obj));
                    }
                    if (field.getType().isAssignableFrom(String.class)) {
                        Object value = field.get(obj);
                        if (Objects.isNull(value)) {
                            return;
                        }
                        field.set(obj, cryptHelper.encrypt(String.valueOf(value)));
                    }
                } catch (IllegalAccessException e) {
                    log.warn("com.wosai.cua.brand.business.service.aspect.EncryptionAspect.handleEncryptObjField has IllegalAccessException", e);
                }
            }
        }
    }

    private void handleDecodeObj(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }
        if (Collection.class.isAssignableFrom(obj.getClass())) {
            HANDLE_DECODE_FUNCTIONS_MAP.get(KEY_LIST).accept(obj);
        } else if (Map.class.isAssignableFrom(obj.getClass())) {
            // 是map
        } else {
            HANDLE_DECODE_FUNCTIONS_MAP.get(KEY_OBJECT).accept(obj);
        }
    }

    private void handleDecodeList(Object object) {
        if (Objects.isNull(object)) {
            return;
        }
        List<Object> collections = (List<Object>) object;
        collections.forEach(this::handleDecodeObj);
    }

    private void handleDecodeObjField(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }
        Class<?> aClass = obj.getClass();
        Field[] fields = aClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(SensitiveField.class)) {
                try {
                    field.setAccessible(true);
                    if (Collection.class.isAssignableFrom(field.getType()) || Map.class.isAssignableFrom(field.getType())) {
                        this.handleDecodeObj(field.get(obj));
                    }
                    if (field.getType().isAssignableFrom(String.class)) {
                        Object value = field.get(obj);
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        field.set(obj, cryptHelper.decrypt(String.valueOf(value)));
                    }
                } catch (IllegalAccessException e) {
                    log.warn("com.wosai.cua.brand.business.service.aspect.EncryptionAspect.handleDecodeObjField has IllegalAccessException", e);
                }
            }
        }
    }

    @AfterReturning(pointcut = "execution(* com.wosai.cua.brand.business.service.facade.impl.app..*.*(..)) " +
            "&& @within(com.wosai.cua.brand.business.api.annotations.MaskClass)", returning = "result")
    public void hiddenSensitiveInfo(JoinPoint joinPoint, Object result) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        if (!method.isAnnotationPresent(MaskMethod.class)) {
            return;
        }
        this.handleHiddenSensitiveInfo(result);
    }

    private void handleHiddenSensitiveInfo(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }
        if (Collection.class.isAssignableFrom(obj.getClass())) {
            HANDLE_HIDDEN_FUNCTIONS_MAP.get(KEY_LIST).accept(obj);
        } else if (Map.class.isAssignableFrom(obj.getClass())) {
            // 是map
        } else {
            HANDLE_HIDDEN_FUNCTIONS_MAP.get(KEY_OBJECT).accept(obj);
        }
    }

    private void handleHiddenList(Object object) {
        if (Objects.isNull(object)) {
            return;
        }
        List<Object> collections = (List<Object>) object;
        collections.forEach(this::handleHiddenObjField);
    }

    private void handleHiddenObjField(Object obj) {
        if (Objects.isNull(obj)) {
            return;
        }

        Class<?> clazz = obj.getClass();

        // 排除常见不可再深入类型
        if (obj instanceof String ||
                obj instanceof Number ||
                obj instanceof Boolean ||
                obj.getClass().isPrimitive() ||
                obj.getClass().getName().startsWith("java.")) {
            return;
        }

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);

                if (value == null) {
                    continue;
                }

                if (Collection.class.isAssignableFrom(field.getType()) || Map.class.isAssignableFrom(field.getType())) {
                    this.handleHiddenSensitiveInfo(value);
                } else if (field.getType().isAssignableFrom(String.class) && field.isAnnotationPresent(MaskField.class)) {
                    field.set(obj, CryptHelper.mask((String) value));
                } else if (!(value instanceof String) &&
                        !(value instanceof Number) &&
                        !(value instanceof Boolean) && !(value instanceof Enum) &&
                        !value.getClass().getName().startsWith("java.")) {
                    // 只对自定义对象递归处理
                    this.handleHiddenSensitiveInfo(value);
                }
            } catch (IllegalAccessException e) {
                log.warn("处理脱敏时发生异常", e);
            }
        }
    }

}
