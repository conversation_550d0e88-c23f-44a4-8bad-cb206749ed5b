package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;

/**
* <AUTHOR>
* @description 针对表【separate_account_settlement_card(分账账号结算卡信息表)】的数据库操作Mapper
* @createDate 2025-06-05 10:18:07
* @Entity com.wosai.cua.brand.business.service.domain.entity.AccountAllocationSettlementCardDO
*/
public interface SeparateAccountSettlementCardDOMapper extends BaseMapper<SeparateAccountSettlementCardDO> {

}




