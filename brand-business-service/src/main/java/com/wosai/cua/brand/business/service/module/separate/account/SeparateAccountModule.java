package com.wosai.cua.brand.business.service.module.separate.account;

import com.wosai.cua.brand.business.api.dto.response.SeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountIdTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class SeparateAccountModule {

    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 子账号类型：FRANCHISE-加盟、BRAND_OWNED-品牌自有、BRAND_SELF_OPERATED-品牌自营、SUPPLIER-供应
     */
    private String accountType;

    /**
     * 类型：PERSONAL-个人/小微、INDIVIDUAL_BUSINESS-个体工商户、COMPANY-企业
     */
    private String type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照，99-统一社会信用代码
     *
     * @see com.wosai.cua.brand.business.api.enums.SeparateAccountIdTypeEnum
     */
    private String idType;

    /**
     * 证件类型描述
     */
    private String idTypeDesc;

    /**
     * 证件编号
     */
    private String idNumber;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照
     *
     * @see com.wosai.cua.brand.business.api.enums.SeparateAccountIdTypeEnum
     */
    private String legalPersonIdType;

    /**
     * 法人证件类型描述
     */
    private String legalPersonIdTypeDesc;

    /**
     * 法人证件编号
     */
    private String legalPersonId;

    /**
     * 联系人姓名
     */
    private String contractName;

    /**
     * 联系人手机号
     */
    private String contractPhone;

    /**
     * 资管子账号
     */
    private String subAccountNo;

    /**
     * 第三方会员ID
     */
    private String memberId;

    /**
     * 子账号开通状态：HAVE_NOT_OPENED-未开通，IN_OPENNING-开通中，OPENED-已开通，OPENING_FAILURE-开通失败，UNDER_REVIEW-审核中，TO_BE_ACTIVATED-待激活
     */
    private String accountOpenStatus;

    /**
     * 子账号开通状态描述
     */
    private String accountOpenStatusDesc;

    /**
     * 子账号开通失败原因
     */
    private String accountOpenFailureReason;

    /**
     * 子账号开通时间
     */
    private Date accountOpenedTime;

    /**
     * 结算卡绑定状态：NOT_BOUND-未绑卡，UNACTIVATED-未激活，ACTIVATED-已激活
     */
    private String settleCardStatus;

    /**
     * 结算卡绑定状态描述
     */
    private String settleCardStatusDesc;

    /**
     * 扩展字段
     */
    private Object ext;

    /**
     * 结算策略ID
     */
    private Long strategyId;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 关联商户编号
     */
    private List<String> relatedMerchantSnList;

    /**
     * 关联门店编号
     */
    private List<String> relatedStoreSnList;

    /**
     * 关联的收单门店id
     */
    private List<String> relatedStoreIdList;

    /**
     * 关联的外部商户号
     */
    private List<String> relatedOutMerchantNoList;

    /**
     * 关联的美团门店id
     */
    private List<String> relatedMeiTuanStoreIdList;

    /**
     * 关联的饿了么门店id
     */
    private List<String> relatedElmStoreIdList;

    /**
     * 关联的抖音门店id
     */
    private List<String> relatedDyStoreIdList;

    /**
     * 绑定银行卡号
     */
    private String bindBankCardNumber;

    /**
     * 绑定银行账号名称
     */
    private String bindBankAccountName;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 账户类型:  1-对私 2-对公
     */
    private Integer bankAccountType;

    /**
     * 账户类型描述
     */
    private String bankAccountTypeDesc;

    /**
     * 绑定银行卡id
     */
    private String thirdBankCardId;

    /**
     * 激活链接
     */
    private String activationUrl;

    /**
     * 激活短链接
     */
    private String activationShortUrl;

    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    public static List<SeparateAccountModule> fromDOList(List<SeparateAccountDO> separateAccountDOList, Map<String, List<SeparateAccountRelatedDO>> separateAccountRelatedMap,Map<String, SeparateAccountSettlementCardDO> separateAccountSettlementCardMap) {
        return separateAccountDOList.stream().map(separateAccountDO -> SeparateAccountModule.fromDO(separateAccountDO, separateAccountRelatedMap.get(separateAccountDO.getAccountNumber()),  separateAccountSettlementCardMap.get(separateAccountDO.getAccountNumber()))).collect(Collectors.toList());
    }

    public static SeparateAccountModule fromDO(SeparateAccountDO separateAccountDO, List<SeparateAccountRelatedDO> separateAccountRelatedDOList,SeparateAccountSettlementCardDO defaultSettlementCardDO) {
        if (Objects.isNull(separateAccountDO)) {
            return null;
        }
        SeparateAccountModule separateAccountModule = new SeparateAccountModule();
        separateAccountModule.setId(separateAccountDO.getId());
        separateAccountModule.setBrandId(separateAccountDO.getBrandId());
        separateAccountModule.setAccountNumber(separateAccountDO.getAccountNumber());
        separateAccountModule.setAccountName(separateAccountDO.getAccountName());
        separateAccountModule.setType(separateAccountDO.getType());
        separateAccountModule.setTypeDesc(BrandMerchantTypeEnum.getMerchantTypeDescByType(separateAccountDO.getType()));
        separateAccountModule.setIdType(separateAccountDO.getIdType());
        separateAccountModule.setIdTypeDesc(SeparateAccountIdTypeEnum.getDescByCode(separateAccountDO.getIdType()));
        separateAccountModule.setIdNumber(separateAccountDO.getIdNumber());
        separateAccountModule.setLegalPersonName(separateAccountDO.getLegalPersonName());
        separateAccountModule.setLegalPersonIdType(separateAccountDO.getLegalPersonIdType());
        separateAccountModule.setLegalPersonIdTypeDesc(SeparateAccountIdTypeEnum.getDescByCode(separateAccountDO.getLegalPersonIdType()));
        separateAccountModule.setLegalPersonId(separateAccountDO.getLegalPersonId());
        separateAccountModule.setContractName(separateAccountDO.getContractName());
        separateAccountModule.setContractPhone(separateAccountDO.getContractPhone());
        separateAccountModule.setAccountOpenStatus(separateAccountDO.getAccountOpenStatus());
        separateAccountModule.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(separateAccountDO.getAccountOpenStatus()));
        separateAccountModule.setAccountOpenFailureReason(separateAccountDO.getAccountOpenFailureReason());
        separateAccountModule.setAccountOpenedTime(separateAccountDO.getAccountOpenedTime());
        separateAccountModule.setSettleCardStatus(separateAccountDO.getSettleCardStatus());
        separateAccountModule.setSettleCardStatusDesc(BankCardActivateStatusEnum.getDescByActivateStatus(separateAccountDO.getSettleCardStatus()));
        separateAccountModule.setExt(separateAccountDO.getExt());
        separateAccountModule.setStrategyId(separateAccountDO.getStrategyId());
        separateAccountModule.setCtime(separateAccountDO.getCtime());
        separateAccountModule.setMtime(separateAccountDO.getMtime());
        separateAccountModule.setSubAccountNo(separateAccountDO.getSubAccountNo());
        separateAccountModule.setMemberId(separateAccountDO.getMemberId());
        separateAccountModule.setAccountType(separateAccountDO.getAccountType());
        if (CollectionUtils.isNotEmpty(separateAccountRelatedDOList)) {
            separateAccountModule.setRelatedMerchantSnList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.MERCHANT_SN.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedStoreSnList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.STORE_SN.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedStoreSnList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.STORE_ID.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedDyStoreIdList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.DY_STORE.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedElmStoreIdList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.ELM_STORE.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedMeiTuanStoreIdList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.MT_STORE.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
            separateAccountModule.setRelatedOutMerchantNoList(
                    separateAccountRelatedDOList.stream()
                            .filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.OUT_MERCHANT.getType()))
                            .map(SeparateAccountRelatedDO::getRelatedSn)
                            .collect(Collectors.toList())
            );
        }
        if (Objects.nonNull(defaultSettlementCardDO)){
            separateAccountModule.setBindBankCardNumber(defaultSettlementCardDO.getCardNumber());
            separateAccountModule.setBindBankAccountName(defaultSettlementCardDO.getHolder());
            separateAccountModule.setBankName(defaultSettlementCardDO.getBankName());
            separateAccountModule.setBankAccountType(defaultSettlementCardDO.getType());
            separateAccountModule.setThirdBankCardId(defaultSettlementCardDO.getThirdBankCardId());
        }
        return separateAccountModule;
    }

    public static SeparateAccountInfoResponseDTO fromModule(SeparateAccountModule separateAccountModule)
    {
        if (Objects.isNull(separateAccountModule)){
            return null;
        }
        SeparateAccountInfoResponseDTO separateAccountInfoResponseDTO = new SeparateAccountInfoResponseDTO();
        separateAccountInfoResponseDTO.setId(separateAccountModule.getId());
        separateAccountInfoResponseDTO.setAccountNumber(separateAccountModule.getAccountNumber());
        separateAccountInfoResponseDTO.setAccountName(separateAccountModule.getAccountName());
        separateAccountInfoResponseDTO.setAccountType(SeparateAccountTypeEnum.getSeparateAccountTypeEnumByCode(separateAccountModule.getAccountType()));
        separateAccountInfoResponseDTO.setType(separateAccountModule.getType());
        separateAccountInfoResponseDTO.setAccountStatus(separateAccountModule.getAccountOpenStatus());
        separateAccountInfoResponseDTO.setAccountStatusDesc(separateAccountModule.getAccountOpenStatusDesc());
        separateAccountInfoResponseDTO.setBindCardStatus(separateAccountModule.getSettleCardStatus());
        separateAccountInfoResponseDTO.setBindCardStatusDesc(separateAccountModule.getSettleCardStatusDesc());
        separateAccountInfoResponseDTO.setContactPhone(separateAccountModule.getContractPhone());
        separateAccountInfoResponseDTO.setRelatedMerchantSnList(separateAccountModule.getRelatedMerchantSnList());
        separateAccountInfoResponseDTO.setRelatedStoreSnList(separateAccountModule.getRelatedStoreSnList());
        separateAccountInfoResponseDTO.setRelatedOutMerchantNoList(separateAccountModule.getRelatedOutMerchantNoList());
        separateAccountInfoResponseDTO.setRelatedMeiTuanStoreIdList(separateAccountModule.getRelatedMeiTuanStoreIdList());
        separateAccountInfoResponseDTO.setRelatedElmStoreIdList(separateAccountModule.getRelatedElmStoreIdList());
        separateAccountInfoResponseDTO.setRelatedDyStoreIdList(separateAccountModule.getRelatedDyStoreIdList());
        separateAccountInfoResponseDTO.setSubAccountNo(separateAccountModule.getSubAccountNo());
        separateAccountInfoResponseDTO.setMemberId(separateAccountModule.getMemberId());
        separateAccountInfoResponseDTO.setStrategyId(separateAccountModule.getStrategyId());
        separateAccountInfoResponseDTO.setBindBankCardNumber(separateAccountModule.getBindBankCardNumber());
        separateAccountInfoResponseDTO.setBindBankAccountName(separateAccountModule.getBindBankAccountName());
        separateAccountInfoResponseDTO.setBankName(separateAccountModule.getBankName());
        separateAccountInfoResponseDTO.setBankAccountType(separateAccountModule.getBankAccountType());
        separateAccountInfoResponseDTO.setBankAccountTypeDesc(BankAccountTypeEnum.getDescByAccountType(separateAccountModule.getBankAccountType()));
        separateAccountInfoResponseDTO.setActivationUrl(separateAccountModule.getActivationUrl());
        separateAccountInfoResponseDTO.setActivationShortUrl(separateAccountModule.getActivationShortUrl());
        separateAccountInfoResponseDTO.setContactName(separateAccountModule.getContractName());
        separateAccountInfoResponseDTO.setLegalPersonName(separateAccountModule.getLegalPersonName());
        separateAccountInfoResponseDTO.setBrandId(separateAccountModule.getBrandId());
        separateAccountInfoResponseDTO.setBankIcon(separateAccountModule.getBankIcon());
        separateAccountInfoResponseDTO.setBankBackPicture(separateAccountModule.getBankBackPicture());
        separateAccountInfoResponseDTO.setThirdBankCardId(separateAccountModule.getThirdBankCardId());
        return separateAccountInfoResponseDTO;
    }

    public static List<SeparateAccountInfoResponseDTO> fromModuleList(List<SeparateAccountModule> separateAccountModuleList){
        return separateAccountModuleList.stream().map(SeparateAccountModule::fromModule).collect(Collectors.toList());
    }
}
