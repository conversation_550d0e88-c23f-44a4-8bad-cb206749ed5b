package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandBatchAssociateMerchantsDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.enums.SftFlagEnum;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.BusinessOpenAssociateMerchantContext;
import com.wosai.cua.brand.business.service.excel.data.BusinessOpenAssociateMerchantOpData;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.externalservice.coreb.CoreBusinessClient;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantFindRequest;
import com.wosai.cua.brand.business.service.externalservice.coreb.model.MerchantSimpleQueryResult;
import com.wosai.cua.brand.business.service.externalservice.marketingprepaid.MarketingPrepaidClient;
import com.wosai.cua.brand.business.service.externalservice.marketingprepaid.model.AssociateBrandBeforeCheckRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.MerchantBusinessOpenClient;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@Slf4j
@Component
public class BusinessOpenAssociateMerchantHandler extends AbstractImportExcelHandler<BusinessOpenAssociateMerchantContext, BusinessOpenAssociateMerchantOpData> {

    @Autowired
    private BrandDomainService brandDomainService;
    @Autowired
    private MerchantCenterClient merchantCenterClient;
    @Autowired
    private CoreBusinessClient coreBusinessClient;
    @Autowired
    private MarketingPrepaidClient marketingPrepaidClient;
    @Autowired
    private MerchantBusinessOpenClient merchantBusinessOpenClient;
    @Autowired
    private MerchantContractJobClient merchantContractJobClient;
    @Value("${appid.paymentHub}")
    private String paymentHubAppId;
    @Value("${appid.brandPaymentHub}")
    private String brandPaymentHubAppId;


    @Override
    public BusinessOpenAssociateMerchantContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return BusinessOpenAssociateMerchantContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.BUSINESS_OPEN_ASSOCIATE_MERCHANT;
    }

    @Override
    public void preCheck(BusinessOpenAssociateMerchantContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (WosaiStringUtils.isEmpty(brandModule.getMerchantSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_FIND);
        }
        MerchantInfoQueryResult merchantInfoQueryResult = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(brandModule.getMerchantSn()));
        context.bindMainMerchantId(merchantInfoQueryResult.getMerchantId());

        List<AppInfoOpenResult> appInfoOpenResults = merchantBusinessOpenClient.queryAppInfoOpenResultByMerchantId(merchantInfoQueryResult.getMerchantId());
        Optional<AppInfoOpenResult> brandPaymentHubOpenResult = appInfoOpenResults.stream().filter(r -> brandPaymentHubAppId.equals(r.getAppId()) && r.getStatus().equals(AppInfoStatusEnum.SUCCESS)).findFirst();
        if (Objects.equals(true, context.getBrandBatchAssociateMerchantsDTO().getOpenPaymentHub()) && !brandPaymentHubOpenResult.isPresent()) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_MAIN_MERCHANT_NOT_OPEN_PAYMENT_HUB);
        }
        context.bindBrandModule(brandModule);
    }

    @Override
    protected void preImport(BusinessOpenAssociateMerchantContext context, List<BrandSubTaskModule> brandSubTaskModules) {
        List<String> subMerchantSns = brandSubTaskModules.stream().map(r -> JSON.parseObject(r.getSubTaskContext(), BusinessOpenAssociateMerchantOpData.class).getMerchantSn()).collect(Collectors.toList());
        List<MerchantSimpleQueryResult> simpleMerchants = coreBusinessClient.findSimpleMerchants(new MerchantFindRequest().setMerchantSns(subMerchantSns));
        Map<String, String> marketingAssociateCheckResult = marketingPrepaidClient.checkBeforeAssociateBrand(AssociateBrandBeforeCheckRequest.builder().mainMerchantId(context.getMainMerchantId()).subMerchantIds(simpleMerchants.stream().map(MerchantSimpleQueryResult::getMerchantId).collect(Collectors.toList())).build());
        context.bindMarketAssociateCheckResult(marketingAssociateCheckResult);
    }

    @Override
    protected void doHandleData(BusinessOpenAssociateMerchantContext context, BrandSubTaskModule subTaskModule) {
        String brandId = context.getBrandModule().getBrandId();
        String parentId = context.getBrandModule().getParentId();
        BrandBatchAssociateMerchantsDTO brandBatchAssociateMerchantsDTO = context.getBrandBatchAssociateMerchantsDTO();
        BusinessOpenAssociateMerchantOpData data = JSON.parseObject(subTaskModule.getSubTaskContext(), BusinessOpenAssociateMerchantOpData.class);
        try {
            doProcessBrandMerchantAssociation(context, brandId, parentId, brandBatchAssociateMerchantsDTO, data);
            subTaskModule.setSubTaskContext(JSON.toJSONString(data));
            subTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            subTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", subTaskModule.getId(), e);
            data.setResult("失败：" + e.getMessage());
            subTaskModule.setSubTaskContext(JSON.toJSONString(data));
            subTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            subTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败：" + e.getMessage())));
        }

    }

    private void doProcessBrandMerchantAssociation(BusinessOpenAssociateMerchantContext context, String brandId, String parentId, BrandBatchAssociateMerchantsDTO brandBatchAssociateMerchantsDTO, BusinessOpenAssociateMerchantOpData data) {
        if (Objects.isNull(data.getCooperation())) {
            data.setResult("失败：合作关系不能为空");
            return;
        }
        MerchantInfoQueryResult merchantInfo = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(data.getMerchantSn()));
        if (Objects.isNull(merchantInfo)) {
            data.setResult("失败：商户不存在");
            return;
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantInfoByMerchantId(merchantInfo.getMerchantId());
        if (Objects.nonNull(brandMerchantModule)) {
            data.setResult("失败：商户已关联品牌");
            return;
        }
        String failReason = context.getMarketingAssociateCheckResult().get(merchantInfo.getMerchantId());
        if (WosaiStringUtils.isNotEmpty(failReason)) {
            data.setResult("失败：" + failReason);
            return;
        }
        brandDomainService.createBrandMerchantFromAudit(new BrandMerchantModule().setBrandId(brandId)
                .setParentBrandId(parentId)
                .setMerchantId(merchantInfo.getMerchantId())
                .setMerchantName(merchantInfo.getName())
                .setMerchantSn(merchantInfo.getMerchantSn())
                .setType(Integer.valueOf(0).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.PERSONAL.getType() : Integer.valueOf(1).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType() : BrandMerchantTypeEnum.COMPANY.getType())
                .setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode())
                .setMerchantType(data.getCooperation().getMerchantType()), context.getAuditSn());
        // 加油站不支持支付模式变更
        if (Objects.equals(SftFlagEnum.GAS_STATION.getFlag(), context.getBrandModule().getSftTag())) {
            data.setResult("成功");
            return;
        }
        PaymentModeChangeResult changeResult = merchantContractJobClient.changePaymentMode(
                new PaymentModeChangeRequest()
                        .setMerchantId(merchantInfo.getMerchantId())
                        .setTargetPaymentMode(context.getBrandBatchAssociateMerchantsDTO().getPaymentModeDetail().getCode())
                        .setPlatform(context.getPlatform())
                        .setOperatorId(context.getOperatorId())
                        .setRemark("审批:" + context.getAuditSn())
        );
        if (changeResult.isSuccess()) {
            BrandMerchantModule updateModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantInfo.getMerchantId());
            updateModule.setPaymentMode(context.getBrandBatchAssociateMerchantsDTO().getPaymentModeDetail().getCode());
            brandDomainService.updateBrandMerchant(updateModule);
            if (WosaiStringUtils.isNotEmpty(changeResult.getMsg())) {
                data.setResult("成功:" + changeResult.getMsg());
            } else {
                data.setResult("成功");
            }
        } else {
            data.setResult("失败:" + changeResult.getMsg());
        }
    }
}
