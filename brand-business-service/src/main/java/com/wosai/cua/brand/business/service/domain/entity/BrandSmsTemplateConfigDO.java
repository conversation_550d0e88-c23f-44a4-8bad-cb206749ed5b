package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 品牌短信模板配置表
 * @TableName brand_sms_template_config
 */
@TableName(value ="brand_sms_template_config")
@Data
public class BrandSmsTemplateConfigDO implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 通知方式：SMS-短信通知、APP-APP通知
     */
    private String method;

    /**
     * 模版code：
     */
    private String templateCode;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 终端code：
     */
    private String terminalCode;

    /**
     * 文本内容
     */
    private String textContent;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}