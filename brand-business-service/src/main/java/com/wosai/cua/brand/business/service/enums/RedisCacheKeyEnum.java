package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public enum RedisCacheKeyEnum {
    CACHE_KEY_BRAND_CONFIG_BRAND_ID("brand:config:%s", "品牌配置"),
    CACHE_KEY_BRAND_ACCOUNT_BRAND_ID("brand:account:%s", "品牌账户配置"),
    CACHE_KEY_BRAND_INFO_SN("brand:info:sn:%s", "品牌信息根据sn"),
    CACHE_KEY_BRAND_INFO_ID("brand:info:id:%s", "品牌信息根据id"),
    CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN("brand:no_search_merchant_info:merchant_sn:%s", "根据商户sn无搜索出结果"),
    CACHE_KEY_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN("brand:search_merchant_info:merchant_sn:%s", "根据商户sn搜索出结果"),
    CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_STORE_ID("brand:no_search_merchant_info:store_id:%s", "根据商户id无搜索出结果"),
    CACHE_KEY_SEARCH_MERCHANT_INFO_BY_STORE_ID("brand:search_merchant_info:store_id:%s", "根据商户id搜索出结果"),
    CACHE_KEY_MERCHANT_INFO_BY_BRAND_ID_AND_OUT_MERCHANT_NO("brand:merchant_info:brand_id:%s:out_merchant_no:%s", "根据品牌id和外部商户号查询出的结果"),
    ;
    private final String key;

    private final String desc;


    RedisCacheKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static String getKey(RedisCacheKeyEnum redisCacheKeyEnum, Object... args) {
        try {
            return String.format(redisCacheKeyEnum.key, args);
        } catch (Exception e) {
            log.error("RedisCacheKeyEnum.getKey error", e);
            return "key";
        }
    }
}
