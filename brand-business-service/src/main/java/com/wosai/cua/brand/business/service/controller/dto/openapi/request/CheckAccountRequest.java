package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class CheckAccountRequest extends OpenApiBaseRequest{

    /**
     * 商户编号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 外部商户号
     */
    @JsonProperty("out_merchant_no")
    private String outMerchantNo;

    /**
     * 证件编号
     */
    @JsonProperty("identify_no")
    @NotBlank(message = "identifyNo不能为空")
    private String identifyNo;
}
