package com.wosai.cua.brand.business.service.event.model;

import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.BrandMerchantPushToVolcanoEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMerchantPushToVolcanoEvent implements Event {

    /**
     * 商户 ID
     */
    private List<String> merchantIds;

    @Override
    public EventType getEventType() {
        return BrandMerchantPushToVolcanoEventType.MERCHANT_PUSH_TO_VOLCANO_EVENT_TYPE;
    }
}
