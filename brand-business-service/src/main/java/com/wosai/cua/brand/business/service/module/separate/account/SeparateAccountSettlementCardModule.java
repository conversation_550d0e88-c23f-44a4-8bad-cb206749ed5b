package com.wosai.cua.brand.business.service.module.separate.account;

import com.wosai.cua.brand.business.api.dto.request.app.AppCreateSeparateAccountSettleCardDTO;
import com.wosai.cua.brand.business.api.dto.response.SeparateAccountSettleCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppSeparateAccountSettleCardResponseDTO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import lombok.Data;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 分账账号结算卡模型
 *
 */
@Data
public class SeparateAccountSettlementCardModule {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户类型：1：个人账户；2：企业账户
     */
    private Integer type;

    /**
     * 账户类型描述
     */
    private String typeDesc;

    /**
     * 账户持有人名称
     */
    private String holder;

    /**
     * 账号
     */
    private String cardNumber;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 分支行名称
     */
    private String branchName;

    /**
     * 清算行号
     */
    private String clearingNumber;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 三方银行卡id
     */
    private String thirdBankCardId;

    /**
     * 银行预留手机号
     */
    private String cellphone;

    /**
     * 是否默认卡 0:否 1:是
     */
    private Integer defaultStatus;

    /**
     * 激活状态 0:未激活 1:已激活
     */
    private Integer activeStatus;

    /**
     * 激活时间
     */
    private Date activationTime;

    /**
     * 激活状态描述
     */
    private String activeStatusDesc;

    /**
     * 激活失败原因
     */
    private String activeFailReason;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    public static SeparateAccountSettlementCardModule convert(SeparateAccountSettlementCardDO separateAccountSettlementCardDO) {
        if (separateAccountSettlementCardDO == null) {
            return null;
        }
        SeparateAccountSettlementCardModule separateAccountSettlementCardModule = new SeparateAccountSettlementCardModule();
        separateAccountSettlementCardModule.setId(separateAccountSettlementCardDO.getId());
        separateAccountSettlementCardModule.setAccountNumber(separateAccountSettlementCardDO.getAccountNumber());
        separateAccountSettlementCardModule.setType(separateAccountSettlementCardDO.getType());
        separateAccountSettlementCardModule.setTypeDesc(separateAccountSettlementCardDO.getType() == 1 ? "对私" : "对公");
        separateAccountSettlementCardModule.setHolder(separateAccountSettlementCardDO.getHolder());
        separateAccountSettlementCardModule.setCardNumber(separateAccountSettlementCardDO.getCardNumber());
        separateAccountSettlementCardModule.setBankName(separateAccountSettlementCardDO.getBankName());
        separateAccountSettlementCardModule.setBranchName(separateAccountSettlementCardDO.getBranchName());
        separateAccountSettlementCardModule.setClearingNumber(separateAccountSettlementCardDO.getClearingNumber());
        separateAccountSettlementCardModule.setOpeningNumber(separateAccountSettlementCardDO.getOpeningNumber());
        separateAccountSettlementCardModule.setCellphone(separateAccountSettlementCardDO.getCellphone());
        separateAccountSettlementCardModule.setDefaultStatus(separateAccountSettlementCardDO.getDefaultStatus());
        separateAccountSettlementCardModule.setActiveStatus(separateAccountSettlementCardDO.getActiveStatus());
        separateAccountSettlementCardModule.setActiveStatusDesc(separateAccountSettlementCardDO.getActiveStatus() == 0 ? "未激活" : "已激活");
        separateAccountSettlementCardModule.setActivationTime(separateAccountSettlementCardDO.getActivationTime());
        separateAccountSettlementCardModule.setActiveFailReason(separateAccountSettlementCardDO.getActiveFailReason());
        separateAccountSettlementCardModule.setExt(separateAccountSettlementCardDO.getExt());
        separateAccountSettlementCardModule.setThirdBankCardId(separateAccountSettlementCardDO.getThirdBankCardId());
        return separateAccountSettlementCardModule;
    }

    public static List<SeparateAccountSettlementCardModule> convert(List<SeparateAccountSettlementCardDO> separateAccountSettlementCardDOList) {
        if (separateAccountSettlementCardDOList == null) {
            return Collections.emptyList();
        }
        return separateAccountSettlementCardDOList.stream().map(SeparateAccountSettlementCardModule::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<AppSeparateAccountSettleCardResponseDTO> convertToAppSeparateAccountSettleCardResponseDTOList(List<SeparateAccountSettlementCardModule> modules) {
        if (modules == null) {
            return Collections.emptyList();
        }
        return modules.stream().map(SeparateAccountSettlementCardModule::convertToAppSeparateAccountSettleCardResponseDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static AppSeparateAccountSettleCardResponseDTO convertToAppSeparateAccountSettleCardResponseDTO(SeparateAccountSettlementCardModule module) {
        if (module == null) {
            return null;
        }
        AppSeparateAccountSettleCardResponseDTO appSeparateAccountSettleCardResponseDTO = new AppSeparateAccountSettleCardResponseDTO();
        appSeparateAccountSettleCardResponseDTO.setId(module.getId());
        appSeparateAccountSettleCardResponseDTO.setAccountNumber(module.getAccountNumber());
        appSeparateAccountSettleCardResponseDTO.setHolder(module.getHolder());
        appSeparateAccountSettleCardResponseDTO.setBankCardNumber(module.getCardNumber());
        appSeparateAccountSettleCardResponseDTO.setAccountType(module.getType());
        appSeparateAccountSettleCardResponseDTO.setAccountTypeDesc(module.getTypeDesc());
        appSeparateAccountSettleCardResponseDTO.setBankName(module.getBankName());
        appSeparateAccountSettleCardResponseDTO.setBranchName(module.getBranchName());
        appSeparateAccountSettleCardResponseDTO.setBankBackPicture(module.getBankBackPicture());
        appSeparateAccountSettleCardResponseDTO.setBankIcon(module.getBankIcon());
        appSeparateAccountSettleCardResponseDTO.setActivateStatus(module.getActiveStatus());
        appSeparateAccountSettleCardResponseDTO.setActivateStatusDesc(module.getActiveStatusDesc());
        appSeparateAccountSettleCardResponseDTO.setDefaultCard(module.getDefaultStatus() == 1);
        appSeparateAccountSettleCardResponseDTO.setActivateFailReason(module.getActiveFailReason());
        appSeparateAccountSettleCardResponseDTO.setMobile(module.getCellphone());
        appSeparateAccountSettleCardResponseDTO.setActivationTime(module.getActivationTime());
        appSeparateAccountSettleCardResponseDTO.setThirdBankCardId(module.getThirdBankCardId());
        return appSeparateAccountSettleCardResponseDTO;
    }

    public static SeparateAccountSettlementCardModule convert(AppCreateSeparateAccountSettleCardDTO request) {
        SeparateAccountSettlementCardModule separateAccountSettlementCardModule = new SeparateAccountSettlementCardModule();
        separateAccountSettlementCardModule.setAccountNumber(request.getAccountNumber());
        separateAccountSettlementCardModule.setType(request.getType());
        separateAccountSettlementCardModule.setHolder(request.getHolder());
        separateAccountSettlementCardModule.setCardNumber(request.getBankCardNo());
        separateAccountSettlementCardModule.setOpeningNumber(request.getOpeningNumber());
        separateAccountSettlementCardModule.setCellphone(request.getMobile());
        separateAccountSettlementCardModule.setDefaultStatus(Boolean.TRUE.equals(request.getSetDefault()) ? 1 : 0);
        separateAccountSettlementCardModule.setBankName(request.getBankName());
        separateAccountSettlementCardModule.setBranchName(request.getBranchName());
        return separateAccountSettlementCardModule;
    }


    public static SeparateAccountSettleCardResponseDTO convertToSeparateAccountSettleCardResponseDTO(SeparateAccountSettlementCardModule module) {
        if (module == null) {
            return null;
        }
        SeparateAccountSettleCardResponseDTO separateAccountSettleCardResponseDTO = new SeparateAccountSettleCardResponseDTO();
        separateAccountSettleCardResponseDTO.setId(module.getId());
        separateAccountSettleCardResponseDTO.setAccountNumber(module.getAccountNumber());
        separateAccountSettleCardResponseDTO.setHolder(module.getHolder());
        separateAccountSettleCardResponseDTO.setBankCardNumber(module.getCardNumber());
        separateAccountSettleCardResponseDTO.setAccountType(module.getType());
        separateAccountSettleCardResponseDTO.setAccountTypeDesc(module.getTypeDesc());
        separateAccountSettleCardResponseDTO.setBankName(module.getBankName());
        separateAccountSettleCardResponseDTO.setBranchName(module.getBranchName());
        separateAccountSettleCardResponseDTO.setBankBackPicture(module.getBankBackPicture());
        separateAccountSettleCardResponseDTO.setBankIcon(module.getBankIcon());
        separateAccountSettleCardResponseDTO.setActivateStatus(module.getActiveStatus());
        separateAccountSettleCardResponseDTO.setActivateStatusDesc(module.getActiveStatusDesc());
        separateAccountSettleCardResponseDTO.setDefaultCard(module.getDefaultStatus() == 1);
        separateAccountSettleCardResponseDTO.setActivateFailReason(module.getActiveFailReason());
        separateAccountSettleCardResponseDTO.setMobile(module.getCellphone());
        separateAccountSettleCardResponseDTO.setActivationTime(module.getActivationTime());
        separateAccountSettleCardResponseDTO.setThirdBankCardId(module.getThirdBankCardId());
        return separateAccountSettleCardResponseDTO;
    }
}