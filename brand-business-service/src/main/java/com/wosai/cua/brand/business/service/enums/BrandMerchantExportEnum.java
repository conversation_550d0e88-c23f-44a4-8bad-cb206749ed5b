package com.wosai.cua.brand.business.service.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
public enum BrandMerchantExportEnum {
    MERCHANT_NAME("ALL", "merchantName", "商户名称"),
    MERCHANT_SN("ALL", "merchantSn", "商户编号"),
    MERCHANT_ID("SPA", "merchantId", "商户id"),
    MERCHANT_TYPE("ALL", "typeDesc", "商户主体类型"),
    MERCHANT_ROLE("ALL", "merchantTypeDesc", "商户角色"),
    MERCHANT_CONTACT_NAME("ALL", "merchantContactName", "联系人"),
    MERCHANT_CONTACT_PHONE("ALL", "merchantContactPhone", "联系电话"),
    FUND_MANAGEMENT_COMPANY("ALL", "fundManagementCompany", "资管机构"),
    ACCOUNT_OPEN_STATUS_DESC("ALL", "accountOpenStatusDesc", "开通状态"),
    ACCOUNT_OPEN_FAILURE_REASON("ALL", "accountOpenFailureReason", "开通失败原因"),
    BANK_CARD_ACTIVATE_STATUS("ALL", "bankCardActivateStatusDesc", "银行卡绑定激活状态"),
    OUT_MERCHANT_NO("ALL", "outMerchantNo", "外部商户编号"),
    SQB_STORE_SN("ALL", "sqbStoreSn", "收钱吧门店编号"),
    ASSOCIATED_MEITUAN_STORE_SN("ALL", "associatedMeituanStoreSn", "美团门店编号"),
    MEI_TUAN_STORE_STATUS("ALL", "meiTuanStoreStatus", "美团门店状态"),
    ASSOCIATED_ELM_STORE_SN("ALL", "associatedElmStoreSn", "饿了么门店编号"),
    DY_STORE_SN("ALL", "dyStoreSn", "抖音门店编号"),
    ARRANGEMENT_STATUS_DESC("ALL", "extra.myBankExtra.arrangementStatusDesc", "协议代扣状态"),
    CREATED_TIME("ALL", "createdTime", "入驻时间"),
    ACCOUNT_OPENED_TIME("ALL", "accountOpenedTime", "生效时间"),
    ACTIVATION_URL("ALL", "activationShortUrl", "激活链接");
    private final String platform;
    private final String fieldName;
    private final String title;

    BrandMerchantExportEnum(String platform, String fieldName, String title) {
        this.platform = platform;
        this.fieldName = fieldName;
        this.title = title;
    }

    public static List<BrandMerchantExportEnum> getAllExportEnum(String platform) {
        ArrayList<BrandMerchantExportEnum> brandMerchantExportEnums = Lists.newArrayList(BrandMerchantExportEnum.values());
        brandMerchantExportEnums.removeIf(b -> !"ALL".equals(b.getPlatform()) && !platform.equals(b.getPlatform()));
        return brandMerchantExportEnums;
    }

    public static Map<Integer, String> getColumnMap(String platform) {
        Map<Integer, String> map = Maps.newHashMap();
        List<BrandMerchantExportEnum> brandMerchantExportEnums = getAllExportEnum(platform);
        for (int i = 0; i < brandMerchantExportEnums.size(); i++){
            map.put(i, brandMerchantExportEnums.get(i).getTitle());
        }
        return map;
    }
}
