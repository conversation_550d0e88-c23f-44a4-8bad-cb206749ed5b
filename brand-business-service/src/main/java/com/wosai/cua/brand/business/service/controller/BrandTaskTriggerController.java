package com.wosai.cua.brand.business.service.controller;

import com.wosai.cua.brand.business.service.job.BrandTaskSchedule;
import com.wosai.cua.brand.business.service.thread.BatchPushBrandMerchantTask;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/10/8
 */
@RestController
@RequestMapping("/brand/task")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BrandTaskTriggerController {

    private final BrandTaskSchedule brandTaskSchedule;

    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");

    @Autowired
    private BatchPushBrandMerchantTask batchPushBrandMerchantTask;

    @GetMapping("/importTask")
    public ResponseEntity<String> importTask(Long timeLimit, int size) {
        brandTaskSchedule.brandMerchantImportTask(timeLimit, size);
        return SUCCESS_RESULT;
    }

    @GetMapping("/pushBrandMerchant")
    public ResponseEntity<String> pushBrandMerchant() {
        batchPushBrandMerchantTask.batchPush();
        return SUCCESS_RESULT;
    }

    @PostMapping("/pushBrandMerchantByMerchantId")
    public ResponseEntity<String> pushBrandMerchantByMerchantId(@RequestParam("merchant_id") String merchantId) {
        batchPushBrandMerchantTask.processBatch(Collections.singletonList(merchantId));
        return SUCCESS_RESULT;
    }
}
