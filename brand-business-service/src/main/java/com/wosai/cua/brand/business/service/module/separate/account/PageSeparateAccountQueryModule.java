package com.wosai.cua.brand.business.service.module.separate.account;

import com.wosai.cua.brand.business.api.dto.request.PageSeparateAccountInfoRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageSeparateAccountInfoRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageSeparateAccountQueryModule {
    /**
     * 分页参数
     */
    private Integer page;
    /**
     * 分页参数
     */
    private Integer pageSize;
    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 开户账户状态
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
     */
    private String openStatus;

    /**
     * 银行卡激活状态
     * @see com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum
     */
    private String bankCardActivateStatus;

    /**
     * 已选ID列表
     */
    private List<Long> selectedIdList;

    /**
     * 需要排除的数据id
     */
    private List<Long> excludeIdList;


    public PageSeparateAccountQueryModule(AppPageSeparateAccountInfoRequestDTO request){
        this.page = Objects.nonNull(request.getPage()) ? request.getPage() : 1;
        this.pageSize = Objects.nonNull(request.getPageSize()) ? request.getPageSize() : 10;
        this.brandId = request.getBrandId();
        this.accountName = request.getAccountName();
        if (Objects.nonNull(request.getOpenStatus())){
            this.openStatus = request.getOpenStatus().getStatus();
        }
        if (Objects.nonNull(request.getBankCardActivateStatus())){
            this.bankCardActivateStatus = request.getBankCardActivateStatus().getActivateStatus();
        }
        if (CollectionUtils.isNotEmpty(request.getSelectedIdList())){
            this.selectedIdList = request.getSelectedIdList();
        }

        if (CollectionUtils.isNotEmpty(request.getExcludeIdList())){
            this.excludeIdList = request.getExcludeIdList();
        }

    }

    public PageSeparateAccountQueryModule(PageSeparateAccountInfoRequestDTO request){
        this.page = Objects.nonNull(request.getPage()) ? request.getPage() : 1;
        this.pageSize = Objects.nonNull(request.getPageSize()) ? request.getPageSize() : 10;
        this.brandId = request.getBrandId();
        this.accountName = request.getAccountName();
        if (Objects.nonNull(request.getOpenStatus())){
            this.openStatus = request.getOpenStatus().getStatus();
        }
        if (Objects.nonNull(request.getBankCardActivateStatus())){
            this.bankCardActivateStatus = request.getBankCardActivateStatus().getActivateStatus();
        }
        if (CollectionUtils.isNotEmpty(request.getSelectedIdList())){
            this.selectedIdList = request.getSelectedIdList();
        }
        if (CollectionUtils.isNotEmpty(request.getExcludeIdList())){
            this.excludeIdList = request.getExcludeIdList();
        }

    }

    public static PageSeparateAccountQueryModule build(PageSeparateAccountInfoRequestDTO requestDTO){
        PageSeparateAccountQueryModule module = new PageSeparateAccountQueryModule();
        module.setPage(Objects.nonNull(requestDTO.getPage()) ? requestDTO.getPage() : 1);
        module.setPageSize(Objects.nonNull(requestDTO.getPageSize()) ? requestDTO.getPageSize() : 10);
        module.setBrandId(requestDTO.getBrandId());
        module.setAccountName(requestDTO.getAccountName());
        if (Objects.nonNull(requestDTO.getOpenStatus())){
            module.setOpenStatus(requestDTO.getOpenStatus().getStatus());
        }
        if (Objects.nonNull(requestDTO.getBankCardActivateStatus())){
            module.setBankCardActivateStatus(requestDTO.getBankCardActivateStatus().getActivateStatus());
        }
        if (CollectionUtils.isNotEmpty(requestDTO.getSelectedIdList())){
            module.setSelectedIdList(requestDTO.getSelectedIdList());
        }
        if (CollectionUtils.isNotEmpty(requestDTO.getExcludeIdList())){
            module.setExcludeIdList(requestDTO.getExcludeIdList());
        }
        return module;
    }
}
