package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.enums.AccountIdTypeEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.enums.PersonalExcelFieldEnum;
import com.wosai.cua.brand.business.service.enums.third.MemberGlobalTypeEnum;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.helper.UcUserV2Helper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.CreateMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PersonalMerchantAnalyzeModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseWithStoreReq;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.mc.model.req.StoreComplete;
import com.wosai.mc.model.resp.CreateMerchantResp;
import com.wosai.mc.model.resp.StoreCompleteResp;
import com.wosai.tools.vo.BankUnionCardReq;
import com.wosai.tools.vo.BankUnionCardVo;
import com.wosai.uc.dto.CreateUcUserReq;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 个人、小微商户解析服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PersonalMerchantsAnalyzeServiceImpl extends BaseMerchantsAnalyzeServiceImpl implements BrandMerchantsAnalyzeService {

    private static final Map<String, Integer> PERSONAL_LICENSE_TYPE_MAP = Maps.newConcurrentMap();

    /**
     * 字段列序号对应的枚举Map
     */
    private static final Map<Integer, PersonalExcelFieldEnum> PERSONAL_EXCEL_FIELD_ENUM_MAP = Maps.newConcurrentMap();

    /**
     * 字段对应的实体类属性缓存
     */
    private static final Map<PersonalExcelFieldEnum, Field> FIELD_CACHE = Maps.newHashMap();

    /**
     * 字段名对应的枚举Map
     */
    private static final Map<String, PersonalExcelFieldEnum> FIELD_NAME_EXCEL_FIELD_ENUM_MAP = Maps.newHashMap();

    @PostConstruct
    public void initMap() {
        PERSONAL_LICENSE_TYPE_MAP.put("身份证", 1);
        PERSONAL_LICENSE_TYPE_MAP.put("外国护照", 2);
        PERSONAL_LICENSE_TYPE_MAP.put("港澳通行证", 4);
        PERSONAL_LICENSE_TYPE_MAP.put("台胞证", 3);
        PERSONAL_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(PersonalExcelFieldEnum.values()).collect(Collectors.toMap(PersonalExcelFieldEnum::getColumnNo, Function.identity())));
        FIELD_NAME_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(PersonalExcelFieldEnum.values()).collect(Collectors.toMap(PersonalExcelFieldEnum::getFieldName, Function.identity())));
        this.cacheFields();
    }

    private void cacheFields() {
        Class<?> clazz = PersonalMerchantAnalyzeModule.class;
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                PersonalExcelFieldEnum personalExcelFieldEnum = FIELD_NAME_EXCEL_FIELD_ENUM_MAP.get(fieldName);
                if (Objects.isNull(personalExcelFieldEnum)) {
                    continue;
                }
                FIELD_CACHE.put(personalExcelFieldEnum, field);
            }
            clazz = clazz.getSuperclass();
        }
    }

    private Field getField(PersonalExcelFieldEnum fieldEnum) throws NoSuchFieldException {
        if (FIELD_CACHE.containsKey(fieldEnum)) {
            return FIELD_CACHE.get(fieldEnum);
        }
        throw new NoSuchFieldException();
    }


    @Override
    public BrandImportSheetEnum getSheetEnum() {
        return BrandImportSheetEnum.PERSONAL;
    }

    @Override
    public List<BaseMerchantAnalyzeModule> analyzeData(List<String[]> fields, FundManagementCompanyEnum fundManagementCompanyCode) {
        List<BaseMerchantAnalyzeModule> personalBusinessMerchantAnalyzeModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fields)) {
            return personalBusinessMerchantAnalyzeModules;
        }
        // 处理数据头两行为无效数据从第三行开始
        for (int row = 4; row < fields.size(); row++) {
            CreateMerchantAnalyzeModule analyzeModule = this.analyze(row, fields.get(row));
            if (Objects.nonNull(analyzeModule)) {
                analyzeModule.setFundManagementCompanyCode(fundManagementCompanyCode);
                personalBusinessMerchantAnalyzeModules.add(analyzeModule);
            }
        }
        return personalBusinessMerchantAnalyzeModules;
    }

    private CreateMerchantAnalyzeModule analyze(int row, String[] fieldArray) {
        PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule = new PersonalMerchantAnalyzeModule();
        personalMerchantAnalyzeModule.setRow(row);
        for (int i = 0; i < fieldArray.length; i++) {
            PersonalExcelFieldEnum personalExcelFieldEnum = PERSONAL_EXCEL_FIELD_ENUM_MAP.get(i);
            if (Objects.isNull(personalExcelFieldEnum)) {
                continue;
            }
            try {
                Field field = this.getField(personalExcelFieldEnum);
                this.analyzeField(personalExcelFieldEnum, field, personalMerchantAnalyzeModule, fieldArray[i]);
            } catch (NoSuchFieldException e) {
                log.warn("解析excel未找到相应字段：{}", personalExcelFieldEnum.getFieldName());
                return null;
            } catch (IllegalAccessException e) {
                log.warn("解析excel字段异常", e);
                return null;
            } catch (BrandBusinessException be) {
                log.warn("解析excel异常。", be);
                return null;
            }
        }
        return personalMerchantAnalyzeModule;
    }

    private void analyzeField(
            PersonalExcelFieldEnum personalExcelFieldEnum,
            Field field,
            PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule,
            String fieldValue
    ) throws IllegalAccessException {
        String value = fieldValue;
        if (StringUtils.isBlank(value)) {
            return;
        }
        value = StringUtils.trim(value);
        field.setAccessible(true);
        if (personalExcelFieldEnum.equals(PersonalExcelFieldEnum.ID_TYPE)) {
            field.set(personalMerchantAnalyzeModule, PERSONAL_LICENSE_TYPE_MAP.get(value));
            return;
        }
        if (personalExcelFieldEnum.equals(PersonalExcelFieldEnum.BRAND_MERCHANT_TYPE)) {
            value = MerchantTypeEnum.getMerchantTypeByDesc(value);
        }
        field.set(personalMerchantAnalyzeModule, value);
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules, Long strategyId) {
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        BrandDO brandDO = brandMapper.selectBrandByBrandId(brandId);
        if (Objects.isNull(brandDO)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND.getCode(), "未查到品牌信息！");
        }
        MerchantInfo adminMerchant = merchantService.getMerchantBySn(brandDO.getMerchantSn(), null);
        if (Objects.isNull(adminMerchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT.getCode(), "未查到商户！");
        }
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId));
        boolean allowLogin = Objects.isNull(brandConfig) || StringUtils.isBlank(brandConfig.getConfig());
        boolean needCreateStore;
        if (Objects.nonNull(brandConfig) && StringUtils.isNotBlank(brandConfig.getConfig())) {
            ConfigModule configModule = JSON.parseObject(brandConfig.getConfig(), ConfigModule.class);
            allowLogin = Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin();
            needCreateStore = Objects.nonNull(configModule.getNeedCreateStore()) && configModule.getNeedCreateStore();
        } else {
            needCreateStore = false;
        }
        boolean needSendSms = allowLogin;
        List<String> sqbStoreSnList = Lists.newArrayList();
        List<String> outMerchantNoList = Lists.newArrayList();
        Map<String,String> documentIdMerchantMap = Maps.newHashMap();
        // 创建商户的完整逻辑
        merchantAnalyzeModules.forEach(baseMerchantAnalyzeModule -> {
            if (Objects.isNull(baseMerchantAnalyzeModule)) {
                return;
            }
            BrandMerchantModule brandMerchantModule;
            try {
                if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getStoreSn())){
                    if (sqbStoreSnList.contains(baseMerchantAnalyzeModule.getStoreSn())){
                        throw new BrandBusinessException("门店编号重复！");
                    }
                    sqbStoreSnList.add(baseMerchantAnalyzeModule.getStoreSn());
                }
                if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getOutMerchantNo())){
                    if (outMerchantNoList.contains(baseMerchantAnalyzeModule.getOutMerchantNo())){
                        throw new BrandBusinessException("外部商户编号重复！");
                    }
                    outMerchantNoList.add(baseMerchantAnalyzeModule.getOutMerchantNo());
                }
                PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule = (PersonalMerchantAnalyzeModule) baseMerchantAnalyzeModule;
                // 判断下非平安账户校验下证件号是否是身份证
                if (Boolean.TRUE.equals(apolloConfig.getIdCardTypeCheckSwitch()) && !FundManagementCompanyEnum.PAB.equals(baseMerchantAnalyzeModule.getFundManagementCompanyCode()) && personalMerchantAnalyzeModule.getIdType() != 1) {
                    throw new BrandBusinessException("资管机构非平安银行的小微企业个人证件暂不支持除身份证以外的证件。");
                }
                // 校验第三方门店编号是否重复
                BrandMerchantsAnalyzeService.checkThirdStoreSn(baseMerchantAnalyzeModule, brandMerchantMapper);
                String ucUserId = ucUserServiceV2.getUcUserIdByIdentifier(UcUserV2Helper.identifierReq(personalMerchantAnalyzeModule.getCellphone()));
                // 如果没有账号的话先创建ucUser
                if (StringUtils.isEmpty(ucUserId)) {
                    CreateUcUserReq createUcUserReq = new CreateUcUserReq();
                    createUcUserReq.setIdentifier(personalMerchantAnalyzeModule.getCellphone());
                    createUcUserReq.setIdentity_type(1);
                    createUcUserReq.setApp("trade");
                    createUcUserReq.setPassword("123456");
                    createUcUserReq.setStatus(-1);
                    UcUserInfoResp ucUser = ucUserServiceV2.createUcUser(UcUserV2Helper.build(createUcUserReq));
                    ucUserId = ucUser.getId();
                }
                if (!needCreateStore) {
                    StoreInfo storeInfo = null;
                    if (StringUtils.isNotBlank(personalMerchantAnalyzeModule.getStoreSn())) {
                        storeInfo = storeService.getStoreBySn(personalMerchantAnalyzeModule.getStoreSn(), null);
                        if (Objects.isNull(storeInfo) && !apolloConfig.getStoreSnCheckWhiteList().contains(brandId)) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_STORE);
                        }
                        if (Objects.nonNull(storeInfo) && !storeInfo.getMerchant_id().equals(adminMerchant.getId())) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_NOT_BELONG_BRAND);
                        }
                        LambdaQueryWrapper<BrandMerchantDO> brandMerchantQueryWrapper = new LambdaQueryWrapper<>();
                        brandMerchantQueryWrapper.eq(BrandMerchantDO::getAssociatedSqbStoreId, storeInfo.getId());
                        brandMerchantQueryWrapper.eq(BrandMerchantDO::getDeleted, 0);
                        List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(brandMerchantQueryWrapper);
                        if (CollectionUtils.isNotEmpty(brandMerchants)) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_WAS_BIND_BRAND);
                        }
                    }
                    // 创建商户&商户下门店
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(personalMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    brandMerchantModule = this.getBrandMerchantModule(brandId, createMerchantResp, personalMerchantAnalyzeModule, storeInfo, strategyId);
                } else {
                    StoreComplete storeComplete = this.getStoreComplete(adminMerchant.getId(), personalMerchantAnalyzeModule);
                    StoreCompleteResp storeCompleteResp = storeService.createStoreComplete(storeComplete);
                    StoreInfo storeInfo = storeService.getStoreBySn(storeCompleteResp.getStoreInfo().getSn(), null);
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(personalMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    brandMerchantModule = this.getBrandMerchantModule(brandId, createMerchantResp, personalMerchantAnalyzeModule, storeInfo, strategyId);
                }
                if (Objects.isNull(brandMerchantModule)) {
                    return;
                }
                personalMerchantAnalyzeModule.setMerchantId(brandMerchantModule.getMerchantId());
                baseMerchantAnalyzeModule.setMatchedAccountNumber(
                        super.checkAlreadyExistJoin(
                                brandMerchantModule,
                                personalMerchantAnalyzeModule.getIdNumber(),
                                MemberGlobalTypeEnum.getSeparateAccountTypeBySqbType(personalMerchantAnalyzeModule.getIdType()),
                                documentIdMerchantMap
                        )
                );
                brandMerchantModules.add(brandMerchantModule);
            } catch (Exception e) {
                Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
                if (MapUtils.isEmpty(errorMapMsg)) {
                    errorMapMsg = Maps.newHashMap();
                }
                analyzeHelper.recordErrorMsg(errorMapMsg, baseMerchantAnalyzeModule, e.getMessage());
                ThreadLocalHelper.set(BrandImportSheetEnum.PERSONAL.name(), errorMapMsg);
            }
        });
        return brandMerchantModules;
    }

    private CreateMerchantAndStoreReq getMerchantComplete(PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule, String ucUserId, StoreInfo storeInfo) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(personalMerchantAnalyzeModule.getMerchantName());
        merchantReq.setBusinessName(personalMerchantAnalyzeModule.getMerchantName());
        merchantReq.setContactName(personalMerchantAnalyzeModule.getName());
        merchantReq.setContactCellphone(personalMerchantAnalyzeModule.getCellphone());
        merchantReq.setContactEmail(personalMerchantAnalyzeModule.getEmail());
        merchantReq.setStreetAddress(personalMerchantAnalyzeModule.getAddress());
        merchantReq.setProvince(personalMerchantAnalyzeModule.getProvince());
        merchantReq.setCity(personalMerchantAnalyzeModule.getCity());
        merchantReq.setDistrict(personalMerchantAnalyzeModule.getDistrict());
        merchantReq.setOwnerCellphone(personalMerchantAnalyzeModule.getCellphone());
        merchantReq.setOwnerName(personalMerchantAnalyzeModule.getName());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(personalMerchantAnalyzeModule, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(personalMerchantAnalyzeModule, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store;
        if (Objects.nonNull(storeInfo)){
            store = JSON.parseObject(JSON.toJSONString(storeInfo), CreateStoreReq.class);
            store.setMerchantId(merchantReq.getId());
        }else {
            store = new CreateStoreReq();
            store.setMerchantId(merchantReq.getId());
            store.setName(personalMerchantAnalyzeModule.getMerchantName());
        }
        store.setId(UUID.randomUUID().toString());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(0);
        license.setLegalPersonName(personalMerchantAnalyzeModule.getName());
        license.setLegalPersonIdType(personalMerchantAnalyzeModule.getIdType());
        license.setLegalPersonIdNumber(personalMerchantAnalyzeModule.getIdNumber());
        license.setLegalPersonIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        license.setIdValidity("********-********");
        return license;
    }

    private AccountReq getAccountReq(PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(personalMerchantAnalyzeModule.getCellphone());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(personalMerchantAnalyzeModule.getName());
        account.setIdType(AccountIdTypeEnum.getAccountIdTypeByAnalyzeIdType(personalMerchantAnalyzeModule.getIdType()));
        account.setIdNumber(personalMerchantAnalyzeModule.getIdNumber());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }

    private BrandMerchantModule getBrandMerchantModule(String brandId, CreateMerchantResp createMerchantResp, PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule, StoreInfo storeInfo, Long strategyId) {
        if (StringUtils.isEmpty(brandId) || Objects.isNull(createMerchantResp) || Objects.isNull(personalMerchantAnalyzeModule)) {
            log.info("【PersonalMerchantsAnalyzeServiceImpl】创建品牌商户失败,缺少参数");
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setMerchantSn(createMerchantResp.getMerchantSn());
        brandMerchantModule.setMerchantId(createMerchantResp.getMerchantId());
        brandMerchantModule.setMerchantName(personalMerchantAnalyzeModule.getMerchantName());
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setMerchantType(personalMerchantAnalyzeModule.getBrandMerchantType());
        if (Objects.nonNull(storeInfo)) {
            brandMerchantModule.setAssociatedSqbStoreId(storeInfo.getId());
            brandMerchantModule.setSqbStoreSn(storeInfo.getSn());
        }
        brandMerchantModule.setAssociatedMeituanStoreSn(personalMerchantAnalyzeModule.getMeiTuanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(personalMerchantAnalyzeModule.getElmStoreSn());
        brandMerchantModule.setDyStoreSn(personalMerchantAnalyzeModule.getDyStoreSn());
        brandMerchantModule.setStrategyId(strategyId);
        brandMerchantModule.setType(BrandMerchantTypeEnum.PERSONAL.getType());
        brandMerchantModule.setOutMerchantNo(personalMerchantAnalyzeModule.getOutMerchantNo());
        return brandMerchantModule;
    }

    @Override
    public List<BankCardModule> getBankCarModules(String brandId, FundManagementCompanyEnum fundManagementCompanyCode, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules) {
        List<BankCardModule> bankCardModules = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(merchantAnalyzeModules) && Boolean.TRUE.equals(fundManagementCompanyCode.getNeedCreateBankAccount())) {
            merchantAnalyzeModules.forEach(merchantAnalyzeModule -> {
                if (Objects.isNull(merchantAnalyzeModule) || StringUtils.isBlank(merchantAnalyzeModule.getMerchantId())) {
                    return;
                }
                BankUnionCardReq bankUnionCardReq = new BankUnionCardReq();
                bankUnionCardReq.setCardNum(merchantAnalyzeModule.getBankNumber());
                bankUnionCardReq.setPlatform("trade");
                bankUnionCardReq.setBusinessCase("brand_business");
                BankUnionCardVo bankUnionCardVo = null;
                try {
                    bankUnionCardVo = infoQueryService.bankUnionCard(bankUnionCardReq);
                } catch (Exception e) {
                    log.warn("【PersonalMerchantsAnalyzeServiceImpl】获取银行卡信息失败,接口异常",e);
                }
                if (Objects.isNull(bankUnionCardVo) || StringUtils.isBlank(bankUnionCardVo.getCnaps())) {
                    log.info("【PersonalMerchantsAnalyzeServiceImpl】获取银行卡信息失败,bankUnionCardVo为空或者开户行行号为空，{}",merchantAnalyzeModule.getBankNumber());
                    Object o = ThreadLocalHelper.get(BrandImportSheetEnum.PERSONAL.name());
                    Map<Integer, String> errorMap = Maps.newHashMap();
                    if (Objects.nonNull(o)) {
                        errorMap.putAll((Map<Integer, String>) o);
                    }
                    analyzeHelper.recordErrorMsg(errorMap, merchantAnalyzeModule, "获取银行卡信息失败，请检查卡号是否正确");
                    ThreadLocalHelper.set(BrandImportSheetEnum.PERSONAL.name(), errorMap);
                    merchantAnalyzeModule.setNeedRemove(true);
                    return;
                }
                PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule = (PersonalMerchantAnalyzeModule) merchantAnalyzeModule;
                personalMerchantAnalyzeModule.setOpeningNumber(bankUnionCardVo.getCnaps());
                BizBankAccountAddReq req = BrandMerchantsAnalyzeService.getBizBankAccountAddReq(personalMerchantAnalyzeModule, biz, BankAccountTypeEnum.PERSONAL.getAccountType(), 1);
                MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
                BankCardModule bankCardModule = new BankCardModule();
                bankCardModule.setHolder(req.getHolder());
                bankCardModule.setMerchantId(personalMerchantAnalyzeModule.getMerchantId());
                bankCardModule.setBrandId(brandId);
                bankCardModule.setAccountType(1);
                bankCardModule.setBankCardId(merchantBizBankAccount.getId());
                bankCardModule.setReservedMobileNumber(personalMerchantAnalyzeModule.getCellPhoneNumber());
                bankCardModule.setIsDefault(true);
                bankCardModule.setMobile(personalMerchantAnalyzeModule.getCellPhoneNumber());
                bankCardModule.setBankCardNo(merchantBizBankAccount.getNumber());
                bankCardModule.setOpeningBankNumber(merchantBizBankAccount.getOpening_number());
                bankCardModules.add(bankCardModule);
                super.checkAlreadyHasSeparateAccountSettlementCard(brandId, personalMerchantAnalyzeModule, bankCardModules);
            });
        }
        return bankCardModules;
    }

    @Override
    public void createErrorMsgIntoExcel(String filePath) {
        Map<Integer, String> errorMapMsg = getErrorMapMsg();
        if (errorMapMsg == null) return;
        FileHelper.createExcelErrorMsg(filePath, BrandImportSheetEnum.PERSONAL.getSheet(), BrandImportSheetEnum.PERSONAL.getErrorMsgColNum(), errorMapMsg);
    }

    private Map<Integer, String> getErrorMapMsg() {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.PERSONAL.name());
        if (Objects.isNull(o)) {
            return Collections.emptyMap();
        }
        Map errorMap = JSON.parseObject(JSON.toJSONString(o), Map.class);
        HashMap<Integer, String> errorMapMsg = Maps.newHashMap();
        errorMap.forEach((key, value) -> {
            boolean flag = Objects.nonNull(key) && NumberUtils.isCreatable(key.toString()) && Objects.nonNull(value);
            if (flag) {
                errorMapMsg.put(Integer.valueOf(key.toString()), value.toString());
            }
        });
        return errorMapMsg;
    }

    @Override
    public void checkParams(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                BaseMerchantAnalyzeModule analyzeModule = iterator.next();
                Class<? extends BaseMerchantAnalyzeModule> analyzeModuleClass = analyzeModule.getClass();
                boolean needRemove = analyzeHelper.checkFields(analyzeModuleClass, analyzeModule, errorMap, BrandImportSheetEnum.PERSONAL);
                if (needRemove) {
                    iterator.remove();
                }
            }
            ThreadLocalHelper.set(BrandImportSheetEnum.PERSONAL.name(), errorMap);
        }
    }

    private StoreComplete getStoreComplete(String merchantId, PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule) {
        StoreComplete storeComplete = new StoreComplete();
        CreateStoreReq createStoreReq = new CreateStoreReq();
        createStoreReq.setId(UUID.randomUUID().toString());
        createStoreReq.setMerchantId(merchantId);
        createStoreReq.setName(personalMerchantAnalyzeModule.getMerchantName());
        createStoreReq.setContactCellphone(personalMerchantAnalyzeModule.getCellphone());
        createStoreReq.setContactName(personalMerchantAnalyzeModule.getName());
        createStoreReq.setContactPhone(personalMerchantAnalyzeModule.getCellphone());
        createStoreReq.setContactEmail(personalMerchantAnalyzeModule.getEmail());
        createStoreReq.setStreetAddress(personalMerchantAnalyzeModule.getAddress());
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = this.getCreateStoreBusinessLicenseWithStore(merchantId, personalMerchantAnalyzeModule);
        storeComplete.setCreateStoreReq(createStoreReq);
        storeComplete.setStoreBusinessLicenseReq(storeBusinessLicenseReq);
        return storeComplete;
    }

    private CreateStoreBusinessLicenseWithStoreReq getCreateStoreBusinessLicenseWithStore(String merchantId, PersonalMerchantAnalyzeModule personalMerchantAnalyzeModule) {
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = new CreateStoreBusinessLicenseWithStoreReq();
        storeBusinessLicenseReq.setMerchantId(merchantId);
        storeBusinessLicenseReq.setType(0);
        storeBusinessLicenseReq.setLegalPersonName(personalMerchantAnalyzeModule.getName());
        storeBusinessLicenseReq.setLegalPersonIdType(personalMerchantAnalyzeModule.getIdType());
        storeBusinessLicenseReq.setLegalPersonIdNumber(personalMerchantAnalyzeModule.getIdNumber());
        return storeBusinessLicenseReq;
    }

    @Override
    public void checkIsValidChineseID(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.PERSONAL.name());
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (Objects.nonNull(o)) {
            errorMap.putAll((Map<Integer, String>) o);
        }
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                PersonalMerchantAnalyzeModule analyzeModule = (PersonalMerchantAnalyzeModule) iterator.next();
                boolean isValid = true;
                if (analyzeModule.getIdType() == 1) {
                    isValid = ParamsCheckHelper.isValidChineseID(analyzeModule.getIdNumber());
                    analyzeHelper.recordErrorMsg(errorMap, analyzeModule, "证件类型为身份证，身份证号码不合法");
                }
                if (!isValid) {
                    iterator.remove();
                }
            }
        }
        ThreadLocalHelper.set(BrandImportSheetEnum.PERSONAL.name(), errorMap);
    }
}
