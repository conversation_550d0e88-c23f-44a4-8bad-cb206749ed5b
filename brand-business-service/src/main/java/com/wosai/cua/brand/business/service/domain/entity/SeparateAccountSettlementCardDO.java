package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountSettlementCardModule;
import lombok.Data;

import java.util.Date;

/**
 * 分账账号结算卡信息表
 *
 * @TableName separate_account_settlement_card
 */
@TableName(value = "separate_account_settlement_card")
@Data
@SensitiveClass
public class SeparateAccountSettlementCardDO {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户类型：1：个人账户；2：企业账户
     */
    private Integer type;

    /**
     * 账户持有人名称
     */
    private String holder;

    /**
     * 账户持有人证件类型：1 身份证；2 香港居民来往内地通行证；3 澳门居民来往内地通行证； 4 台湾居民来往大陆通行证； 5 非中华人民共和国护照； 6 中国护照
     */
    private Integer idType;

    /**
     * 账户持有人证件编号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String identity;

    /**
     * 账号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String cardNumber;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 分支行名称
     */
    private String branchName;

    /**
     * 清算行号
     */
    private String clearingNumber;

    /**
     * 开户行号
     */
    private String openingNumber;

    /**
     * 三方银行卡id
     */
    private String thirdBankCardId;

    /**
     * 和账号绑定的手机号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.MOBILE)
    private String cellphone;

    /**
     * 是否默认卡 0:否 1:是
     */
    private Integer defaultStatus;

    /**
     * 激活状态 0:未激活 1:已激活
     */
    private Integer activeStatus;

    /**
     * 激活时间
     */
    private Date activationTime;

    /**
     * 激活失败原因
     */
    private String activeFailReason;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 扩展字段
     */
    private String ext;

    public static SeparateAccountSettlementCardDO convert(SeparateAccountSettlementCardModule separateAccountSettlementCardModule) {
        SeparateAccountSettlementCardDO separateAccountSettlementCardDO = new SeparateAccountSettlementCardDO();
        separateAccountSettlementCardDO.setAccountNumber(separateAccountSettlementCardModule.getAccountNumber());
        separateAccountSettlementCardDO.setType(separateAccountSettlementCardModule.getType());
        separateAccountSettlementCardDO.setHolder(separateAccountSettlementCardModule.getHolder());
        separateAccountSettlementCardDO.setCardNumber(separateAccountSettlementCardModule.getCardNumber());
        separateAccountSettlementCardDO.setBankName(separateAccountSettlementCardModule.getBankName());
        separateAccountSettlementCardDO.setBranchName(separateAccountSettlementCardModule.getBranchName());
        separateAccountSettlementCardDO.setClearingNumber(separateAccountSettlementCardModule.getClearingNumber());
        separateAccountSettlementCardDO.setOpeningNumber(separateAccountSettlementCardModule.getOpeningNumber());
        separateAccountSettlementCardDO.setCellphone(separateAccountSettlementCardModule.getCellphone());
        separateAccountSettlementCardDO.setDefaultStatus(separateAccountSettlementCardModule.getDefaultStatus());
        separateAccountSettlementCardDO.setActiveStatus(separateAccountSettlementCardModule.getActiveStatus());
        separateAccountSettlementCardDO.setActivationTime(separateAccountSettlementCardModule.getActivationTime());
        separateAccountSettlementCardDO.setActiveFailReason(separateAccountSettlementCardModule.getActiveFailReason());
        separateAccountSettlementCardDO.setExt(separateAccountSettlementCardModule.getExt());
        return separateAccountSettlementCardDO;
    }
}