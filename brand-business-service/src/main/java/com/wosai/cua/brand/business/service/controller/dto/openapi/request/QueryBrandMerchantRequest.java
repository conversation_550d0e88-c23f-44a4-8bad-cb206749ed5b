package com.wosai.cua.brand.business.service.controller.dto.openapi.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBrandMerchantRequest extends OpenApiBaseRequest{

    @JsonProperty("merchant_sn")
    private String merchantSn;

    @JsonProperty("out_merchant_no")
    private String outMerchantNo;
}
