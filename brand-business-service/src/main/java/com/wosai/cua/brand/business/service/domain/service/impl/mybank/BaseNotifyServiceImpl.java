package com.wosai.cua.brand.business.service.domain.service.impl.mybank;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandCallbackRecordsMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantControlRecordsMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantCreationRecordMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.helper.RedisLockHelper;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;

public class BaseNotifyServiceImpl {

    protected CommonRequestHandle commonRequestHandle;

    protected BrandMerchantMapper brandMerchantMapper;

    protected BrandMerchantControlRecordsMapper brandMerchantControlRecordsMapper;

    protected BrandConfigDOMapper brandConfigMapper;

    protected DefaultEventPublisher defaultEventPublisher;

    protected BrandMapper brandMapper;

    protected SeparateAccountDOMapper separateAccountDOMapper;

    protected BrandMerchantCreationRecordMapper brandMerchantCreationRecordMapper;

    protected RedisLockHelper redisLockHelper;

    protected BrandCallbackRecordsMapper brandCallbackRecordsMapper;

    public BaseNotifyServiceImpl() {
    }

    protected void updateUnionAccount(LambdaQueryWrapper<SeparateAccountDO> queryWrapper, SeparateAccountDO separateAccountDO) {
        queryWrapper.clear();
        queryWrapper.eq(SeparateAccountDO::getBrandId, separateAccountDO.getBrandId());
        queryWrapper.eq(SeparateAccountDO::getIdNumber, separateAccountDO.getIdNumber());
        queryWrapper.eq(SeparateAccountDO::getIdType, separateAccountDO.getIdType());
        queryWrapper.eq(SeparateAccountDO::getDeleted, 0);
        List<SeparateAccountDO> separateAccounts = separateAccountDOMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(separateAccounts)) {
            separateAccounts.forEach(accountDO -> {
                accountDO.setAccountOpenStatus(separateAccountDO.getAccountOpenStatus());
                accountDO.setSubAccountNo(separateAccountDO.getSubAccountNo());
                accountDO.setMemberId(separateAccountDO.getMemberId());
                accountDO.setMtime(new Date());
                accountDO.setAccountOpenedTime(separateAccountDO.getAccountOpenedTime());
                accountDO.setAccountOpenFailureReason(separateAccountDO.getAccountOpenFailureReason());
                separateAccountDOMapper.updateById(accountDO);
            });
        }
    }

}
