package com.wosai.cua.brand.business.service.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveMethod;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountSettlementCardDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountCardQueryDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountSettlementCardDOService;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【separate_account_settlement_card(分账账号结算卡信息表)】的数据库操作Service实现
 * @createDate 2025-06-05 10:18:07
 */
@Service
@SensitiveClass
@Slf4j
public class SeparateAccountSettlementCardDOServiceImpl extends ServiceImpl<SeparateAccountSettlementCardDOMapper, SeparateAccountSettlementCardDO>
        implements SeparateAccountSettlementCardDOService {

    @Autowired
    protected CryptHelper cryptHelper;

    @Override
    @SensitiveMethod
    public boolean add(SeparateAccountSettlementCardDO separateAccountSettlementCardDO) {
        LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, separateAccountSettlementCardDO.getAccountNumber());
        queryWrapper.eq(SeparateAccountSettlementCardDO::getCardNumber, separateAccountSettlementCardDO.getCardNumber());
        SeparateAccountSettlementCardDO one = getOne(queryWrapper);
        if (one == null) {
            separateAccountSettlementCardDO.setVersion(1L);
            return save(separateAccountSettlementCardDO);
        }
        separateAccountSettlementCardDO.setId(one.getId());
        separateAccountSettlementCardDO.setVersion(1L);
        separateAccountSettlementCardDO.setDeleted(0);
        separateAccountSettlementCardDO.setCtime(new Date());
        separateAccountSettlementCardDO.setMtime(new Date());
        boolean result = updateById(separateAccountSettlementCardDO);
        separateAccountSettlementCardDO.setCellphone(cryptHelper.decrypt(separateAccountSettlementCardDO.getCellphone()));
        return result;
    }

    @Override
    @SensitiveMethod
    public Page<SeparateAccountSettlementCardDO> pageList(Page<SeparateAccountSettlementCardDO> page, SeparateAccountCardQueryDO query) {
        return null;
    }

    @Override
    @SensitiveMethod
    public List<SeparateAccountSettlementCardDO> getCardList(String accountNumber) {
        if (StringUtils.isNotBlank(accountNumber)) {
            LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber);
            queryWrapper.eq(SeparateAccountSettlementCardDO::getDeleted, 0);
            return list(queryWrapper);
        }
        return Collections.emptyList();
    }

    @Override
    @SensitiveMethod
    public SeparateAccountSettlementCardDO getDefaultCard(String accountNumber) {
        if (StringUtils.isNotBlank(accountNumber)) {
            LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber);
            queryWrapper.eq(SeparateAccountSettlementCardDO::getDefaultStatus, 1);
            queryWrapper.eq(SeparateAccountSettlementCardDO::getDeleted, 0);
            return getOne(queryWrapper);
        }
        return null;
    }

    @Override
    @SensitiveMethod
    public List<SeparateAccountSettlementCardDO> getDefaultCardList(List<String> accountNumber) {
        if (CollectionUtils.isNotEmpty(accountNumber)) {
            LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber)
                    .eq(SeparateAccountSettlementCardDO::getDefaultStatus, 1)
                    .eq(SeparateAccountSettlementCardDO::getDeleted, 0)
                    .orderByDesc(SeparateAccountSettlementCardDO::getId);
            return list(
                    queryWrapper
            );
        }
        return Collections.emptyList();
    }

    @Override
    @SensitiveMethod
    public SeparateAccountSettlementCardDO getCardByAccountNumberAndCardNumber(SeparateAccountCardQueryDO separateAccountCardQueryDO) {
        LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, separateAccountCardQueryDO.getAccountNumber()).eq(SeparateAccountSettlementCardDO::getCardNumber, separateAccountCardQueryDO.getCardNumber())
                .eq(SeparateAccountSettlementCardDO::getDeleted, 0);
        List<SeparateAccountSettlementCardDO> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<SeparateAccountSettlementCardDO> getCardByCardNumber(SeparateAccountCardQueryDO separateAccountCardQueryDO) {
        LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(separateAccountCardQueryDO.getAccountNumber())){
            queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, separateAccountCardQueryDO.getAccountNumber());
        }
        if (StringUtils.isNotBlank(separateAccountCardQueryDO.getCardNumber())){
            queryWrapper.eq(SeparateAccountSettlementCardDO::getCardNumber, separateAccountCardQueryDO.getCardNumber());
        }
        queryWrapper.eq(SeparateAccountSettlementCardDO::getDeleted, 0);
        return list(queryWrapper);
    }

    @Override
    @SensitiveMethod
    public boolean updateCard(SeparateAccountSettlementCardDO separateAccountSettlementCardDO) {
        return updateById(separateAccountSettlementCardDO);
    }


}




