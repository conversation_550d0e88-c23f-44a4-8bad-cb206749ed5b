package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.api.dto.response.common.CommonResult;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.ImportExcelAuditContext;
import com.wosai.cua.brand.business.service.excel.data.SimpleBrandMerchantOpData;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class BrandRemoveAssociationHandler extends AbstractImportExcelHandler<ImportExcelAuditContext, SimpleBrandMerchantOpData> {

    @Autowired
    private BrandDomainService brandDomainService;
    @Autowired
    private MerchantService coreMerchantService;
    @Autowired
    private MerchantContractJobClient merchantContractJobClient;

    @Override
    public ImportExcelAuditContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return ImportExcelAuditContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.BATCH_REMOVE_MERCHANT_ASSOCIATION;
    }

    @Override
    public void preCheck(ImportExcelAuditContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        context.bindBrandModule(brandModule);
    }


    @Override
    protected void doHandleData(ImportExcelAuditContext context, BrandSubTaskModule brandSubTaskModule) {
        SimpleBrandMerchantOpData data = JSON.parseObject(brandSubTaskModule.getSubTaskContext(), SimpleBrandMerchantOpData.class);
        try {
            doRemoveAssociation(context, data);
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", brandSubTaskModule.getId(), e);
            data.setResult("失败：" + e.getMessage());
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败:" + e.getMessage())));
        }
    }

    private void doRemoveAssociation(ImportExcelAuditContext context, SimpleBrandMerchantOpData data) {
        Map merchant = coreMerchantService.getMerchantBySn(data.getMerchantSn());
        if (WosaiMapUtils.isEmpty(merchant)) {
            data.setResult("失败：商户不存在");
            return;
        }
        BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(context.getBrandId(), WosaiMapUtils.getString(merchant, DaoConstants.ID));
        if (Objects.isNull(brandMerchantModule)) {
            data.setResult("失败：商户未关联品牌");
            return;
        }
        if (Objects.equals(brandMerchantModule.getMerchantType(), MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            data.setResult("失败：品牌主商户不可解绑");
            return;
        }
        PaymentModeEnum paymentModeEnum = PaymentModeEnum.getPaymentModeEnum(brandMerchantModule.getPaymentMode());
        if (Objects.nonNull(paymentModeEnum) && paymentModeEnum.isBrandMode()) {
            PaymentModeChangeResult paymentModeChangeResult = merchantContractJobClient.changePaymentMode(new PaymentModeChangeRequest()
                    .setMerchantId(brandMerchantModule.getMerchantId())
                    .setTargetPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode())
                    .setPlatform(context.getPlatform())
                    .setOperatorId(context.getOperatorId())
                    .setRemark("审批:" + context.getAuditSn()));
            if (!paymentModeChangeResult.isSuccess()) {
                data.setResult("失败：" + paymentModeChangeResult.getMsg());
                return;
            }
            brandMerchantModule.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
            brandDomainService.updateBrandMerchant(brandMerchantModule);
        }
        brandDomainService.deleteBrandMerchantFromAudit(context.getBrandId(), Collections.singletonList(WosaiMapUtils.getString(merchant, DaoConstants.ID)), context.getAuditSn());
        data.setResult("成功");
    }
}
