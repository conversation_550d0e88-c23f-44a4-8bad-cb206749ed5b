package com.wosai.cua.brand.business.service.domain.service;

import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.module.brand.BrandConditionModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.ChangeBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.MerchantBrandDetailModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandDomainService {

    /**
     * 创建品牌
     *
     * @param brandModule 品牌模型
     */
    void createBrand(@SensitiveField BrandModule brandModule);

    /**
     * 创建品牌与商户的关联关系
     *
     * @param brandMerchantModule 品牌与商户的关联关系模型
     */
    void createBrandMerchant(BrandMerchantModule brandMerchantModule);

    /**
     * 创建品牌与商户的关联关系 从审批入口进入
     *
     * @param brandMerchantModule 品牌与商户的关联关系模型
     */
    void createBrandMerchantFromAudit(BrandMerchantModule brandMerchantModule, String auditSn);

    /**
     * 更新品牌与商户的关联关系
     *
     * @param brandMerchantModule 品牌与商户的关联关系模型
     */
    void updateBrandMerchant(BrandMerchantModule brandMerchantModule);

    void updateBrandMerchantByFields(ChangeBrandMerchantModule changeBrandMerchantModule);

    void updateMerchantAccountOpenStatus(String brandId, String merchantSn, String accountOpenStatus, String accountOpenStatusReason);

    /**
     * 根据商户id获取该商户所有的品牌信息
     *
     * @param merchantId 商户id
     * @return 商户的品牌信息模型
     */
    List<MerchantBrandDetailModule> getBrandModuleByMerchantId(String merchantId);

    /**
     * 根据商户id获取该商户所有的品牌信息
     *
     * @param merchantIds 商户id集合
     * @return 商户的品牌信息模型
     */
    List<MerchantBrandDetailModule> getBrandModuleByMerchantIds(List<String> merchantIds);

    /**
     * 根据品牌id查询商户品牌详情模型集合
     *
     * @param brandId 品牌id
     * @return 品牌模型
     */
    List<MerchantBrandDetailModule> getMerchantBrandDetailModuleByBrandId(String brandId);

    /**
     * 根据品牌id查询品牌模型
     *
     * @param brandId 品牌id
     * @return 品牌模型
     */
    BrandModule getBrandModuleByBrandId(String brandId);

    /**
     * 根据品牌编号查询品牌模型
     *
     * @param brandSn 品牌编号
     * @return 品牌模型
     */
    BrandModule getBrandModuleByBrandSn(String brandSn);

    /**
     * 根据品牌编号查询品牌模型
     *
     * @param merchantSn 品牌管理商户编号
     * @return 品牌模型
     */
    BrandModule getBrandModuleByMerchantSn(String merchantSn);

    /**
     * 根据品牌id集合查询品牌模型集合
     *
     * @param brandIds 品牌id集合查询品牌模型集合
     * @return 品牌模型集合
     */
    List<BrandModule> getBrandModuleByBrandIdsOrBrandSnList(List<String> brandIds, List<String> brandSnList);

    /**
     * 根据品牌条件分页查询品牌信息
     *
     * @param brandConditionModule 品牌条件模型
     * @return 分页查询结果
     */
    PageBrandModule pageBrandModuleByBrandConditions(BrandConditionModule brandConditionModule);

    /**
     * 根据商户条件分页查询品牌信息
     *
     * @param merchantConditionModule 商户条件模型
     * @return 分页查询结果
     */
    PageBrandModule pageBrandModuleByMerchantConditions(MerchantConditionModule merchantConditionModule);

    /**
     * 根据条件获取品牌商户信息
     *
     * @param merchantConditionModule 品牌商户查询模型
     * @return 品牌商户模型列表
     */
    List<BrandMerchantModule> getBrandMerchantByConditions(MerchantConditionModule merchantConditionModule);

    /**
     * 删除品牌
     *
     * @param brandId 品牌id
     * @return 删除数量
     */
    int deleteBrand(String brandId);

    /**
     * 根据品牌id获取品牌商户模型集合
     *
     * @param brandId 品牌id
     * @return 品牌商户集合
     */
    List<BrandMerchantModule> getBrandMerchantByBrandId(String brandId);

    /**
     * 根据品牌id获取品牌商户模型集合（包含子品牌的）
     *
     * @param brandId 品牌id
     * @return 品牌商户集合
     */
    List<BrandMerchantModule> getAllBrandMerchantsByBrandId(String brandId, Boolean selectChildBrand);

    /**
     * 根据品牌编号获取品牌商户模型集合（包含子品牌的）
     *
     * @param brandSn 品牌编号
     * @return 品牌商户集合
     */
    List<BrandMerchantModule> getAllBrandMerchantsByBrandSn(String brandSn, Boolean selectChildBrand);

    /**
     * 删除品牌商户关系
     *
     * @param brandId     品牌id
     * @param merchantIds 商户id集合
     * @return 删除条数
     */
    int deleteBrandMerchant(String brandId, List<String> merchantIds);

    /**
     * 删除品牌商户关系 从审批进入
     *
     * @param brandId     品牌id
     * @param merchantIds 商户id集合
     * @return 删除条数
     */
    int deleteBrandMerchantFromAudit(String brandId, List<String> merchantIds, String auditSn);

    /**
     * 批量创建品牌商户关系
     *
     * @param brandMerchantModuleList 品牌商户关系模型
     * @return 创建条数
     */
    int bathCreateBrandMerchant(List<BrandMerchantModule> brandMerchantModuleList);

    /**
     * 创建一条品牌与商户关系创建的记录
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @param merchantSn 商户编号
     * @return 写入数据的主键
     */
    BrandMerchantCreationRecordModule createBrandMerchantCreationRecord(String brandId, String merchantId, String merchantSn);

    /**
     * 根据品牌id商户id和商户编号获取商户创建记录
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @param merchantSn 商户编号
     * @return 创建记录
     */
    BrandMerchantCreationRecordModule getBrandMerchantCreationRecordByBrandIdAndMerchantIdAndSn(String brandId, String merchantId, String merchantSn);

    /**
     * 创建一条品牌与商户创建的记录
     *
     * @param brandMerchantCreationRecordModule 品牌商户创建记录模型
     */
    void createBrandMerchantCreationRecord(BrandMerchantCreationRecordModule brandMerchantCreationRecordModule);

    /**
     * 批量插入品牌商户关系创建记录
     *
     * @param brandMerchantCreationRecordModules 品牌商户创建记录模型集合
     */
    void batchCreateBrandMerchantCreationRecord(List<BrandMerchantCreationRecordModule> brandMerchantCreationRecordModules);

    /**
     * 根据条件查询品牌商户模型
     *
     * @param merchantConditionModule 条件模型
     * @return 品牌商户模型
     */
    PageBrandMerchantModule pageQueryBrandMerchantModule(MerchantConditionModule merchantConditionModule);

    /**
     * 根据条件查询品牌商户模型（非分页）
     *
     * @param merchantConditionModule 条件模型
     * @return 品牌商户模型
     */
    List<BrandMerchantModule> queryBrandMerchantByConditions(MerchantConditionModule merchantConditionModule);

    /**
     * 编辑品牌信息
     *
     * @param module 品牌模型
     */
    void modifyBrand(@SensitiveField BrandModule module);

    /**
     * 根据品牌id和商户id查询品牌商户模型
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandMerchantByBrandIdAndMerchantId(String brandId, String merchantId);

    /**
     * 根据品牌id和商户编号查询品牌商户模型
     *
     * @param brandId    品牌id
     * @param merchantSn 商户编号
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandMerchantByBrandIdAndMerchantSn(String brandId, String merchantSn);

    /**
     * 更新数据
     *
     * @param module 插入记录模型
     * @return 更新条数
     */
    int updateCreateBrandMerchantCreationRecord(BrandMerchantCreationRecordModule module);

    /**
     * 根据品牌id和商户id集合获取品牌商户模型
     *
     * @param brandId     品牌id
     * @param merchantIds 品牌商户id集合
     * @return 品牌商户模型集合
     */
    List<BrandMerchantModule> getBrandMerchantByBrandIdAndMerchantIds(String brandId, List<String> merchantIds);

    /**
     * 删除品牌下的提现策略
     *
     * @param strategyIdList 提现策略id
     * @return 删除条数
     */
    int deleteBrandMerchantWithdrawStrategy(List<Long> strategyIdList);


    /**
     * 获取
     *
     * @param merchantId
     * @return
     */
    BrandMerchantModule getBrandMerchantInfoByMerchantId(String merchantId);

    /**
     * 获取
     *
     * @param memberId
     * @return
     */
    List<BrandMerchantModule> getBrandMerchantInfoByMemberId(String memberId);


    /**
     * 获取品牌商户信息
     *
     * @param brandId 品牌id
     * @param subAccountNo 子账户号
     * @return BrandMerchantModule
     */
    List<BrandMerchantModule> getBrandMerchantInfoBySubAccountNo(String brandId, String subAccountNo);

    /**
     * 根据商户编号获取
     *
     * @param outMerchantNo
     * @return
     */
    BrandMerchantModule getBrandMerchantInfoByOutMerchantNo(String brandId, String outMerchantNo);

    /**
     * 关联提现策略
     *
     * @param brandId        品牌id
     * @param strategyId     策略id
     * @param merchantSnList 商户编号集合
     * @return 关联成功数量
     */
    int relevanceBrandWithdrawStrategy(Long strategyId, String brandId, List<String> merchantSnList);

    /**
     * 根据商户编号查询品牌
     *
     * @param merchantSn 商户编号
     * @return 品牌模型
     */
    BrandModule getBrandByMerchantSn(String merchantSn);

    /**
     * 获取品牌商户的模型
     *
     * @param brandId 品牌id
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandOwnerMerchant(String brandId);

    /**
     * 获取品牌商户的模型
     *
     * @param brandId 品牌id
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandAdminMerchant(String brandId);

    /**
     * 检查品牌名称是否已经被使用过
     *
     * @param brandName 品牌名称
     * @return 判断结果
     */
    boolean checkExistBrandName(String brandName);

    /**
     * 根据品牌id和商户id分页获取品牌商户模型
     *
     * @param brandId  品牌id
     * @param id       分页id
     * @param pageSize 分页大小
     * @return 品牌商户模型集合
     */
    List<BrandMerchantModule> pageGetBrandMerchantByBrandIdAndId(String brandId, Long id, Integer pageSize);

    /**
     * 根据id分页获取品牌商户模型
     *
     * @param id       分页id
     * @param pageSize 分页大小
     * @return 品牌商户模型集合
     */
    List<BrandMerchantModule> pageBrandMerchantById(Long id, Integer pageSize);

    /**
     * 根据记录id查询记录
     *
     * @param recordId 记录id
     * @return 记录模型
     */
    BrandMerchantCreationRecordModule getRecordModuleByRecordId(String recordId);

    /**
     * 根据记录id查询记录
     *
     * @param startId                 开始id
     * @param pageSize                分页大小
     * @param merchantConditionModule 条件模型
     * @return 品牌商户模型集合
     */
    List<BrandMerchantModule> pageIdBrandMerchantByConditions(Long startId, Integer pageSize, MerchantConditionModule merchantConditionModule);

    /**
     * 根据商户编号获取品牌商户模型
     *
     * @param brandId    品牌id
     * @param merchantSn 商户编号
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandMerchantByMerchantSn(String brandId, String merchantSn);

    List<BrandMerchantModule> getBrandMerchantSimpleInfoByBrandId(String brandId);

    /**
     * 平安银行登记
     *
     * @param brandModule         品牌模型
     * @param brandMerchantModule 商户模型
     * @return 登记是否通过
     */
    boolean pabRegister(BrandModule brandModule, BrandMerchantModule brandMerchantModule);

    /**
     * 根据sqb门店id获取品牌商户模型(最新的)
     *
     * @param brandId 品牌id
     * @param storeId sqb门店id
     * @return 品牌商户模型
     */
    BrandMerchantModule getBrandMerchantBySqbStoreId(String brandId, String storeId);

    BrandModule getBrandModuleByClientSnAndVendorId(String clientSn, String vendorId);
}
