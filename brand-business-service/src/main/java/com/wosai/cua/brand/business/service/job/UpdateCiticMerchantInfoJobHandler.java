package com.wosai.cua.brand.business.service.job;

import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.UpdateMerchantRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.UpdateUserInfoResponse;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandConditionModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateCiticMerchantInfoJobHandler {

    private final BrandDomainService brandDomainService;

    private final BrandConfigDomainService brandConfigDomainService;

    private final ApolloConfig apolloConfig;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = new EnumMap<>(FundManagementCompanyEnum.class);



    public UpdateCiticMerchantInfoJobHandler(BrandDomainService brandDomainService, BrandConfigDomainService brandConfigDomainService, ApolloConfig apolloConfig, List<TripartiteSystemCallService> tripartiteSystemCallServices) {
        this.brandDomainService = brandDomainService;
        this.brandConfigDomainService = brandConfigDomainService;
        this.apolloConfig = apolloConfig;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
    }

    @PostConstruct
    public void init() {
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    @XxlJob("updateCiticMerchantInfo")
    public void updateCiticMerchantInfo() {
        int pageSize = 10;
        int currentPage = 1;
        while (true) {
            BrandConditionModule conditionModule = new BrandConditionModule();
            conditionModule.setPage(currentPage);
            conditionModule.setPageSize(pageSize);
            conditionModule.setFundManagementCompanyCode(FundManagementCompanyEnum.CITIC.getFundManagementCompanyCode());
            PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByBrandConditions(conditionModule);
            if (pageBrandModule == null || pageBrandModule.getTotal() == null || pageBrandModule.getTotal() == 0 || CollectionUtils.isEmpty(pageBrandModule.getBrandList())) {
                break;
            }
            pageBrandModule.getBrandList().forEach(this::updateCiticMerchantInfo);
            currentPage ++;
        }
    }

    private void updateCiticMerchantInfo(BrandModule brandModule) {
        int pageSize = 100;
        int currentPage = 1;
        while (true) {
            MerchantConditionModule conditionModule = new MerchantConditionModule();
            conditionModule.setBrandId(brandModule.getBrandId());
            conditionModule.setPage(currentPage);
            conditionModule.setPageSize(pageSize);
            conditionModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
            PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(conditionModule);
            if (pageBrandMerchantModule == null || pageBrandMerchantModule.getTotal() == null || pageBrandMerchantModule.getTotal() == 0
                || CollectionUtils.isEmpty(pageBrandMerchantModule.getBrandMerchantModuleList())) {
                break;
            }
            CiticBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), CiticBankConfigModule.class);
            pageBrandMerchantModule.getBrandMerchantModuleList().forEach(brandMerchantModule -> update(brandMerchantModule, configModule));
            currentPage++;
        }
    }

    private void update(BrandMerchantModule brandMerchantModule, CiticBankConfigModule configModule) {
        UpdateMerchantRequest request = new UpdateMerchantRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(brandMerchantModule.getMemberId());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + brandMerchantModule.getMerchantSn().substring(brandMerchantModule.getMerchantSn().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        request.setUserRole(apolloConfig.getCiticUserTypeMap().getString(brandMerchantModule.getMerchantType()));
        if (BrandMerchantTypeEnum.PERSONAL.getType().equals(brandMerchantModule.getType())){
            request.setUserType("1");
        }
        if (BrandMerchantTypeEnum.COMPANY.getType().equals(brandMerchantModule.getType())){
            request.setUserType("2");
        }
        if (BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType().equals(brandMerchantModule.getType())){
            request.setUserType("3");
        }
        request.setSigningAgreementNo("A" + brandMerchantModule.getMerchantSn());
        request.setSigningFlag("1");
        try {
            TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(request, UpdateUserInfoResponse.class, configModule);
            // 休眠0.5秒
            Thread.sleep(500);
        } catch (Exception e) {
            log.error("更新中信商户信息失败", e);
        }
    }
}
