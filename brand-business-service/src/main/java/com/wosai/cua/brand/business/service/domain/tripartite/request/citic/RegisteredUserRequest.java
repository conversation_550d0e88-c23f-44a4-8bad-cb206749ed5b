package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.api.enums.AccountTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class RegisteredUserRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {

    /**
     * 商户端用户编号
     */
    @XmlElement(name = "MCHNT_USER_ID")
    private String merchantSn;
    /**
     * 用户类型
     */
    @XmlElement(name = "USER_TYPE")
    private String userType;
    /**
     * 用户姓名
     */
    @XmlElement(name = "USER_NM")
    private String userName;
    /**
     * 用户角色
     */
    @XmlElement(name = "USER_ROLE")
    private String userRole;
    /**
     * 签约类型
     */
    @XmlElement(name = "SIGN_TYPE")
    private String signType;
    /**
     * 证件类型
     * 企业用户必填； 用户类型为个人： 01-个人身份证 22-户口簿 23-外国护照 25-军人军官证 26-军人士兵证 27-武警军官证 28-港澳居民往来内地通行证（香港） 29-台湾居民往来大陆通行证 30-临时居民身份证 31-外国人永久居留证 32-中国护照 33-武警士兵证 34-港澳居民往来内地通行证（澳门） 35-边民出入境通行证 36-台湾居民旅行证 37 -港澳居民居住证（香港） 38-港澳居民居住证（澳门） 39 -台湾居民居住证 用户类型为企业： 02-组织机构代码 03-统一社会信用代码 04-民办非企业登记证书 05-社会团体法人登记证书 06-事业单位法人登记证 07-营业执照号码 08-其他单位证件 "（1）管理台-运营管理新增“对公用户特殊证件白名单”，添加白名单后，允许商户下企业以及个体工商户用户注册/信息变更时，使用民办非企业登记证书、社会团体法人登记证书、事业单位法人登记证书、营业执照等其他对公特殊证件：未添加白名单的商户，企业以及个体工商户用户仅支持使用组织机构代码、统一社会信用代码。 （2）使用对公特殊证件注册用户变更用户信息，需运营人员在管理台用户信息审批页面进行审核。"
     */
    @XmlElement(name = "USER_ID_TYPE")
    private String userIdType;
    /**
     * 证件号码
     */
    @XmlElement(name = "USER_ID_NO")
    private String userIdNo;
    /**
     * 用户手机号
     */
    @XmlElement(name = "USER_PHONE")
    private String userPhone;
    /**
     * 企业法人姓名
     */
    @XmlElement(name = "CORP_NM")
    private String legalPersonName;
    /**
     * 企业法人证件号码
     */
    @XmlElement(name = "CORP_ID_NO")
    private String legalPersonId;
    /**
     * 企业法人证件类型
     */
    @XmlElement(name = "CORP_ID_TYPE")
    private String legalPersonIdType;
    /**
     * 用户地址
     */
    @XmlElement(name = "USER_ADD")
    private String userAddress;

    /**
     * 签约协议号
     */
    @Setter
    @XmlElement(name = "AGRM_NUM")
    private String signingAgreementNo;

    /**
     * 签约标志
     */
    @XmlElement(name = "SIGCT_FLG")
    private String signingFlag;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_REGISTERED_USER;
    }

    public RegisteredUserRequest() {
        super();
        super.setTransCode(REGISTERED_USER_REQUEST_TRANS_CODE);
    }

    public static RegisteredUserRequest build(CiticBankConfigModule configModule, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, String userType) {
        RegisteredUserRequest registeredUserRequest = new RegisteredUserRequest();
        registeredUserRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + merchant.getSn().substring(merchant.getSn().length() - 8));
        recordModule.setRecordId(registeredUserRequest.getReqSsn());
        registeredUserRequest.setMerchantId(configModule.getMerchantId());
        registeredUserRequest.setMerchantSn(merchant.getSn());
        switch (merchantBusinessLicense.getType()) {
            case 0:
                registeredUserRequest.setUserType("1");
                registeredUserRequest.setUserIdType("01");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getLegal_person_id_number());
                registeredUserRequest.setUserPhone(merchant.getContact_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getLegal_person_name());
                break;
            case 1:
                registeredUserRequest.setUserType("3");
                registeredUserRequest.setUserIdType("03");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getNumber());
                registeredUserRequest.setUserPhone(merchant.getOwner_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getName());
                break;
            case 2:
                registeredUserRequest.setUserType("2");
                registeredUserRequest.setUserIdType("03");
                registeredUserRequest.setUserIdNo(merchantBusinessLicense.getNumber());
                registeredUserRequest.setUserPhone(merchant.getOwner_cellphone());
                registeredUserRequest.setUserName(merchantBusinessLicense.getName());
                break;
            default:
                throw new BrandBusinessException("商户类型错误");
        }
        registeredUserRequest.setUserRole(userType);
        registeredUserRequest.setSignType("00");
        registeredUserRequest.setLegalPersonIdType("01");
        registeredUserRequest.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
        registeredUserRequest.setLegalPersonId(merchantBusinessLicense.getLegal_person_id_number());
        registeredUserRequest.setUserAddress(merchant.getStreet_address());
        registeredUserRequest.setSigningAgreementNo("A" + merchant.getSn());
        registeredUserRequest.setSigningFlag("1");
        return registeredUserRequest;
    }

    public static RegisteredUserRequest build(CiticBankConfigModule configModule, SeparateAccountDO separateAccountDO,String userType) {
        RegisteredUserRequest registeredUserRequest = new RegisteredUserRequest();
        registeredUserRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + separateAccountDO.getAccountNumber().substring(separateAccountDO.getAccountNumber().length() - 8));
        registeredUserRequest.setMerchantId(configModule.getMerchantId());
        registeredUserRequest.setMerchantSn(separateAccountDO.getAccountNumber());
        if (AccountTypeEnum.PERSONAL.getType().equals(separateAccountDO.getType())) {
            registeredUserRequest.setUserType("1");
            switch (separateAccountDO.getIdType()) {
                case "02":
                    registeredUserRequest.setUserIdType("28");
                    break;
                case "03":
                    registeredUserRequest.setUserIdType("34");
                    break;
                case "04":
                    registeredUserRequest.setUserIdType("39");
                    break;
                case "05":
                    registeredUserRequest.setUserIdType("23");
                    break;
                default:
                    registeredUserRequest.setUserIdType("01");
            }
            registeredUserRequest.setUserIdNo(separateAccountDO.getIdNumber());
            registeredUserRequest.setUserPhone(separateAccountDO.getContractPhone());
            registeredUserRequest.setUserName(separateAccountDO.getAccountName());
        }
        if (AccountTypeEnum.INDIVIDUAL_BUSINESS.getType().equals(separateAccountDO.getType())){
            registeredUserRequest.setUserType("3");
            registeredUserRequest.setUserIdType("03");
            registeredUserRequest.setUserIdNo(separateAccountDO.getIdNumber());
            registeredUserRequest.setUserPhone(separateAccountDO.getContractPhone());
            registeredUserRequest.setUserName(separateAccountDO.getAccountName());
        }
        if (AccountTypeEnum.COMPANY.getType().equals(separateAccountDO.getType())){
            registeredUserRequest.setUserType("2");
            registeredUserRequest.setUserIdType("03");
            registeredUserRequest.setUserIdNo(separateAccountDO.getIdNumber());
            registeredUserRequest.setUserPhone(separateAccountDO.getContractPhone());
            registeredUserRequest.setUserName(separateAccountDO.getAccountName());
        }
        registeredUserRequest.setUserRole(userType);
        registeredUserRequest.setSignType("00");
        registeredUserRequest.setLegalPersonIdType("01");
        switch (separateAccountDO.getLegalPersonIdType()){
            case "02":
                registeredUserRequest.setLegalPersonIdType("28");
                break;
            case "03":
                registeredUserRequest.setLegalPersonIdType("34");
                break;
            case "04":
                registeredUserRequest.setLegalPersonIdType("39");
                break;
            case "05":
                registeredUserRequest.setLegalPersonIdType("23");
                break;
            default:
                registeredUserRequest.setLegalPersonIdType("01");
        }
        registeredUserRequest.setLegalPersonName(separateAccountDO.getLegalPersonName());
        registeredUserRequest.setLegalPersonId(separateAccountDO.getLegalPersonId());
        registeredUserRequest.setSigningAgreementNo("A" + separateAccountDO.getAccountNumber());
        registeredUserRequest.setSigningFlag("1");
        return registeredUserRequest;
    }
}
