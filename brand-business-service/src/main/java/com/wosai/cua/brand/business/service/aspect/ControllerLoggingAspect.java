package com.wosai.cua.brand.business.service.aspect;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class ControllerLoggingAspect {

    @Pointcut("execution(* com.wosai.cua.brand.business.service.controller.rest..*.*(..))")
    public void controllerMethods() {
        // 定义切点，匹配所有Controller中的方法
    }

    @Around("controllerMethods()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Map<String, Object> toAppendEntriesMap = new HashMap<>(10);
        toAppendEntriesMap.put("method", joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());

        LocalDateTime beginTime = LocalDateTime.now();

        // 执行目标方法
        Object result = joinPoint.proceed();

        Long executionTime = Duration.between(beginTime, LocalDateTime.now()).toMillis();

        // 记录响应信息
        toAppendEntriesMap.put("response", result);
        toAppendEntriesMap.put("duration", executionTime);
        log.info("Execution time: {} ms", executionTime);
        log.info(Markers.appendEntries(toAppendEntriesMap),"");
        return result;
    }

    @AfterThrowing(pointcut = "controllerMethods()", throwing = "ex")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable ex) {
        Map<String, Object> toAppendEntriesMap = new HashMap<>(10);
        toAppendEntriesMap.put("method", joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        toAppendEntriesMap.put("error", StringUtils.defaultIfEmpty(ex.getMessage(), Objects.nonNull(ex.getCause())?ex.getCause().getMessage() : "未知异常"));
        log.error(Markers.appendEntries(toAppendEntriesMap), "");
    }
}
