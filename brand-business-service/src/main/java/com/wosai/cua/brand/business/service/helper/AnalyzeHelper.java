package com.wosai.cua.brand.business.service.helper;

import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.annotations.AnalyzeFieldCheck;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.module.bank.BankInfoBeanModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PooledMerchantAnalyzeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AnalyzeHelper {

    private final BankCardDomainService bankCardDomainService;

    private final BankOfDepositHelper bankOfDepositHelper;

    public AnalyzeHelper(BankCardDomainService bankCardDomainService, BankOfDepositHelper bankOfDepositHelper) {
        this.bankCardDomainService = bankCardDomainService;
        this.bankOfDepositHelper = bankOfDepositHelper;
    }


    public boolean checkFields(Class<? extends BaseMerchantAnalyzeModule> analyzeModuleClass,
                               BaseMerchantAnalyzeModule analyzeModule,
                               Map<Integer, String> errorMap,
                               BrandImportSheetEnum sheetEnum) {
        if (Objects.isNull(analyzeModuleClass)) {
            return false;
        }

        // 获取类及其父类的所有字段
        List<Field> fields = getAllFields(analyzeModuleClass);

        boolean hasErrors = false;
        for (Field field : fields) {
            if (field.isAnnotationPresent(AnalyzeFieldCheck.class)) {
                AnalyzeFieldCheck annotation = field.getAnnotation(AnalyzeFieldCheck.class);
                field.setAccessible(true); // 注意：此处仍然需要，但应评估安全影响

                Object value;
                try {
                    value = field.get(analyzeModule);
                } catch (IllegalAccessException e) {
                    log.warn("字段获取异常", e);
                    // 现在，也将错误记录到errorMap中
                    recordErrorMsg(errorMap, analyzeModule, "字段访问异常");
                    continue;
                }

                hasErrors |= checkFieldConstraints(value, annotation, errorMap, analyzeModule, sheetEnum);
            }
        }
        return hasErrors;
    }

    public boolean checkFields(Class<? extends PooledMerchantAnalyzeModule> analyzeModuleClass,
                               PooledMerchantAnalyzeModule analyzeModule,
                               Map<Integer, String> errorMap,
                               BrandImportSheetEnum sheetEnum) {
        if (Objects.isNull(analyzeModuleClass)) {
            return false;
        }

        // 获取类及其父类的所有字段
        List<Field> fields = getAllFields(analyzeModuleClass);

        boolean hasErrors = false;
        for (Field field : fields) {
            if (field.isAnnotationPresent(AnalyzeFieldCheck.class)) {
                AnalyzeFieldCheck annotation = field.getAnnotation(AnalyzeFieldCheck.class);
                field.setAccessible(true); // 注意：此处仍然需要，但应评估安全影响

                Object value;
                try {
                    value = field.get(analyzeModule);
                } catch (IllegalAccessException e) {
                    log.warn("字段获取异常", e);
                    // 现在，也将错误记录到errorMap中
                    recordErrorMsg(errorMap, analyzeModule, "字段访问异常");
                    continue;
                }

                hasErrors |= checkFieldConstraints(value, annotation, errorMap, analyzeModule, sheetEnum);
            }
        }
        return hasErrors;
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = Arrays.stream(clazz.getDeclaredFields()).collect(Collectors.toList());
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null) {
            fields.addAll(getAllFields(superClass));
        }
        return fields;
    }

    private boolean checkFieldConstraints(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap,
                                          BaseMerchantAnalyzeModule analyzeModule, BrandImportSheetEnum sheetEnum) {
        boolean needToRemove = false;
        // 必填校验
        needToRemove = checkNeedRequired(value, annotation, errorMap, analyzeModule, needToRemove, sheetEnum);
        // 格式校验
        needToRemove = checkFormat(value, annotation, errorMap, analyzeModule, needToRemove);
        // 值有效性校验
        needToRemove = checkValueValidity(value, annotation, errorMap, analyzeModule, needToRemove, sheetEnum);
        return needToRemove;
    }

    private boolean checkFieldConstraints(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap,
                                          PooledMerchantAnalyzeModule analyzeModule, BrandImportSheetEnum sheetEnum) {
        boolean needToRemove = false;
        // 必填校验
        needToRemove = checkNeedRequired(value, annotation, errorMap, analyzeModule, needToRemove, sheetEnum);
        // 格式校验
        needToRemove = checkFormat(value, annotation, errorMap, analyzeModule, needToRemove);
        return needToRemove;
    }

    private boolean checkValueValidity(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule, boolean needToRemove, BrandImportSheetEnum sheetEnum) {
        // 参数有效性检查
        if (annotation == null || errorMap == null || analyzeModule == null) {
            return true;
        }

        List<BrandImportSheetEnum> importSheetEnums = Arrays.stream(annotation.sheetEnums()).collect(Collectors.toList());
        List<FundManagementCompanyEnum> groups = Arrays.stream(annotation.groups()).collect(Collectors.toList());

        String valueStr = Objects.toString(value, "");
        String searchType = annotation.bankSearch();

        // 验证是否需要检查有效性以及搜索类型
        if (annotation.needCheckValueValidity() && StringUtils.isNotEmpty(searchType) && importSheetEnums.contains(sheetEnum) && groups.contains(analyzeModule.getFundManagementCompanyCode())) {
            if (!valueStr.isEmpty() && value instanceof String) {
                try {
                    BankInfoBeanModule bankInfoModule =
                            bankCardDomainService.getBankInfoModuleByOpeningNumber(valueStr);

                    if (this.processBankInfoModule(bankInfoModule, annotation, errorMap, analyzeModule)) {
                        return true; // 需要移除
                    }
                } catch (Exception e) {
                    log.warn("Error occurred during value validity check: {}", e.getMessage());
                    recordErrorMsg(errorMap, analyzeModule, "系统错误，请联系管理员");
                    return true; // 需要移除
                }
            } else {
                recordErrorMsg(errorMap, analyzeModule, annotation.checkValueValidityMsg());
                return true; // 需要移除
            }
        }
        return needToRemove;
    }

    private boolean processBankInfoModule(BankInfoBeanModule bankInfoModule, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule) {
        if (bankInfoModule == null) {
            recordErrorMsg(errorMap, analyzeModule, annotation.checkValueValidityMsg());
            return true; // 需要移除
        }
        // 设置银行信息到analyzeModule
        analyzeModule.setOpeningNumber(bankInfoModule.getOpeningNumber());
        return false; // 不需要移除
    }


    private boolean checkFormat(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule, boolean needToRemove) {
        if (annotation.needFormatCheck() && (bankOfDepositHelper.getApolloConfig().getFormatCheckSwitch() != null && bankOfDepositHelper.getApolloConfig().getFormatCheckSwitch()) && Objects.nonNull(value) && value instanceof String) {
            String strValue = (String) value;
            Pattern pattern = Pattern.compile(annotation.pattern());
            Matcher matcher = pattern.matcher(strValue);
            if (annotation.satisfyPattern() && !matcher.matches()) {
                recordErrorMsg(errorMap, analyzeModule, annotation.formatCheckMsg());
                needToRemove = true;
            }
            if (!annotation.satisfyPattern() && matcher.matches()) {
                recordErrorMsg(errorMap, analyzeModule, annotation.formatCheckMsg());
                needToRemove = true;
            }
        }
        return needToRemove;
    }

    private boolean checkFormat(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, PooledMerchantAnalyzeModule analyzeModule, boolean needToRemove) {
        if (annotation.needFormatCheck() && (bankOfDepositHelper.getApolloConfig().getFormatCheckSwitch() != null && bankOfDepositHelper.getApolloConfig().getFormatCheckSwitch()) && Objects.nonNull(value) && value instanceof String) {
            String strValue = (String) value;
            Pattern pattern = Pattern.compile(annotation.pattern());
            Matcher matcher = pattern.matcher(strValue);
            if (annotation.satisfyPattern() && !matcher.matches()) {
                recordErrorMsg(errorMap, analyzeModule, annotation.formatCheckMsg());
                needToRemove = true;
            }
            if (!annotation.satisfyPattern() && matcher.matches()) {
                recordErrorMsg(errorMap, analyzeModule, annotation.formatCheckMsg());
                needToRemove = true;
            }
        }
        return needToRemove;
    }

    private boolean checkNeedRequired(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule, boolean needToRemove, BrandImportSheetEnum sheetEnum) {
        List<BrandImportSheetEnum> importSheetEnums = Arrays.stream(annotation.sheetEnums()).collect(Collectors.toList());
        List<FundManagementCompanyEnum> groups = Arrays.stream(annotation.groups()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importSheetEnums) && CollectionUtils.isEmpty(groups) && annotation.needRequired()) {
            needToRemove = isNeedToRemove(value, annotation, errorMap, analyzeModule, needToRemove);
        }
        boolean needToCheck = Objects.nonNull(sheetEnum) && importSheetEnums.contains(sheetEnum) &&
                Objects.nonNull(analyzeModule.getFundManagementCompanyCode()) && groups.contains(analyzeModule.getFundManagementCompanyCode()) && annotation.needRequired();
        if (needToCheck) {
            needToRemove = isNeedToRemove(value, annotation, errorMap, analyzeModule, needToRemove);
        }
        return needToRemove;
    }

    private boolean checkNeedRequired(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, PooledMerchantAnalyzeModule analyzeModule, boolean needToRemove, BrandImportSheetEnum sheetEnum) {
        List<BrandImportSheetEnum> importSheetEnums = Arrays.stream(annotation.sheetEnums()).collect(Collectors.toList());
        List<FundManagementCompanyEnum> groups = Arrays.stream(annotation.groups()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importSheetEnums) && CollectionUtils.isEmpty(groups) && annotation.needRequired()) {
            needToRemove = isNeedToRemove(value, annotation, errorMap, analyzeModule, needToRemove);
        }
        return needToRemove;
    }

    private boolean isNeedToRemove(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule, boolean needToRemove) {
        boolean checkNull = Objects.isNull(value) || (value instanceof String && StringUtils.isEmpty((String) value));
        if (checkNull) {
            recordErrorMsg(errorMap, analyzeModule, annotation.message());
            needToRemove = true;
        }
        return needToRemove;
    }

    private boolean isNeedToRemove(Object value, AnalyzeFieldCheck annotation, Map<Integer, String> errorMap, PooledMerchantAnalyzeModule analyzeModule, boolean needToRemove) {
        boolean checkNull = Objects.isNull(value) || (value instanceof String && StringUtils.isEmpty((String) value));
        if (checkNull) {
            recordErrorMsg(errorMap, analyzeModule, annotation.message());
            needToRemove = true;
        }
        return needToRemove;
    }

    public void recordErrorMsg(Map<Integer, String> errorMap, BaseMerchantAnalyzeModule analyzeModule, String errorMessage) {
        String concurrentErrorMsg = errorMap.getOrDefault(analyzeModule.getRow(), "");
        concurrentErrorMsg = concurrentErrorMsg.isEmpty() ? errorMessage : concurrentErrorMsg + ";" + errorMessage;
        errorMap.put(analyzeModule.getRow(), concurrentErrorMsg);
    }

    public void recordErrorMsg(Map<Integer, String> errorMap, PooledMerchantAnalyzeModule analyzeModule, String errorMessage) {
        String concurrentErrorMsg = errorMap.getOrDefault(analyzeModule.getRow(), "");
        concurrentErrorMsg = concurrentErrorMsg.isEmpty() ? errorMessage : concurrentErrorMsg + ";" + errorMessage;
        errorMap.put(analyzeModule.getRow(), concurrentErrorMsg);
    }
}
