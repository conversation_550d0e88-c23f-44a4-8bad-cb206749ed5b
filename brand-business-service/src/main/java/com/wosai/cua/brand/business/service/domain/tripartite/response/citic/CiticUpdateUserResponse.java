package com.wosai.cua.brand.business.service.domain.tripartite.response.citic;


import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.data.UpdateUserResponseData;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR> Date: 2024/08/08 Time: 3:53 PM
 * 中信银行结算商户注册响应（测试）
 */
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "ROOT")
public class CiticUpdateUserResponse extends CiticResponse<UpdateUserResponseData> {

    public CiticUpdateUserResponse(){
        super();
    }

    public CiticUpdateUserResponse(String code, String message) {
        super(code, message);
    }

    @Override
    @XmlElement(name = "CODE")
    public String getCode() {
        return super.getCode();
    }

    @Override
    @XmlElement(name = "MESSAGE")
    public String getMessage() {
        return super.getMessage();
    }

    @Override
    public void setCode(String code) {
        super.setCode(code);
    }

    @Override
    public void setMessage(String message) {
        super.setMessage(message);
    }

    @XmlElement(name = "DATA")
    @Override
    public UpdateUserResponseData getData() {
        return data;
    }

    @Override
    public void setData(UpdateUserResponseData data) {
        this.data = data;
    }
}

