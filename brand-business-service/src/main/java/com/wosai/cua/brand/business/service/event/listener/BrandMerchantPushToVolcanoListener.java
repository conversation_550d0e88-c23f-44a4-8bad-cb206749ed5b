package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantPushToVolcanoEvent;
import com.wosai.cua.brand.business.service.event.type.BrandMerchantPushToVolcanoEventType;
import com.wosai.cua.brand.business.service.externalservice.salessystem.SalesSystemClient;
import com.wosai.cua.brand.business.service.helper.MerchantBrandPushHelper;
import com.wosai.cua.brand.business.service.kafka.DataCenterKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.dto.DataCenterMessageBody;
import com.wosai.cua.brand.business.service.kafka.dto.MerchantBrandPushToVolcanoParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BrandMerchantPushToVolcanoListener implements Listener<BrandMerchantPushToVolcanoEvent> {

    private final MerchantBrandPushHelper merchantBrandPushHelper;

    @Autowired
    public BrandMerchantPushToVolcanoListener(MerchantBrandPushHelper merchantBrandPushHelper) {
        this.merchantBrandPushHelper = merchantBrandPushHelper;
    }

    @Override
    public EventType getEventType() {
        return BrandMerchantPushToVolcanoEventType.MERCHANT_PUSH_TO_VOLCANO_EVENT_TYPE;
    }

    @Override
    public void handle(BrandMerchantPushToVolcanoEvent event) {
        log.info("BrandMerchantPushToVolcanoListener 监听到品牌商户上送火山事件：{}", JSON.toJSONString(event));
        List<String> merchantIds = event.getMerchantIds();
        if (CollectionUtils.isEmpty(merchantIds)) {
            return;
        }
        merchantBrandPushHelper.pushBrandMerchant(merchantIds);
    }
}
