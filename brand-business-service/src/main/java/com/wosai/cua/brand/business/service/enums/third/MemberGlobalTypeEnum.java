package com.wosai.cua.brand.business.service.enums.third;

import lombok.Getter;

@Getter
public enum MemberGlobalTypeEnum {
    MEMBER_GLOBAL_TYPE_COMPANY("73", -99, "99","统一社会信用代码"),
    MEMBER_GLOBAL_TYPE_PERSON("1", 1, "01","身份证"),
    MEMBER_GLOBAL_TYPE_HONGKONG("3", 4, "02","香港居民通行证"),
    MEMBER_GLOBAL_TYPE__MARKARO("3", 4, "03","澳门台居民通行证"),
    MEMBER_GLOBAL_TYPE_TAIWAN("5", 3, "04","台湾居民来往大陆通行证"),
    MEMBER_GLOBAL_TYPE_PASSPORT("19", 2, "05","外国护照");

    private final String type;
    private final Integer sqbType;
    private final String separateAccountType;
    private final String description;


    MemberGlobalTypeEnum(String type, Integer sqbType, String separateAccountType, String description) {
        this.type = type;
        this.sqbType = sqbType;
        this.separateAccountType = separateAccountType;
        this.description = description;
    }

    public static String getTypeBySqbType(Integer sqbType)
    {
        for (MemberGlobalTypeEnum memberGlobalTypeEnum : MemberGlobalTypeEnum.values())
        {
            if (memberGlobalTypeEnum.getSqbType().equals(sqbType))
            {
                return memberGlobalTypeEnum.getType();
            }
        }
        return null;
    }

    public static String getSeparateAccountTypeBySqbType(Integer sqbType){
        for (MemberGlobalTypeEnum memberGlobalTypeEnum : MemberGlobalTypeEnum.values()) {
            if (memberGlobalTypeEnum.getSqbType().equals(sqbType)) {
                return memberGlobalTypeEnum.getSeparateAccountType();
            }
        }
        return null;
    }

    public static String getTypeBySeparateAccountType(String separateAccountType) {
        for (MemberGlobalTypeEnum memberGlobalTypeEnum : MemberGlobalTypeEnum.values())
        {
            if (memberGlobalTypeEnum.getSeparateAccountType().equals(separateAccountType))
            {
                return memberGlobalTypeEnum.getType();
            }
        }
        return null;
    }

}
