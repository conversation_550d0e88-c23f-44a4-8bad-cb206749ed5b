package com.wosai.cua.brand.business.service.excel.handler;

import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.BrandBatchAddMerchantsDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.excel.context.BusinessOpenAddMerchantContext;
import com.wosai.cua.brand.business.service.excel.data.BusinessOpenAddMicroMerchantOpData;
import com.wosai.cua.brand.business.service.excel.groups.Default;
import com.wosai.cua.brand.business.service.excel.groups.Indirect;
import com.wosai.cua.brand.business.service.excel.model.BusinessOpenBasicModel;
import com.wosai.cua.brand.business.service.excel.model.MerchantPhotoModel;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.BankInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.DistrictInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.bankinfo.model.IndustryInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.StoreExtAndPicturesQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.UcUserInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.salespoi.model.PoiDetailQueryResult;
import com.wosai.cua.brand.business.service.helper.OssFileHelper;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.ValidationUtils;
import com.wosai.cua.brand.business.service.helper.ValidityTransferHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.Store;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 批量新增小微商户关联品牌
 *
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class BusinessOpenAddMicroMerchantHandler extends AbstractAddMerchantHandler<BusinessOpenAddMerchantContext, BusinessOpenAddMicroMerchantOpData> {

    @Override
    public BusinessOpenAddMerchantContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return BusinessOpenAddMerchantContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.BUSINESS_OPEN_ADD_MICRO_MERCHANT;
    }

    @Override
    public void checkData(BusinessOpenAddMerchantContext context, BusinessOpenAddMicroMerchantOpData data) {
        BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO = context.getBrandBatchAddMerchantsDTO();
        if (brandBatchAddMerchantsDTO.getOpenIndirect()) {
            doCheckOpenIndirect(data);
        }
    }

    private void doCheckOpenIndirect(BusinessOpenAddMicroMerchantOpData data) {
        ValidationUtils.ValidationResult validate = ValidationUtils.validate(data, Default.class, Indirect.class);
        if (validate.isInvalid()) {
            if (WosaiStringUtils.isNotEmpty(data.getSequenceNo())) {
                throw new BrandBusinessException(String.format("序号:%s %s", data.getSequenceNo(), validate.getMsg()));
            } else {
                throw new BrandBusinessException(validate.getMsg());
            }
        }
        // 校验身份证号
        if(!ParamsCheckHelper.isValidChineseID(data.getIdentity())) {
            throw new BrandBusinessException(String.format("序号:%s 身份证号格式不正确", data.getSequenceNo()));
        }
        // 校验证件有效期
        if (!ParamsCheckHelper.checkIdValidityStart(data.getIdValidityStart())) {
            throw new BrandBusinessException(String.format("序号:%s 证件有效期开始日期格式不正确", data.getSequenceNo()));
        }
        if (!ParamsCheckHelper.checkIdValidityEnd(data.getIdValidityEnd())) {
            throw new BrandBusinessException(String.format("序号:%s 证件有效期截止日期格式不正确", data.getSequenceNo()));
        }
        // 校验银行卡有效期
        if (!ParamsCheckHelper.checkCardValidity(data.getCardValidity())) {
            throw new BrandBusinessException(String.format("序号:%s 银行卡有效期格式不正确", data.getSequenceNo()));
        }
    }

    @Override
    protected void doOpenBusiness(BusinessOpenAddMicroMerchantOpData data, BusinessOpenAddMerchantContext context, File photoFile) {
        BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO = context.getBrandBatchAddMerchantsDTO();
        String merchantId = WosaiStringUtils.isNotEmpty(data.getMerchantId()) ? data.getMerchantId() : UUID.randomUUID().toString();
        data.setMerchantId(merchantId);
        MerchantPhotoModel merchantPhotoModel = assemblePhotoInfo(data.getSequenceNo(), photoFile, brandBatchAddMerchantsDTO.getLaterSupplyPhoto());
        BusinessOpenBasicModel businessOpenBasicModel = assembleBasicInfo(merchantId, data, context, merchantPhotoModel);
        if (brandBatchAddMerchantsDTO.getOpenIndirect()) {
            // 将该商户的品牌ID_支付模式写入到redis中，这样进件服务在选择入网规则组的时候可以查到支付模式
            redisClusterTemplate.opsForValue().set("BRAND_MERCHANT_PAYMENT_MODE:" + merchantId, context.getBrandModule().getBrandId() + "_" + brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode(), 20, TimeUnit.SECONDS);
            BusinessOpenResult businessOpenResult = openIndirect(businessOpenBasicModel, merchantPhotoModel, data, context);
            if (businessOpenResult.isSuccess()) {
                data.setMerchantSn(businessOpenResult.getMerchantSn());
                saveOrUpdateBrandMerchant(context, businessOpenResult.getMerchantSn(), businessOpenBasicModel, brandBatchAddMerchantsDTO, data);
                data.setIndirectResult("成功");
            } else {
                data.setIndirectResult("失败:" + businessOpenResult.getMessage());
            }
        }
    }

    private MerchantPhotoModel assemblePhotoInfo(String sequenceNo, File photoFile, Boolean laterSupplyPhoto) {
        if (Objects.equals(true, laterSupplyPhoto)) {
            return MerchantPhotoModel.DEFAULT_IMAGE_MODEL;
        }
        if (Objects.isNull(photoFile) || !photoFile.exists() || !photoFile.isDirectory()) {
            throw new BrandBusinessException("未找到照片文件");
        }
        File file = FileUtils.getFile(photoFile, sequenceNo);
        if (!file.exists() || !file.isDirectory()) {
            throw new BrandBusinessException("未找到照片文件");
        }
        MerchantPhotoModel merchantPhotoModel = new MerchantPhotoModel();
        merchantPhotoModel.setBankCardPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.BANK_CARD_NAME)).orElseThrow(() -> new BrandBusinessException("未找到银行卡照片")));
        merchantPhotoModel.setHolderIdFrontPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.MICRO_ID_FRONT_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到身份证正面照")));
        merchantPhotoModel.setHolderIdBackPhoto(Optional.ofNullable(OssFileHelper.getTargetPhotoUrl(file, MerchantPhotoModel.MICRO_ID_BACK_PHOTO)).orElseThrow(() -> new BrandBusinessException("未找到身份证反面照")));
        return merchantPhotoModel;
    }

    private void saveOrUpdateBrandMerchant(BusinessOpenAddMerchantContext context, String merchantSn, BusinessOpenBasicModel businessOpenBasicModel, BrandBatchAddMerchantsDTO brandBatchAddMerchantsDTO, BusinessOpenAddMicroMerchantOpData data) {
        BrandMerchantModule brandMerchantInfo = brandDomainService.getBrandMerchantInfoByMerchantId(businessOpenBasicModel.getMerchantId());
        if (Objects.nonNull(brandMerchantInfo)) {
            brandMerchantInfo.setPaymentMode(brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode());
            brandDomainService.updateBrandMerchant(brandMerchantInfo);
        } else {
            BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
            brandMerchantModule.setBrandId(context.getBrandModule().getBrandId());
            brandMerchantModule.setParentBrandId(context.getBrandModule().getParentId());
            brandMerchantModule.setMerchantId(businessOpenBasicModel.getMerchantId());
            brandMerchantModule.setMerchantSn(merchantSn);
            brandMerchantModule.setMerchantName(WosaiMapUtils.getString(businessOpenBasicModel.getMerchant(), Merchant.NAME));
            brandMerchantModule.setMerchantId(businessOpenBasicModel.getMerchantId());
            brandMerchantModule.setMerchantType(data.getCooperation().getMerchantType());
            brandMerchantModule.setPaymentMode(brandBatchAddMerchantsDTO.getPaymentModeDetail().getCode());
            brandMerchantModule.setType(BrandMerchantTypeEnum.PERSONAL.getType());
            brandDomainService.createBrandMerchantFromAudit(brandMerchantModule, context.getAuditSn());
        }
    }

    private BusinessOpenBasicModel assembleBasicInfo(String merchantId, BusinessOpenAddMicroMerchantOpData data, BusinessOpenAddMerchantContext context, MerchantPhotoModel merchantPhotoModel) {
        // 组装参数
        DistrictInfoQueryResult districtInfoQueryResult = bankInfoClient.getDistrictInfoByCode(data.getDistrictCode());
        PoiDetailQueryResult poiDetailQueryResult = salesPoiClient.getPoiDetailByCompleteAddress(districtInfoQueryResult.getProvince() + districtInfoQueryResult.getCity() + districtInfoQueryResult.getDistrict() + data.getStreetAddress());
        IndustryInfoQueryResult industryInfoQueryResult = bankInfoClient.getIndustryInfoByCode(data.getIndustryCode());
        // 商户
        Map<String, Object> merchant = assembleMerchantInfo(data, industryInfoQueryResult, districtInfoQueryResult, poiDetailQueryResult);
        // account
        Map<String, Object> account = assembleAccountInfo(data);
        // license
        Map<String, Object> license = assembleLicenseInfo(data, merchantPhotoModel);
        // store
        Map<String, Object> store = assembleStoreInfo(data, districtInfoQueryResult, poiDetailQueryResult, context.getStoreExtAndPicturesQueryResult());
        return BusinessOpenBasicModel.builder()
                .merchantId(merchantId)
                .merchant(merchant)
                .account(account)
                .license(license)
                .store(store)
                .build();
    }

    private BusinessOpenResult openIndirect(BusinessOpenBasicModel basicModel, MerchantPhotoModel merchantPhotoModel, BusinessOpenAddMicroMerchantOpData data, BusinessOpenAddMerchantContext context) {
        // appinfo
        Map<String, Object> appInfo = assembleIndirectAppInfo(data, basicModel, merchantPhotoModel, context);
        // 调用业务开通的接口
        return merchantBusinessOpenClient.openApp(new BusinessOpenRequest()
                .setMerchantInfo(basicModel.getMerchant())
                .setAccountInfo(basicModel.getAccount())
                .setLicenseInfo(basicModel.getLicense())
                .setStoreInfo(basicModel.getStore())
                .setAppInfo(appInfo)
                .setAppId(indirectAppId)
                .setMerchantId(basicModel.getMerchantId())
                .setUserId(context.getKeeperUserId()));
    }

    private Map<String, Object> assembleMerchantInfo(BusinessOpenAddMicroMerchantOpData data, IndustryInfoQueryResult industryInfoQueryResult, DistrictInfoQueryResult districtInfoQueryResult, PoiDetailQueryResult poiDetailQueryResult) {
        Map<String, Object> merchant = new HashMap<>();
        merchant.put(Merchant.OWNER_CELLPHONE, data.getLoginPhone());
        merchant.put(Merchant.CUSTOMER_PHONE, data.getContactPhone());
        merchant.put(Merchant.BUSINESS, data.getBusinessName());
        merchant.put(Merchant.BUSINESS_NAME, data.getBusinessName());
        merchant.put(Merchant.INDUSTRY, industryInfoQueryResult.getIndustryId());
        merchant.put(Merchant.ALIAS, data.getBusinessName());
        merchant.put(Merchant.NAME, data.getBusinessName());
        merchant.put(Merchant.OWNER_NAME, data.getContactName());
        merchant.put(Merchant.CONTACT_NAME, data.getContactName());
        merchant.put(Merchant.CONTACT_CELLPHONE, data.getContactPhone());
        merchant.put(Merchant.PROVINCE, districtInfoQueryResult.getProvince());
        merchant.put(Merchant.CITY, districtInfoQueryResult.getCity());
        merchant.put(Merchant.DISTRICT, districtInfoQueryResult.getDistrict());
        merchant.put(Merchant.STREET_ADDRESS, data.getStreetAddress());
        merchant.put(Merchant.LONGITUDE, poiDetailQueryResult.getLongitude());
        merchant.put(Merchant.LATITUDE, poiDetailQueryResult.getLatitude());
        return merchant;
    }

    private Map<String, Object> assembleAccountInfo(BusinessOpenAddMicroMerchantOpData data) {
        // account
        UcUserInfoQueryResult ucUserInfoQueryResult = merchantUserClient.getUcUserInfoByIdentifier(data.getLoginPhone());
        Map<String, Object> account = new HashMap<>();
        account.put("uc_user_id", Objects.nonNull(ucUserInfoQueryResult) ? ucUserInfoQueryResult.getUcUserId() : null);
        account.put("identity_type", 1);
        account.put("identifier", data.getLoginPhone());
        return account;
    }

    private Map<String, Object> assembleLicenseInfo(BusinessOpenAddMicroMerchantOpData data, MerchantPhotoModel merchantPhotoModel) {
        Map<String, Object> license = new HashMap<>();
        license.put(MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_NAME, data.getHolder());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, data.getIdentity());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE, 1);
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, merchantPhotoModel.getHolderIdFrontPhoto());
        license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO, merchantPhotoModel.getHolderIdBackPhoto());
        license.put(MerchantBusinessLicence.ID_VALIDITY, data.getIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getIdValidityEnd()));
        return license;
    }

    private Map<String, Object> assembleStoreInfo(BusinessOpenAddMicroMerchantOpData data, DistrictInfoQueryResult districtInfoQueryResult, PoiDetailQueryResult poiDetailQueryResult, StoreExtAndPicturesQueryResult storeExtAndPicturesQueryResult) {
        Map<String, Object> storeInfo = new HashMap<>();
        storeInfo.put(Store.PROVINCE, districtInfoQueryResult.getProvince());
        storeInfo.put(Store.CITY, districtInfoQueryResult.getCity());
        storeInfo.put(Store.DISTRICT, districtInfoQueryResult.getDistrict());
        storeInfo.put(Store.STREET_ADDRESS, data.getStreetAddress());
        storeInfo.put(Store.LONGITUDE, poiDetailQueryResult.getLongitude());
        storeInfo.put(Store.LATITUDE, poiDetailQueryResult.getLatitude());
        storeInfo.put(Store.CONTACT_NAME, data.getContactName());
        storeInfo.put(Store.CONTACT_CELLPHONE, data.getContactPhone());
        storeInfo.put(Store.NAME, data.getBusinessName());
        storeInfo.put("brand_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getBrandPhoto().getUrl()));
        storeInfo.put("indoor_material_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getIndoorMaterialPhoto().getUrl()));
        storeInfo.put("outdoor_material_photo", CollectionUtil.hashMap("url", storeExtAndPicturesQueryResult.getOutdoorMaterialPhoto().getUrl()));
        return storeInfo;
    }

    private Map<String, Object> assembleIndirectAppInfo(BusinessOpenAddMicroMerchantOpData data, BusinessOpenBasicModel basicModel, MerchantPhotoModel merchantPhotoModel, BusinessOpenAddMerchantContext context) {
        BankInfoQueryResult bankInfoQueryResult = bankInfoClient.getBankInfoByOpeningNumber(data.getOpeningNumber());
        if (Objects.isNull(bankInfoQueryResult)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_INFO_NOT_FIND);
        }
        Map<String, Object> appInfo = assembleMerchantConfig(context, basicModel);
        appInfo.put("bank_account", CollectionUtil.hashMap(
                MerchantBankAccount.HOLDER, data.getHolder(),
                MerchantBankAccount.IDENTITY, data.getIdentity(),
                MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, merchantPhotoModel.getHolderIdFrontPhoto(),
                MerchantBankAccount.HOLDER_ID_BACK_PHOTO, merchantPhotoModel.getHolderIdBackPhoto(),
                MerchantBankAccount.ID_VALIDITY, data.getIdValidityStart() + "-" + ValidityTransferHelper.transferIdValidityEnd(data.getIdValidityEnd()),
                MerchantBankAccount.BANK_CARD_IMAGE, merchantPhotoModel.getBankCardPhoto(),
                MerchantBankAccount.NUMBER, data.getNumber(),
                MerchantBankAccount.CARD_VALIDITY, data.getCardValidity(),
                MerchantBankAccount.OPENING_NUMBER, data.getOpeningNumber(),
                MerchantBankAccount.BANK_NAME, bankInfoQueryResult.getBankName(),
                MerchantBankAccount.BRANCH_NAME, bankInfoQueryResult.getBranchName(),
                MerchantBankAccount.CARD_VALIDITY, ValidityTransferHelper.transferCardValidity(data.getCardValidity()),
                "pay_type", 1,
                MerchantBankAccount.ID_TYPE, 1
        ));
        return appInfo;
    }
}
