package com.wosai.cua.brand.business.service.config.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.SentinelRpcException;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.any;

/**
 * 继承抽象类JsonRPCFallbackDefine实现自定义处理类
 * <AUTHOR>
 */
@Slf4j
public class SentinelServiceFallbackConfig extends JsonRPCFallbackDefine {
    /**
     * 定义方法拦截处理数组
     * @return
     */
    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        // 匹配该class下任意方法
                        return any();
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        log.error("接口：{}，触发限流/熔断规则",method.getName());
                        // 默认抛出运行时异常，可自定义实现
                        throw new SentinelRpcException("系统繁忙，请稍后重试。");
                    }
                }
        };
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        // 定义拦截的rpc接口范围 ExampleService或者HelloService
        return any();
    }

    @Override
    public Provider getProvider() {
        // 定义是客户端or服务端
        return Provider.SERVER;
    }
}