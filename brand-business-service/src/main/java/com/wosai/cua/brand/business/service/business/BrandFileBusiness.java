package com.wosai.cua.brand.business.service.business;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.brand.business.api.dto.request.OssFileRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.PageRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.ImportBrandMerchantForAuditRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.ImportMerchantTaskResponseDTO;
import com.wosai.cua.brand.business.api.enums.AggregationModelEnum;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskTypeEnum;
import com.wosai.cua.brand.business.api.enums.EnrollChannelEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.UseOfFundsEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountQueryDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.domain.service.BrandTaskLogDomainService;
import com.wosai.cua.brand.business.service.domain.service.MerchantDomainService;
import com.wosai.cua.brand.business.service.domain.service.PooledMerchantAnalyzeService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountRelatedDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountSettlementCardDOService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.RegisteredUserRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.AddConcentrateRelationRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.OpenAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletPreRegisterRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateMemberRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.AddConcentrateRelationResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.OpenAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletPreRegisterResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.CreateMemberResponse;
import com.wosai.cua.brand.business.service.enums.BankCardExportEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.enums.BrandMerchantExportEnum;
import com.wosai.cua.brand.business.service.enums.RedisKeyEnum;
import com.wosai.cua.brand.business.service.enums.TaskApplyStatusEnum;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.enums.third.MemberGlobalTypeEnum;
import com.wosai.cua.brand.business.service.event.model.BrandMerchantEnrollEvent;
import com.wosai.cua.brand.business.service.event.model.MerchantOpenStatusEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.AbstractImportContext;
import com.wosai.cua.brand.business.service.excel.handler.ImportExcelHandlerFactory;
import com.wosai.cua.brand.business.service.helper.CommonHelper;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.helper.OssFileHelper;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.helper.ZipHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantDockingModeExtraModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.config.BrandConfigModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.convert.ConfigModuleConverter;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.cua.brand.business.service.module.log.QueryLogConditionsModule;
import com.wosai.cua.brand.business.service.module.log.TaskApplyLogResultModule;
import com.wosai.cua.brand.business.service.module.log.TaskContextModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.PooledMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.input.BrandMerchantInputResultModule;
import com.wosai.cua.brand.business.service.module.tripartite.fuiou.FuiouToBeActiveRecordModule;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.merchant.dmp.api.Base;
import com.wosai.sharing.proxy.model.request.OpenAggregationRequest;
import com.wosai.sharing.proxy.service.AggregationService;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BrandFileBusiness {

    private final BrandDomainService brandDomainService;

    private final BankCardDomainService bankCardDomainService;

    private final BrandTaskLogDomainService brandTaskLogDomainService;

    private final BrandConfigDomainService brandConfigDomainService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private TagIngestService tagIngestService;

    @Autowired
    private AggregationService aggregationService;
    @Autowired
    private RedissonClient redissonClient;

    private final ImportExcelHandlerFactory importExcelHandlerFactory;

    private final ApolloConfig apolloConfig;

    @Value(value = "${tmp.folder}")
    private String tmpFolder;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    @Value("${aggregation.open.notify-url}")
    private String aggregationOpenNotifyUrl;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    private final List<BrandMerchantsAnalyzeService> brandMerchantsAnalyzeServices;

    private static final Map<BrandImportSheetEnum, BrandMerchantsAnalyzeService> BRAND_MERCHANTS_ANALYZE_SERVICE_MAP = Maps.newConcurrentMap();

    private final DefaultEventPublisher defaultEventPublisher;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = Maps.newConcurrentMap();

    private final PooledMerchantAnalyzeService pooledMerchantAnalyzeService;

    private final RedisHelper redisHelper;

    private final CommonHelper commonHelper;

    private final MerchantDomainService merchantDomainService;

    private final SeparateAccountSettlementCardDOService separateAccountSettlementCardDOService;

    private final SeparateAccountRelatedDOService separateAccountRelatedDOService;

    private final SeparateAccountDOService separateAccountDOService;

    private final MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    protected CryptHelper cryptHelper;


    @Autowired
    public BrandFileBusiness(BrandDomainService brandDomainService, BankCardDomainService bankCardDomainService, BrandTaskLogDomainService brandTaskLogDomainService, BrandConfigDomainService brandConfigDomainService, ImportExcelHandlerFactory importExcelHandlerFactory, ApolloConfig apolloConfig, IdGeneratorSnowflake idGeneratorSnowflake, List<BrandMerchantsAnalyzeService> brandMerchantsAnalyzeServices, DefaultEventPublisher defaultEventPublisher, List<TripartiteSystemCallService> tripartiteSystemCallServices, PooledMerchantAnalyzeService pooledMerchantAnalyzeService, RedisHelper redisHelper, CommonHelper commonHelper, MerchantDomainService merchantDomainService, SeparateAccountSettlementCardDOService separateAccountSettlementCardDOService, SeparateAccountRelatedDOService separateAccountRelatedDOService, SeparateAccountDOService separateAccountDOService, MerchantBizBankAccountService merchantBizBankAccountService) {
        this.brandDomainService = brandDomainService;
        this.bankCardDomainService = bankCardDomainService;
        this.brandTaskLogDomainService = brandTaskLogDomainService;
        this.brandConfigDomainService = brandConfigDomainService;
        this.importExcelHandlerFactory = importExcelHandlerFactory;
        this.apolloConfig = apolloConfig;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
        this.brandMerchantsAnalyzeServices = brandMerchantsAnalyzeServices;
        this.defaultEventPublisher = defaultEventPublisher;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.pooledMerchantAnalyzeService = pooledMerchantAnalyzeService;
        this.redisHelper = redisHelper;
        this.commonHelper = commonHelper;
        this.merchantDomainService = merchantDomainService;
        this.separateAccountSettlementCardDOService = separateAccountSettlementCardDOService;
        this.separateAccountRelatedDOService = separateAccountRelatedDOService;
        this.separateAccountDOService = separateAccountDOService;
        this.merchantBizBankAccountService = merchantBizBankAccountService;
    }

    @PostConstruct
    public void initBrandMerchantsAnalyzeServiceMap() {
        Map<BrandImportSheetEnum, BrandMerchantsAnalyzeService> analyzeServiceMap =
                brandMerchantsAnalyzeServices.stream().collect(Collectors.toMap(BrandMerchantsAnalyzeService::getSheetEnum, Function.identity()));
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.putAll(analyzeServiceMap);
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    @Transactional(rollbackFor = Exception.class)
    public void importBrandMerchantsByExcel(OssFileRequestDTO ossFileRequest) {
        long taskId = idGeneratorSnowflake.nextId();
        BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
        brandTaskLogModule.setTaskId(taskId);
        brandTaskLogModule.setPlatform("SPA");
        brandTaskLogModule.setTaskType(BrandTaskTypeEnum.MERCHANT_IMPORT.getTaskType());
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.EXECUTION.getTaskStatus());
        brandTaskLogModule.setBrandId(ossFileRequest.getBrandId());
        String date = new SimpleDateFormat(TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT).format(new Date());
        String[] split = ossFileRequest.getOssKey().split("/");
        String completeFileName = split[split.length - 1];
        String[] strings = completeFileName.split("\\.");
        String fileName = strings[0] + "-" + date + "." + strings[strings.length - 1];
        String filePath = tmpFolder + fileName;
        brandTaskLogModule.setTaskName(fileName);
        Long id = brandTaskLogDomainService.insertBrandTaskLog(brandTaskLogModule);
        brandTaskLogModule.setId(id);
        ZipHelper.deletefile(filePath);
        try {
            OssFileHelper.downloadFile(ossFileRequest.getBucket(), ossFileRequest.getOssKey(), filePath);
            BrandMerchantInputResultModule resultModule = ((BrandFileBusiness) AopContext.currentProxy()).importBrandMerchants(ossFileRequest, ossFileRequest.getStrategyId(), filePath, String.valueOf(taskId));
            OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + fileName, new File(filePath));
            String url = OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + fileName);
            brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandTaskLogModule.setTaskResult(JSON.toJSONString(
                    TaskApplyLogResultModule.builder()
                            .result("导入成功：" + resultModule.getSuccess() + "条！\n导入失败：" + resultModule.getFailed() + "条")
                            .downloadUrl(url).build()));
            brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        } catch (Exception e) {
            log.error("任务：【{}】，导入异常！", taskId, e);
            brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandTaskLogModule.setTaskResult(JSON.toJSONString(TaskApplyLogResultModule.builder().result("批量导入数据异常")));
            brandTaskLogModule.setErrorLog(e.getMessage());
            brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
        } finally {
            ZipHelper.deletefile(filePath);
        }

        // 发送开户信息通知
        ThreadPoolHelper.execute(() -> submitOpenedMerchantInfo(String.valueOf(taskId), ossFileRequest.getBrandId()));
    }

    public BrandMerchantInputResultModule importBrandMerchants(OssFileRequestDTO ossFileRequest, Long strategyId, String filePath, String transactionId) {
        BrandMerchantInputResultModule resultModule = new BrandMerchantInputResultModule();
        try {
            BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(ossFileRequest.getBrandId());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
            }
            BrandConfigModule brandConfig = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
            // 导入分账类型的商户
            if (BrandMerchantDockingModeEnum.SEPARATE_ACCOUNT.equals(ossFileRequest.getDockingMode()) && Objects.nonNull(ossFileRequest.getStrategyId())) {
                return this.getBrandMerchantInputResultModule(ossFileRequest.getBrandId(), filePath, strategyId, brandModule, brandConfig, resultModule, transactionId);
            }
            if (BrandMerchantDockingModeEnum.COLLECTION.equals(ossFileRequest.getDockingMode())) {
                // 导入归集的商户
                return this.getCollectionBrandMerchantInputResultModule(ossFileRequest, filePath, brandModule, brandConfig, resultModule);
            }
        } catch (Exception e) {
            log.error("excel解析异常", e);
            if (e instanceof BrandBusinessException) {
                throw e;
            }
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR.getCode(), "excel解析异常");
        } finally {
            ThreadLocalHelper.removeThreadLocal();
        }
        return resultModule;
    }

    /**
     * 归集商户（富友）
     */
    private BrandMerchantInputResultModule getCollectionBrandMerchantInputResultModule(OssFileRequestDTO ossFileRequest, String filePath, BrandModule brandModule, BrandConfigModule brandConfig, BrandMerchantInputResultModule resultModule) {
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        if (configModule == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.CONFIG_MODULE_NOT_BE_NULL);
        }
        int total = 0;
        int passCheck = 0;
        Map<Integer, List<String[]>> integerListMap = FileHelper.readXlsx(filePath);
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        List<PooledMerchantAnalyzeModule> merchantAnalyzeModules = pooledMerchantAnalyzeService.analyzeData(integerListMap.get(0));
        total += merchantAnalyzeModules.size();
        pooledMerchantAnalyzeService.checkParams(merchantAnalyzeModules);
        brandMerchantModules.addAll(pooledMerchantAnalyzeService.getBrandMerchantModuleList(brandModule.getBrandId(), merchantAnalyzeModules));
        passCheck += brandMerchantModules.size();
        brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setExtra(BrandMerchantExtraModule.builder().dockingModeExtraModule(
                BrandMerchantDockingModeExtraModule.builder().mobile(brandMerchantModule.getNotifyMobile()).aggregationModel(ossFileRequest.getAggregationModel().getCode()).concentrateScale(ossFileRequest.getConcentrateScale()).useOfFunds(ossFileRequest.getUseOfFunds().getCode()).build()
        ).build()));
        brandDomainService.bathCreateBrandMerchant(brandMerchantModules);
        this.invokeService(brandModule, brandMerchantModules, configModule);
        // 打标签
        brandMerchantModules.forEach(brandMerchantModule -> tagIngestService.ingest(Lists.newArrayList(brandMerchantModule.getMerchantId()), apolloConfig.getCollectionModeMerchantTagId(), true));
        pooledMerchantAnalyzeService.createErrorMsgIntoExcel(filePath);
        resultModule.setSuccess(passCheck);
        resultModule.setFailed(total - passCheck);
        return resultModule;
    }

    private void invokeService(BrandModule brandModule, List<BrandMerchantModule> brandMerchantModules, ConfigModule configModule) {
        brandMerchantModules.forEach(brandMerchantModule -> {
            if (Objects.isNull(configModule)) {
                return;
            }

            MerchantInfo merchant = merchantService.getMerchantBySn(
                    brandMerchantModule.getMerchantSn(), null
            );
            if (Objects.isNull(merchant)) {
                return;
            }
            MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
            if (Objects.isNull(merchantBusinessLicense)) {
                return;
            }
            UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
            if (Objects.isNull(superAdmin)) {
                return;
            }

            BrandMerchantCreationRecordModule recordModule = new BrandMerchantCreationRecordModule();
            recordModule.setBrandId(brandModule.getBrandId());
            recordModule.setMerchantId(merchant.getId());
            recordModule.setMerchantSn(merchant.getSn());
            recordModule.setRecordId(merchant.getSn() + System.currentTimeMillis());
            if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
                this.invokeServiceHandleCollection(brandModule, brandMerchantModule, configModule, recordModule);
                brandDomainService.updateBrandMerchant(brandMerchantModule);
                brandDomainService.updateCreateBrandMerchantCreationRecord(recordModule);
            }
        });
    }

    private void invokeServiceHandleCollection(
            BrandModule brandModule,
            BrandMerchantModule brandMerchantModule,
            ConfigModule configModule,
            BrandMerchantCreationRecordModule recordModule) {
        if (!brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
            brandDomainService.updateBrandMerchant(brandMerchantModule);
            brandDomainService.updateCreateBrandMerchantCreationRecord(recordModule);
            return;
        }
        if (!brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType()) || brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            BrandMerchantDockingModeExtraModule dockingModeExtraModule = brandMerchantModule.getExtra().getDockingModeExtraModule();
            AddConcentrateRelationRequest request = new AddConcentrateRelationRequest(((FuiouConfigModule) configModule).getMerchantNo());
            request.getBody().setCheckType(fuiouCheckType);
            request.getBody().setBalanceConcentrateScale(String.valueOf(dockingModeExtraModule.getConcentrateScale() * 100));
            request.getBody().setTraceNo(recordModule.getRecordId());
            request.getBody().setUseType(UseOfFundsEnum.getFyCodeByCode(dockingModeExtraModule.getUseOfFunds()));
            request.getBody().setMchntCdConcentrate(brandMerchantModule.getMemberId());
            request.getBody().setConcentrateTypes(Lists.newArrayList(AggregationModelEnum.BALANCE_POOLING.getFyCode()));
            request.getBody().setMobile(brandMerchantModule.getNotifyMobile());
            try {
                AddConcentrateRelationResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, AddConcentrateRelationResponse.class, configModule);
                if (response == null) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用富友接口申请归集授权未响应");
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    return;
                }
                if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用富友接口申请归集授权失败," + response.getResultMsg());
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                    return;
                }
                recordModule.setRecordId(response.getBody().getBatchNo());
                if (Objects.nonNull(brandMerchantModule.getExtra()) && Objects.nonNull(brandMerchantModule.getExtra().getDockingModeExtraModule())) {
                    brandMerchantModule.getExtra().getDockingModeExtraModule().setBatchNo(response.getBody().getBatchNo());
                }
                brandMerchantModule.setAccountOpenFailureReason(response.getBody().getCheckUrl());
            } catch (Exception e) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口申请归集授权异常," + e.getMessage());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(e.getMessage());
                return;
            }
            recordModule.setStatus(3);
            recordModule.setResult("归集授权申请请求成功，等待富友回调！");
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.PENDING.getStatus());

        }
    }

    private BrandMerchantInputResultModule getBrandMerchantInputResultModule(String brandId, String filePath, Long strategyId, BrandModule brandModule, BrandConfigModule brandConfig, BrandMerchantInputResultModule resultModule, String transactionId) {
        // 获取配置
        ConfigModule configModule = ConfigModuleConverter.convert(brandModule.getFundManagementCompanyCode(), brandConfig.getConfig());
        int total = 0;
        int passCheck = 0;
        Map<Integer, List<String[]>> integerListMap = FileHelper.readXlsx(filePath);
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        List<BankCardModule> bankCardModules = Lists.newArrayList();
        // 处理企业
        List<BaseMerchantAnalyzeModule> companyMerchantAnalyzeModules = BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).analyzeData(integerListMap.get(BrandImportSheetEnum.COMPANY.getSheet()), brandModule.getFundManagementCompanyCode());
        total += companyMerchantAnalyzeModules.size();
        // 校验字段
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).checkParams(companyMerchantAnalyzeModules);
        // 校验身份证号格式
        if (Boolean.TRUE.equals(apolloConfig.getCheckValidChineseIdSwitch())) {
            BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).checkIsValidChineseID(companyMerchantAnalyzeModules);
        }
        brandMerchantModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).getBrandMerchantModuleList(brandId, companyMerchantAnalyzeModules, strategyId));
        companyMerchantAnalyzeModules.removeIf(companyMerchantAnalyzeModule -> StringUtils.isBlank(companyMerchantAnalyzeModule.getMerchantId()));
        bankCardModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).getBankCarModules(brandId, brandModule.getFundManagementCompanyCode(), companyMerchantAnalyzeModules));
        passCheck += companyMerchantAnalyzeModules.size();
        // 处理个人/小微
        List<BaseMerchantAnalyzeModule> personalMerchantAnalyzeModules = BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).analyzeData(integerListMap.get(BrandImportSheetEnum.PERSONAL.getSheet()), brandModule.getFundManagementCompanyCode());
        total += personalMerchantAnalyzeModules.size();
        // 校验字段
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).checkParams(personalMerchantAnalyzeModules);
        if (Boolean.TRUE.equals(apolloConfig.getCheckValidChineseIdSwitch())) {
            BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).checkIsValidChineseID(personalMerchantAnalyzeModules);
        }
        brandMerchantModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).getBrandMerchantModuleList(brandId, personalMerchantAnalyzeModules, strategyId));
        personalMerchantAnalyzeModules.removeIf(personalMerchantAnalyzeModule -> StringUtils.isBlank(personalMerchantAnalyzeModule.getMerchantId()));
        bankCardModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).getBankCarModules(brandId, brandModule.getFundManagementCompanyCode(), personalMerchantAnalyzeModules));
        personalMerchantAnalyzeModules.removeIf(BaseMerchantAnalyzeModule::isNeedRemove);
        passCheck += personalMerchantAnalyzeModules.size();
        // 处理个体工商户
        List<BaseMerchantAnalyzeModule> individualBusinessMerchantAnalyzeModules = BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).analyzeData(integerListMap.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS.getSheet()), brandModule.getFundManagementCompanyCode());
        total += individualBusinessMerchantAnalyzeModules.size();
        // 校验字段
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).checkParams(individualBusinessMerchantAnalyzeModules);
        if (Boolean.TRUE.equals(apolloConfig.getCheckValidChineseIdSwitch())) {
            BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).checkIsValidChineseID(individualBusinessMerchantAnalyzeModules);
        }
        brandMerchantModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).getBrandMerchantModuleList(brandId, individualBusinessMerchantAnalyzeModules, strategyId));
        individualBusinessMerchantAnalyzeModules.removeIf(individualBusinessMerchantAnalyzeModule -> StringUtils.isBlank(individualBusinessMerchantAnalyzeModule.getMerchantId()));
        bankCardModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).getBankCarModules(brandId, brandModule.getFundManagementCompanyCode(), individualBusinessMerchantAnalyzeModules));
        individualBusinessMerchantAnalyzeModules.removeIf(BaseMerchantAnalyzeModule::isNeedRemove);
        passCheck += individualBusinessMerchantAnalyzeModules.size();
        // 处理收钱吧商户导入
        List<BaseMerchantAnalyzeModule> alreadyExistMerchantAnalyzeModules = BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT).analyzeData(integerListMap.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT.getSheet()), brandModule.getFundManagementCompanyCode());
        total += alreadyExistMerchantAnalyzeModules.size();
        // 校验字段
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT).checkParams(alreadyExistMerchantAnalyzeModules);
        brandMerchantModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT).getBrandMerchantModuleList(brandId, alreadyExistMerchantAnalyzeModules, strategyId));
        alreadyExistMerchantAnalyzeModules.removeIf(alreadyExistAnalyzeMerchant -> StringUtils.isBlank(alreadyExistAnalyzeMerchant.getMerchantId()));
        bankCardModules.addAll(BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT).getBankCarModules(brandId, brandModule.getFundManagementCompanyCode(), alreadyExistMerchantAnalyzeModules));
        passCheck += alreadyExistMerchantAnalyzeModules.size();
        Map<String, BrandMerchantModule> uniqueExistMerchantMap = brandMerchantModules.stream()
                .collect(Collectors.toMap(BrandMerchantModule::getMerchantId, Function.identity(), (existing, replacement) -> existing, LinkedHashMap::new));
        brandMerchantModules.clear();
        brandMerchantModules.addAll(uniqueExistMerchantMap.values());
        this.filterBankCardModule(bankCardModules, brandMerchantModules, brandModule);
        if (Boolean.TRUE.equals(apolloConfig.getAutoOpenSubAccountsSwitch())) {
            Map<String, List<BankCardModule>> merchantIdBankCardModuleMap = bankCardModules.stream().collect(Collectors.groupingBy(BankCardModule::getMerchantId));
            this.invokeService(brandModule, brandMerchantModules, configModule, merchantIdBankCardModuleMap);
        } else {
            brandMerchantModules.forEach(brandMerchantModule -> brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.HAVE_NOT_OPENED.getStatus()));
        }
        brandDomainService.bathCreateBrandMerchant(brandMerchantModules);
        bankCardDomainService.batchCreateBankCards(bankCardModules);
        createSeparateAccountByBrandMerchantModules(brandId, brandMerchantModules, bankCardModules);
        // 处理校验报错不通过回写excel的逻辑
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.COMPANY).createErrorMsgIntoExcel(filePath);
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.INDIVIDUAL_BUSINESS).createErrorMsgIntoExcel(filePath);
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.PERSONAL).createErrorMsgIntoExcel(filePath);
        BRAND_MERCHANTS_ANALYZE_SERVICE_MAP.get(BrandImportSheetEnum.ALREADY_EXIST_MERCHANT).createErrorMsgIntoExcel(filePath);
        resultModule.setSuccess(passCheck);
        resultModule.setFailed(total - passCheck);
        redisHelper.saveAllToSet(String.format(RedisKeyEnum.IMPORT_MERCHANT_TASK_TRANSACTION.getKey(), transactionId, brandId), brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList()));
        redisHelper.setExpireTime(String.format(RedisKeyEnum.IMPORT_MERCHANT_TASK_TRANSACTION.getKey(), transactionId, brandId), apolloConfig.getImportMerchantTaskTransactionKeyExpireTime());
        return resultModule;
    }

    private void createSeparateAccountByBrandMerchantModules(String brandId, List<BrandMerchantModule> brandMerchantModules, List<BankCardModule> bankCardModules) {
        if (!Boolean.TRUE.equals(apolloConfig.getSynchronizeSeparateAccountFlag())) {
            return;
        }
        Map<String, BrandMerchantModule> brandMerchantModuleMap = brandMerchantModules.stream()
                .collect(Collectors.toMap(
                        BrandMerchantModule::getMerchantId,
                        merchant -> merchant,
                        (existing, replacement) -> existing // 处理重复的merchantId，保留第一个
                ));
        Set<String> merchantIdSet = brandMerchantModuleMap.keySet();
        if (CollectionUtils.isEmpty(merchantIdSet)) {
            return;
        }
        List<MerchantInfo> merchantListByMerchantIds = merchantService.getMerchantListByMerchantIds(Lists.newArrayList(merchantIdSet));
        if (CollectionUtils.isEmpty(merchantListByMerchantIds)) {
            return;
        }
        Map<String, MerchantInfo> merchantInfoMap = merchantListByMerchantIds.stream().collect(Collectors.toMap(MerchantInfo::getId, merchant -> merchant));
        Map<String, List<BankCardModule>> bankCardModulesListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(bankCardModules)) {
            bankCardModulesListMap.putAll(bankCardModules.stream().collect(Collectors.groupingBy(BankCardModule::getMerchantId)));
        }
        createSeparateAccountInfo(brandId, brandMerchantModuleMap, merchantInfoMap, bankCardModulesListMap);
    }

    /**
     * 初始化分离账户信息
     *
     * @param brandId                品牌ID
     * @param brandMerchantModuleMap 品牌商户Map
     * @param merchantInfoMap        商户信息Map
     */
    private void createSeparateAccountInfo(String brandId, Map<String, BrandMerchantModule> brandMerchantModuleMap,
                                           Map<String, MerchantInfo> merchantInfoMap, Map<String, List<BankCardModule>> bankCardModulesListMap) {
        brandMerchantModuleMap.forEach((merchantId, brandMerchantModule) -> {
            // 获取商户信息
            MerchantInfo merchantInfo = merchantInfoMap.get(merchantId);
            if (Objects.isNull(merchantInfo)) {
                return;
            }
            SeparateAccountDO separateAccountDO = new SeparateAccountDO();
            separateAccountDO.setBrandId(brandId);
            separateAccountDO.setAccountName(merchantInfo.getName());
            separateAccountDO.setAccountNumber(brandMerchantModule.getMerchantSn());
            separateAccountDO.setAccountType(SeparateAccountTypeEnum.getSeparateAccountTypeEnumByMerchantTypeEnum(MerchantTypeEnum.getEnumByMerchantType(brandMerchantModule.getMerchantType())).getCode());
            separateAccountDO.setType(brandMerchantModule.getType());
            separateAccountDO.setContractName(merchantInfo.getContact_name());
            separateAccountDO.setContractPhone(merchantInfo.getContact_cellphone());
            separateAccountDO.setSubAccountNo(brandMerchantModule.getSubAccountNo());
            separateAccountDO.setMemberId(brandMerchantModule.getMemberId());
            separateAccountDO.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
            separateAccountDO.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
            separateAccountDO.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
            separateAccountDO.setStrategyId(brandMerchantModule.getStrategyId());
            // 获取营业执照信息
            try {
                MerchantBusinessLicenseInfo merchantBusinessLicense =
                        merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
                if (Objects.nonNull(merchantBusinessLicense)) {
                    switch (merchantBusinessLicense.getType()) {
                        //个人
                        case 0:
                            separateAccountDO.setIdType(MemberGlobalTypeEnum.getSeparateAccountTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
                            separateAccountDO.setIdNumber(merchantBusinessLicense.getLegal_person_id_number());
                            separateAccountDO.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
                            separateAccountDO.setLegalPersonIdType(MemberGlobalTypeEnum.getSeparateAccountTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
                            separateAccountDO.setLegalPersonId(merchantBusinessLicense.getLegal_person_id_number());
                            separateAccountDO.setLegalPersonPhone(merchantInfo.getContact_phone());
                            break;
                        case 1:
                        case 2:
                            separateAccountDO.setIdType("99");
                            separateAccountDO.setIdNumber(merchantBusinessLicense.getNumber());
                            separateAccountDO.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
                            separateAccountDO.setLegalPersonIdType(MemberGlobalTypeEnum.getSeparateAccountTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
                            separateAccountDO.setLegalPersonId(merchantBusinessLicense.getLegal_person_id_number());
                            separateAccountDO.setLegalPersonPhone(merchantInfo.getContact_phone());
                            break;
                        default:
                    }
                }
                separateAccountDOService.addSeparateAccount(separateAccountDO);
                List<BankCardModule> bankCardModuleList = bankCardModulesListMap.get(merchantId);
                if (CollectionUtils.isNotEmpty(bankCardModuleList)) {
                    bankCardModuleList.forEach(bankCardModule -> {
                        BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardModule.getBankCardId());
                        if (Objects.nonNull(bankAccount)) {
                            SeparateAccountSettlementCardDO separateAccountSettlementCardDO = new SeparateAccountSettlementCardDO();
                            separateAccountSettlementCardDO.setAccountNumber(separateAccountDO.getAccountNumber());
                            separateAccountSettlementCardDO.setHolder(bankAccount.getHolder());
                            separateAccountSettlementCardDO.setType(bankAccount.getType());
                            separateAccountSettlementCardDO.setCardNumber(bankAccount.getNumber());
                            separateAccountSettlementCardDO.setBankName(bankAccount.getBankName());
                            separateAccountSettlementCardDO.setBranchName(bankAccount.getBranchName());
                            separateAccountSettlementCardDO.setClearingNumber(bankAccount.getClearingNumber());
                            separateAccountSettlementCardDO.setOpeningNumber(bankAccount.getOpeningNumber());
                            separateAccountSettlementCardDO.setCellphone(cryptHelper.decrypt(bankCardModule.getReservedMobileNumber()));
                            separateAccountSettlementCardDO.setDefaultStatus(Boolean.TRUE.equals(bankCardModule.getIsDefault()) ? 1 : 0);
                            separateAccountSettlementCardDO.setActiveStatus(bankCardModule.getStatus());
                            separateAccountSettlementCardDO.setActiveFailReason(bankCardModule.getActivateFailReason());
                            separateAccountSettlementCardDO.setCtime(bankCardModule.getCreatedTime());
                            separateAccountSettlementCardDO.setMtime(bankCardModule.getUpdatedTime());
                            separateAccountSettlementCardDO.setThirdBankCardId(bankCardModule.getThirdBankCardId());
                            separateAccountSettlementCardDOService.add(separateAccountSettlementCardDO);
                        }
                    });
                }
                List<SeparateAccountRelatedDO> separateAccountRelatedDOList = Lists.newArrayList();
                separateAccountRelatedDOList.add(
                        SeparateAccountRelatedDO.builder()
                                .brandId(brandId)
                                .accountNumber(separateAccountDO.getAccountNumber())
                                .type(SeparateAccountRelateTypeEnum.MERCHANT_SN.getType())
                                .relatedSn(merchantInfo.getSn())
                                .build()
                );
                if (StringUtils.isNotBlank(brandMerchantModule.getOutMerchantNo())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.OUT_MERCHANT.getType())
                            .relatedSn(brandMerchantModule.getOutMerchantNo())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getSqbStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.STORE_SN.getType())
                            .relatedSn(brandMerchantModule.getSqbStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedSqbStoreId())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.STORE_ID.getType())
                            .relatedSn(brandMerchantModule.getAssociatedSqbStoreId())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedElmStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.ELM_STORE.getType())
                            .relatedSn(brandMerchantModule.getAssociatedElmStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedMeituanStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.MT_STORE.getType())
                            .relatedSn(brandMerchantModule.getAssociatedMeituanStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getDyStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.DY_STORE.getType())
                            .relatedSn(brandMerchantModule.getDyStoreSn())
                            .build()
                    );
                }
                separateAccountRelatedDOService.saveBatch(separateAccountRelatedDOList);
            } catch (Exception e) {
                log.error("initSeparateAccountInfo error {}", JSON.toJSONString(brandMerchantModule), e);
            }
        });
    }

    private void filterBankCardModule(List<BankCardModule> bankCardModules, List<BrandMerchantModule> brandMerchantModules, BrandModule brandModule) {
        brandMerchantModules.forEach(brandMerchantModule -> {
            if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType()) && brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
                bankCardModules.removeIf(bankCardModule -> brandMerchantModule.getMerchantId().equals(bankCardModule.getMerchantId()) && bankCardModule.getBrandId().equals(brandModule.getBrandId()));
            }
        });
    }


    private void invokeService(BrandModule brandModule, List<BrandMerchantModule> brandMerchantModules, ConfigModule configModule, Map<String, List<BankCardModule>> merchantIdBankCardModuleMap) {
        brandMerchantModules.forEach(brandMerchantModule -> {
            log.info("invokeService status:{}，subAccountNo:{}，sameBatchImportAndSameIdNumberMerchant:{}，merchantSn:{}", brandMerchantModule.getAccountOpenStatus(), brandMerchantModule.getSubAccountNo(), brandMerchantModule.isSameBatchImportAndSameIdNumberMerchant(), brandMerchantModule.getMerchantSn());
            if (brandMerchantModule.isSameBatchImportAndSameIdNumberMerchant()) {
                Optional<BrandMerchantModule> any = brandMerchantModules.stream().filter(module -> !module.isSameBatchImportAndSameIdNumberMerchant() && module.getMerchantSn().equals(brandMerchantModule.getSameBatchMerchantSn())).findAny();
                if (any.isPresent()) {
                    brandMerchantModule.setBankCardActivateStatus(any.get().getBankCardActivateStatus());
                    brandMerchantModule.setSubAccountNo(any.get().getSubAccountNo());
                    brandMerchantModule.setSubAccountName(any.get().getSubAccountName());
                    brandMerchantModule.setAccountOpenStatus(any.get().getAccountOpenStatus());
                    brandMerchantModule.setAccountOpenedTime(any.get().getAccountOpenedTime());
                    brandMerchantModule.setAccountOpenFailureReason(any.get().getAccountOpenFailureReason());
                    brandMerchantModule.setMemberId(any.get().getMemberId());
                    return;
                }
            }
            boolean jump = (
                    (
                            BrandMerchantAccountOpenStatusEnum.OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                                    || BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                                    || BrandMerchantAccountOpenStatusEnum.IN_OPENING.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                    ) && StringUtils.isNotBlank(brandMerchantModule.getSubAccountNo())
            );
            if (jump) {
                log.info("满足条件，status:{}，subAccountNo:{}，sameBatchImportAndSameIdNumberMerchant:{}，不处理该数据调用资管机构接口。{}", brandMerchantModule.getAccountOpenStatus(), brandMerchantModule.getSubAccountNo(), brandMerchantModule.isSameBatchImportAndSameIdNumberMerchant(), brandMerchantModule.getMerchantSn());
                return;
            }
            List<BankCardModule> bankCardModules = merchantIdBankCardModuleMap.get(brandMerchantModule.getMerchantId());
            if (CollectionUtils.isNotEmpty(bankCardModules)) {
                brandMerchantModule.setBankCardInfo(new BrandMerchantModule.BankCardInfo());
                bankCardModules.stream().filter(bankCardModule -> Boolean.TRUE.equals(bankCardModule.getIsDefault())).findFirst().ifPresent(bankCardModule -> {
                    brandMerchantModule.getBankCardInfo().setBankCardNo(bankCardModule.getBankCardNo());
                    brandMerchantModule.getBankCardInfo().setOpeningBankNumber(bankCardModule.getOpeningBankNumber());
                    brandMerchantModule.getBankCardInfo().setMobile(bankCardModule.getReservedMobileNumber());
                    brandMerchantModule.getBankCardInfo().setHolder(bankCardModule.getHolder());
                    brandMerchantModule.getBankCardInfo().setType(bankCardModule.getAccountType());
                });
            }
            this.invokePartnerService(brandModule, brandMerchantModule, configModule);
            if (CollectionUtils.isNotEmpty(bankCardModules) && StringUtils.isNoneBlank(brandMerchantModule.getMemberId())) {
                bankCardModules.stream().filter(bankCardModule -> Boolean.TRUE.equals(bankCardModule.getIsDefault())).findFirst().ifPresent(bankCardModule -> bankCardModule.setMemberId(brandMerchantModule.getMemberId()));
            }
            boolean b = BrandMerchantAccountOpenStatusEnum.OPENED.getStatus().equals(brandMerchantModule.getAccountOpenStatus()) && Objects.nonNull(brandMerchantModule.getIsAlreadyExistMerchantImport()) && brandMerchantModule.getIsAlreadyExistMerchantImport() && Objects.nonNull(configModule.getNeedSpecialTreatment()) && configModule.getNeedSpecialTreatment();
            if (b) {
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.IN_OPENING.getStatus());
            }
        });
    }

    private void updateSameSeparateAccountsAndMerchantsInfo(BrandMerchantModule brandMerchantModule, BrandModule brandModule, MerchantBusinessLicenseInfo merchantBusinessLicense) {
        List<SeparateAccountDO> separateAccountList;
        SeparateAccountQueryDO separateAccountQueryDO = new SeparateAccountQueryDO();
        switch (merchantBusinessLicense.getType()) {
            case 0:
                separateAccountQueryDO.setIdNumber(merchantBusinessLicense.getLegal_person_id_number());
                separateAccountQueryDO.setBrandId(brandModule.getBrandId());
                separateAccountQueryDO.setIdType(MemberGlobalTypeEnum.getSeparateAccountTypeBySqbType(merchantBusinessLicense.getLegal_person_id_type()));
                separateAccountList = separateAccountDOService.getSeparateAccountListByBrandIdAndIdNumber(separateAccountQueryDO);
                break;
            case 1:
            case 2:
                separateAccountQueryDO.setIdNumber(merchantBusinessLicense.getNumber());
                separateAccountQueryDO.setBrandId(brandModule.getBrandId());
                separateAccountQueryDO.setIdType("99");
                separateAccountList = separateAccountDOService.getSeparateAccountListByBrandIdAndIdNumber(separateAccountQueryDO);
                break;
            default:
                separateAccountList = Lists.newArrayList();
                break;
        }
        if (CollectionUtils.isNotEmpty(separateAccountList)) {
            Map<String, List<SeparateAccountRelatedDO>> separateAccountRelatedMap = separateAccountRelatedDOService.getSeparateAccountRelatedMap(separateAccountList.stream().map(SeparateAccountDO::getAccountNumber).collect(Collectors.toList()));
            separateAccountList.forEach(separateAccount -> {
                if (StringUtils.isNotBlank(separateAccount.getSubAccountNo())) {
                    log.info("已有子账户，不覆盖");
                    return;
                }
                separateAccount.setSubAccountNo(brandMerchantModule.getSubAccountNo());
                separateAccount.setMemberId(brandMerchantModule.getMemberId());
                separateAccount.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
                separateAccount.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
                separateAccount.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
                separateAccountDOService.updateSeparateAccount(separateAccount);
                List<SeparateAccountRelatedDO> separateAccountRelatedList = separateAccountRelatedMap.get(separateAccount.getAccountNumber());
                if (CollectionUtils.isNotEmpty(separateAccountRelatedList)) {
                    List<SeparateAccountRelatedDO> collect = separateAccountRelatedList.stream().filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.MERCHANT_SN.getType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        collect.forEach(separateAccountRelatedDO -> {
                            BrandMerchantModule brandMerchantByBrandIdAndMerchantSn = brandDomainService.getBrandMerchantByBrandIdAndMerchantSn(brandModule.getBrandId(), separateAccountRelatedDO.getRelatedSn());
                            if (Objects.nonNull(brandMerchantByBrandIdAndMerchantSn)) {
                                brandMerchantByBrandIdAndMerchantSn.setSubAccountNo(brandMerchantModule.getSubAccountNo());
                                brandMerchantByBrandIdAndMerchantSn.setMemberId(brandMerchantModule.getMemberId());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
                                brandDomainService.updateBrandMerchant(brandMerchantByBrandIdAndMerchantSn);
                            }
                        });
                    }
                }
            });
        }
    }

    private void invokePartnerService(
            BrandModule brandModule,
            BrandMerchantModule brandMerchantModule,
            ConfigModule configModule) {
        if (Objects.isNull(configModule)) {
            return;
        }

        MerchantInfo merchant = merchantService.getMerchantBySn(
                brandMerchantModule.getMerchantSn(), null
        );
        if (Objects.isNull(merchant)) {
            return;
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), null);
        if (Objects.isNull(merchantBusinessLicense)) {
            return;
        }
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchant.getId());
        if (Objects.isNull(superAdmin)) {
            return;
        }

        BrandMerchantCreationRecordModule recordModule = new BrandMerchantCreationRecordModule();
        recordModule.setBrandId(brandModule.getBrandId());
        recordModule.setMerchantId(merchant.getId());
        recordModule.setMerchantSn(merchant.getSn());
        recordModule.setRecordId(UUID.randomUUID().toString().replace("-", ""));
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                this.invokePabService(brandModule, brandMerchantModule, merchantBusinessLicense, merchant, configModule, recordModule);
                break;
            case MY_BANK:
                this.invokeMyBankService(merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                break;
            case CITIC:
                this.invokeCiticService(brandModule, merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                break;
            case FUIOU:
                this.createFuiouMember(merchant, merchantBusinessLicense, recordModule, configModule, brandMerchantModule);
                break;
            default:
                break;
        }
        this.updateSameSeparateAccountsAndMerchantsInfo(brandMerchantModule, brandModule, merchantBusinessLicense);
    }

    private void invokeCiticService(BrandModule brandModule, MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (configModule instanceof CiticBankConfigModule) {
            CiticBankConfigModule citicBankConfigModule = (CiticBankConfigModule) configModule;
            try {
                if (StringUtils.isBlank(citicBankConfigModule.getMerchantId()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKey()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKeyPassword()) || StringUtils.isBlank(citicBankConfigModule.getPublicKey())) {
                    recordModule.setResult("中信配置信息异常");
                    recordModule.setStatus(0);
                    return;
                }
                RegisteredUserRequest request = RegisteredUserRequest.build(citicBankConfigModule, merchant, merchantBusinessLicense, recordModule, apolloConfig.getCiticUserTypeMap().getString(brandMerchantModule.getMerchantType()));
                RegisteredResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(request, RegisteredResponse.class, citicBankConfigModule);
                // 调用失败
                if (response == null) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用中信银行接口未响应");
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandDomainService.createBrandMerchantCreationRecord(recordModule);
                    return;
                }
                if (response.getResultCode().equals(TripartiteSystemCallResponse.ResultCodeEnum.FAIL)) {
                    recordModule.setResult(response.getResultMsg());
                    recordModule.setStatus(0);
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                    brandDomainService.createBrandMerchantCreationRecord(recordModule);
                    return;
                }
                //调用成功回写会员memberId和子账号
                brandMerchantModule.setMemberId(response.getUserId());
                brandMerchantModule.setSubAccountNo(response.getUserId());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                brandMerchantModule.setAccountOpenedTime(new Date());
                recordModule.setStatus(1);
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                // 事务提交后执行的业务代码
                defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.CITIC).build());
            } catch (Exception e) {
                log.error("中信系统调用异常。", e);
                recordModule.setResult("调用中信系统异常");
                recordModule.setStatus(0);
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason("调用CICIT系统异常：" + e.getMessage());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
            }
        }
    }

    private void invokePabService(BrandModule brandModule, BrandMerchantModule brandMerchantModule, MerchantBusinessLicenseInfo merchantBusinessLicense, MerchantInfo merchant, ConfigModule configModule, BrandMerchantCreationRecordModule recordModule) {
        if (configModule instanceof PabConfigModule) {
            PabConfigModule pabConfigModule = (PabConfigModule) configModule;
            try {
                CreateMemberRequest createMemberRequest = VfinanceInterfaceService.getCreateMemberRequest(merchantBusinessLicense, merchant, pabConfigModule);
                CreateMemberResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.PAB).call(createMemberRequest, CreateMemberResponse.class, pabConfigModule);
                // 调用失败
                if (Objects.isNull(response) || VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                    log.warn("调用维金接口失败！返回值为：{}", JSON.toJSONString(response));
                    recordModule.setResult(response.getErrorMessage());
                    recordModule.setStatus(0);
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchantModule.setAccountOpenFailureReason(response.getErrorMessage());
                    brandDomainService.createBrandMerchantCreationRecord(recordModule);
                    return;
                }
                //调用成功回写会员memberId和子账号
                brandMerchantModule.setMemberId(response.getMemberId());
                brandMerchantModule.setSubAccountNo(response.getMerchantAccountId());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                recordModule.setStatus(1);
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.PING_AN_WEI_JIN).build());
            } catch (Exception e) {
                log.error("调用维金系统异常。", e);
                recordModule.setResult("调用维金系统异常");
                recordModule.setStatus(0);
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason("调用PAB系统异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
            }
        }
    }

    private void invokeMyBankService(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (Objects.nonNull(configModule) && configModule instanceof MyBankConfigModule) {
            MyBankConfigModule myBankConfigModule = (MyBankConfigModule) configModule;
            if (StringUtils.isBlank(myBankConfigModule.getAppId()) || StringUtils.isBlank(myBankConfigModule.getIsvOrgId()) || StringUtils.isBlank(myBankConfigModule.getIsvPrivateKey()) || StringUtils.isBlank(myBankConfigModule.getIsvPublicKey())) {
                recordModule.setResult("配置信息异常");
                recordModule.setStatus(0);
                return;
            }
            MerchantAppletPreRegisterRequest request = MerchantAppletPreRegisterRequest.buildRequest(myBankConfigModule, merchant, merchantBusinessLicense, recordModule, brandMerchantModule, apolloConfig.getMybankHandlerSwitch());
            try {
                MerchantAppletPreRegisterResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(request, MerchantAppletPreRegisterResponse.class, myBankConfigModule);
                if (response == null) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用网商银行接口未响应");
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandDomainService.createBrandMerchantCreationRecord(recordModule);
                    return;
                }
                if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                    recordModule.setStatus(0);
                    recordModule.setResult("调用网商银行接口失败," + response.getResultMsg());
                    brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                    brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                    brandDomainService.createBrandMerchantCreationRecord(recordModule);
                    return;
                }
            } catch (Exception e) {
                recordModule.setStatus(0);
                recordModule.setResult("调用网商银行接口异常," + e.getMessage());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason("调用网商银行接口异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
            recordModule.setStatus(3);
            recordModule.setResult("预入驻请求成功，等待网商回调！");
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.UNDER_REVIEW.getStatus());
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
        }
    }

    public ImportMerchantTaskResponseDTO getImportTaskList(PageRequestDTO pageRequest) {
        if (Objects.isNull(pageRequest.getPage())) {
            pageRequest.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageRequest.getPageSize())) {
            pageRequest.setPageSize(apolloConfig.getDefaultPageSize());
        }
        QueryLogConditionsModule conditionsModule = JSON.parseObject(JSON.toJSONString(pageRequest), QueryLogConditionsModule.class);
        Long total = brandTaskLogDomainService.countBrandTaskLog(conditionsModule);
        if (total == 0) {
            return ImportMerchantTaskResponseDTO.builder().total(total).records(Lists.newArrayList()).build();
        }
        List<BrandTaskLogModule> brandTaskLogModules = brandTaskLogDomainService.pageSearchBrandTaskLog(conditionsModule, pageRequest.getPage(), pageRequest.getPageSize());
        List<ImportMerchantTaskResponseDTO.TaskApplyLogDTO> taskApplyLogList = Lists.newArrayList();
        brandTaskLogModules.forEach(brandTaskLogModule -> {
            String applyResult = brandTaskLogModule.getTaskResult();
            TaskApplyLogResultModule taskApplyLogResultModule = null;
            if (StringUtils.isNoneBlank(applyResult)) {
                taskApplyLogResultModule = JSON.parseObject(applyResult, TaskApplyLogResultModule.class);
            }
            ImportMerchantTaskResponseDTO.TaskApplyLogDTO taskApplyLogDTO = ImportMerchantTaskResponseDTO.TaskApplyLogDTO.builder()
                    .fileName(Objects.isNull(brandTaskLogModule.getTaskName()) ? "未知" : brandTaskLogModule.getTaskName())
                    .result(Objects.isNull(taskApplyLogResultModule) ? null : taskApplyLogResultModule.getResult())
                    .downloadUrl(Objects.isNull(taskApplyLogResultModule) ? null : taskApplyLogResultModule.getDownloadUrl())
                    .applyTime(TimeConverterHelper.dateFormat(brandTaskLogModule.getCreatedTime(), "yyyy-MM-dd"))
                    .status(brandTaskLogModule.getTaskStatus())
                    .statusDesc(TaskApplyStatusEnum.getDescByStatus(brandTaskLogModule.getTaskStatus()))
                    .build();
            taskApplyLogList.add(taskApplyLogDTO);
        });
        return ImportMerchantTaskResponseDTO.builder().total(total).records(taskApplyLogList).build();
    }

    public boolean importBrandMerchantWithdrawStrategy(OssFileRequestDTO ossFileRequest) {
        String date = new SimpleDateFormat(TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT).format(new Date());
        String[] split = ossFileRequest.getOssKey().split("/");
        String completeFileName = split[split.length - 1];
        String[] strings = completeFileName.split("\\.");
        String fileName = strings[0] + "-" + date + "." + strings[strings.length - 1];
        String filePath = tmpFolder + fileName;
        OssFileHelper.downloadFile(ossFileRequest.getBucket(), ossFileRequest.getOssKey(), filePath);
        Map<Long, List<String>> csvFileData = FileHelper.getCsvFileData(filePath);
        List<String> merchantSnList = Lists.newArrayList();
        csvFileData.forEach((line, stringList) -> merchantSnList.addAll(stringList));
        brandDomainService.relevanceBrandWithdrawStrategy(ossFileRequest.getStrategyId(), ossFileRequest.getBrandId(), merchantSnList);
        return true;
    }

    public boolean importBrandMerchantMetTuanIdAndElmId(OssFileRequestDTO ossFileRequest) {
        String date = new SimpleDateFormat(TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT).format(new Date());
        String[] split = ossFileRequest.getOssKey().split("/");
        String completeFileName = split[split.length - 1];
        String[] strings = completeFileName.split("\\.");
        String fileName = strings[0] + "-" + date + "." + strings[strings.length - 1];
        String filePath = tmpFolder + fileName;
        try {
            ZipHelper.deletefile(filePath);
            OssFileHelper.downloadFile(ossFileRequest.getBucket(), ossFileRequest.getOssKey(), filePath);
            Map<Integer, List<String[]>> integerListMap = FileHelper.readXlsx(filePath);
            if (MapUtils.isNotEmpty(integerListMap)) {
                List<BrandMerchantModule> brandMerchantByBrandId = brandDomainService.getBrandMerchantByBrandId(ossFileRequest.getBrandId());
                if (CollectionUtils.isNotEmpty(brandMerchantByBrandId)) {
                    Map<String, BrandMerchantModule> collect = brandMerchantByBrandId.stream().collect(Collectors.toMap(BrandMerchantModule::getMerchantSn, brandMerchantModule -> brandMerchantModule));
                    List<String[]> vars = integerListMap.get(0);
                    vars.forEach(values -> {
                        String sn = "";
                        String metTuanId = "";
                        String elmId = "";
                        switch (values.length) {
                            case 1:
                                sn = values[0];
                                break;
                            case 2:
                                sn = values[0];
                                metTuanId = values[1];
                                break;
                            case 3:
                                sn = values[0];
                                metTuanId = values[1];
                                elmId = values[2];
                                break;
                            default:
                                break;
                        }

                        BrandMerchantModule brandMerchantModule = collect.get(sn);
                        if (Objects.isNull(brandMerchantModule)) {
                            return;
                        }
                        brandMerchantModule.setAssociatedMeituanStoreSn(metTuanId);
                        brandMerchantModule.setAssociatedElmStoreSn(elmId);
                        brandDomainService.updateBrandMerchant(brandMerchantModule);
                    });
                }
            }
        } catch (Exception e) {
            log.error("导入失败", e);
            throw new BrandBusinessException("导入失败");
        } finally {
            ZipHelper.deletefile(filePath);
        }
        return true;
    }

    private void createFuiouMember(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, ConfigModule configModule, BrandMerchantModule brandMerchantModule) {
        if (Objects.isNull(configModule)) {
            return;
        }
        FuiouConfigModule fuiouConfigModule = (FuiouConfigModule) configModule;
        if (StringUtils.isBlank(fuiouConfigModule.getMerchantNo()) || StringUtils.isBlank(fuiouConfigModule.getPrivateKey()) || StringUtils.isBlank(fuiouConfigModule.getPublicKey())) {
            recordModule.setResult("富友配置信息异常");
            recordModule.setStatus(0);
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
            return;
        }
        fuiouConfigModule.setCheckType(fuiouCheckType);
        recordModule.setRecordId(merchant.getSn() + System.currentTimeMillis());
        // 创建富友会员
        if (!brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType()) && !brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_ADMIN.getMerchantType())) {
            this.createFuiouUser(merchant, merchantBusinessLicense, recordModule, brandMerchantModule, fuiouConfigModule);
        }
        //如果是品牌商户，开启品牌商户子账户
        if (brandMerchantModule.getMerchantType().equals(MerchantTypeEnum.BRAND_OWNER.getMerchantType())) {
            this.createFuiouSubAccount(merchant, recordModule, brandMerchantModule);
        }
        brandDomainService.createBrandMerchantCreationRecord(recordModule);
    }

    private void createFuiouSubAccount(MerchantInfo merchant, BrandMerchantCreationRecordModule recordModule, BrandMerchantModule brandMerchantModule) {
        recordModule.setStatus(1);
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
        // 事务提交后执行的业务代码
        defaultEventPublisher.publish(BrandMerchantEnrollEvent.builder().brandId(brandMerchantModule.getBrandId()).merchantSn(merchant.getSn()).enrollChannelEnum(EnrollChannelEnum.FUIOU).build());
    }

    private void createFuiouUser(MerchantInfo merchant, MerchantBusinessLicenseInfo merchantBusinessLicense, BrandMerchantCreationRecordModule recordModule, BrandMerchantModule brandMerchantModule, FuiouConfigModule fuiouConfigModule) {
        OpenAccountRequest request = new OpenAccountRequest();
        try {
            request.setBody(OpenAccountRequest.buildBody(merchant, merchantBusinessLicense, brandMerchantModule, fuiouConfigModule, recordModule));
            OpenAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, OpenAccountResponse.class, fuiouConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口未响应");
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                return;
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口失败," + response.getResultMsg());
                brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandMerchantModule.setAccountOpenFailureReason(response.getResultMsg());
                return;
            }
            brandMerchantModule.setMemberId(response.getBody().getAccountIn());
            brandMerchantModule.setSubAccountNo(response.getBody().getAccountIn());
            brandMerchantModule.setAccountOpenFailureReason(response.getBody().getCheckUrl());
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用富友接口异常," + e.getMessage());
            brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            brandMerchantModule.setAccountOpenFailureReason(e.getMessage());
            return;
        }
        recordModule.setStatus(3);
        recordModule.setResult("预入驻请求成功，等待客户激活后，富友回调！");
        brandMerchantModule.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus());
        redisHelper.saveAllToSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), Lists.newArrayList(
                        FuiouToBeActiveRecordModule.builder()
                                .brandId(brandMerchantModule.getBrandId())
                                .merchantId(brandMerchantModule.getMerchantId())
                                .tradeNo(recordModule.getRecordId())
                                .build()
                )
        );
    }

    public List<BrandMerchantDTO> getExportBrandMerchants(ExportBrandMerchantRequestDTO exportBrandMerchantRequest) {
        List<BrandMerchantDTO> brandMerchantDtoList = Lists.newArrayList();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(exportBrandMerchantRequest.getBrandId());
        MerchantConditionModule merchantConditionModule = new MerchantConditionModule();
        merchantConditionModule.setBrandIds(Lists.newArrayList(exportBrandMerchantRequest.getBrandId()));
        merchantConditionModule.setMerchantName(exportBrandMerchantRequest.getMerchantName());
        merchantConditionModule.setMerchantSn(exportBrandMerchantRequest.getMerchantSn());
        if (Objects.nonNull(exportBrandMerchantRequest.getMerchantType())) {
            merchantConditionModule.setTypes(Lists.newArrayList(exportBrandMerchantRequest.getMerchantType().getMerchantType()));
        }
        if (Objects.nonNull(exportBrandMerchantRequest.getAccountOpenStatus())) {
            merchantConditionModule.setAccountOpenStatus(exportBrandMerchantRequest.getAccountOpenStatus().getStatus());
        }
        List<BrandMerchantModule> brandMerchantModules = brandDomainService.queryBrandMerchantByConditions(merchantConditionModule);
        if (CollectionUtils.isEmpty(brandMerchantModules)) {
            return brandMerchantDtoList;
        }
        Map<String, BankCardModule> bankCardModuleMap = Maps.newHashMap();
        List<BankCardModule> defaultBankCardModules = bankCardDomainService.getDefaultBankCardModules(brandModule.getBrandId(), brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).collect(Collectors.toList()));
        bankCardModuleMap.putAll(defaultBankCardModules.stream().collect(Collectors.toMap(
                BankCardModule::getMerchantId,
                Function.identity(),
                (existing, replacement) -> existing.getStatus() == 1 ? existing : replacement
        )));

        BrandConfigModule brandConfigModule = brandConfigDomainService.getBrandConfigByBrandId(brandModule.getBrandId());
        List<String> merchantIds = brandMerchantModules.stream().map(BrandMerchantModule::getMerchantId).distinct().collect(Collectors.toList());
        List<MerchantModule> merchantInfoList = merchantDomainService.batchGetMerchantModule(merchantIds);
        Map<String, MerchantModule> merchantInfoMap = merchantInfoList.stream().collect(Collectors.toMap(MerchantModule::getId, Function.identity()));
        brandMerchantModules.forEach(brandMerchantModule -> {
            BrandMerchantDTO brandMerchantDto = JSON.parseObject(JSON.toJSONString(brandMerchantModule), BrandMerchantDTO.class);
            brandMerchantDto.setBankCardActivateStatus(BankCardActivateStatusEnum.NOT_BOUND.getActivateStatus());
            brandMerchantDto.setBrandName(brandModule.getName());
            BankCardModule bankCardModule = bankCardModuleMap.get(brandMerchantDto.getMerchantId());
            if (Objects.nonNull(bankCardModule)) {
                brandMerchantDto.setBankCardActivateStatus(bankCardModule.getStatus() == 0 ? BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus() : BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
            } else {
                brandMerchantDto.setBankCardActivateStatus(BankCardActivateStatusEnum.NOT_BOUND.getActivateStatus());
            }
            brandMerchantDto.setBankCardActivateStatusDesc(BankCardActivateStatusEnum.getDescByActivateStatus(brandMerchantDto.getBankCardActivateStatus()));
            brandMerchantDto.setTypeDesc(BrandMerchantTypeEnum.getMerchantTypeDescByType(brandMerchantDto.getType()));
            brandMerchantDto.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantDto.getAccountOpenStatus()));
            brandMerchantDto.setFundManagementCompany(brandModule.getFundManagementCompany());
            brandMerchantDto.setFundManagementCompanyCode(brandModule.getFundManagementCompanyCode().getFundManagementCompanyCode());
            if (StringUtils.isNotBlank(brandMerchantModule.getAccountOpenStatus())
                    && BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus().equals(brandMerchantModule.getAccountOpenStatus())
                    && brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.MY_BANK)
                    && Objects.nonNull(brandConfigModule)
                    && StringUtils.isNotBlank(brandConfigModule.getConfig())
            ) {
                MyBankConfigModule myBankConfigModule = JSON.parseObject(brandConfigModule.getConfig(), MyBankConfigModule.class);
                String myBankQrCodeUrl = CommonHelper.getMyBankQrCodeUrl(apolloConfig.getMyBankActivateQrcodeUrl(), myBankConfigModule.getAppId(), brandMerchantModule.getMerchantSn(), myBankConfigModule.getIsvOrgId());
                brandMerchantDto.setActivationShortUrl(commonHelper.getShortUrl(myBankQrCodeUrl));
                brandMerchantDto.setActivationUrl(myBankQrCodeUrl);
            }
            if (Objects.nonNull(brandMerchantModule.getExtra())) {
                brandMerchantDto.setExtra(BrandMerchantExtraModule.convertToExtra(brandMerchantModule.getExtra()));
            }
            MerchantModule merchant = merchantInfoMap.get(brandMerchantDto.getMerchantId());
            if (Objects.nonNull(merchant)) {
                brandMerchantDto.setMerchantContactName(merchant.getContactName());
                brandMerchantDto.setMerchantContactPhone(merchant.getContactPhone());
            }
            brandMerchantDtoList.add(brandMerchantDto);
        });
        return brandMerchantDtoList;
    }

    public void exportBrandMerchantExcel(String fileName, String brandId, String platform, List<BrandMerchantDTO> exportBrandMerchants) {
        long taskId = idGeneratorSnowflake.nextId();
        BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
        brandTaskLogModule.setTaskId(taskId);
        brandTaskLogModule.setPlatform(platform);
        brandTaskLogModule.setTaskType(BrandTaskTypeEnum.MERCHANT_EXPORT.getTaskType());
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.EXECUTION.getTaskStatus());
        brandTaskLogModule.setBrandId(brandId);
        brandTaskLogModule.setTaskName(fileName);
        fileName = fileName + ".xlsx";
        String filePath = tmpFolder + fileName;
        Long id = brandTaskLogDomainService.insertBrandTaskLog(brandTaskLogModule);
        brandTaskLogModule.setId(id);
        String finalFileName = fileName;
        ThreadPoolHelper.execute(() -> {
            try {
                if (CollectionUtils.isEmpty(exportBrandMerchants)) {
                    return;
                }
                List<Map<Integer, String>> context = Lists.newArrayList();
                List<BrandMerchantExportEnum> merchantExportEnums = BrandMerchantExportEnum.getAllExportEnum(platform);
                context.add(BrandMerchantExportEnum.getColumnMap(platform));
                exportBrandMerchants.forEach(brandMerchantDto -> {
                    Map<Integer, String> row = Maps.newHashMap();
                    for (int i = 0; i < merchantExportEnums.size(); i++) {
                        row.put(i, CommonHelper.getFieldValue(brandMerchantDto, merchantExportEnums.get(i).getFieldName().split("\\.")));
                    }
                    context.add(row);
                });
                FileHelper.createExcel(filePath, "商户列表", context);
                OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + finalFileName, new File(filePath));
                String url = OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + finalFileName);
                brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
                brandTaskLogModule.setTaskResult(JSON.toJSONString(
                        TaskApplyLogResultModule.builder()
                                .result("导出成功：" + context.size() + "条")
                                .downloadUrl(url).build()));
                brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
            } catch (Exception e) {
                log.error("导出品牌商户信息时，发生异常", e);
                brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
                brandTaskLogModule.setTaskResult(JSON.toJSONString(
                        TaskApplyLogResultModule.builder()
                                .result("导出失败：" + e.getMessage()).build()));
                brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
            } finally {
                ZipHelper.deletefile(filePath);
            }
        });

    }

    @Transactional(rollbackFor = Exception.class)
    public void importBrandMerchantsForAudit(ImportBrandMerchantForAuditRequestDTO createBrandMerchantForAuditRequest) {
        String auditSn = createBrandMerchantForAuditRequest.getAuditSn();
        RLock lock = redissonClient.getLock("importBrandMerchantsForAudit:" + auditSn);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            if (!isLocked) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, "获取锁失败，有其他任务正在执行");
            }

            BrandModule brandModule = brandDomainService.getBrandModuleByBrandSn(createBrandMerchantForAuditRequest.getBrandSn());
            if (Objects.isNull(brandModule)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
            }
            long taskId = idGeneratorSnowflake.nextId();
            BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
            brandTaskLogModule.setBrandId(brandModule.getBrandId());
            brandTaskLogModule.setTaskId(taskId);
            brandTaskLogModule.setTaskName(createBrandMerchantForAuditRequest.getAuditType().getDesc() + "_" + auditSn);
            brandTaskLogModule.setTaskType(createBrandMerchantForAuditRequest.getAuditType().getTaskType());
            brandTaskLogModule.setUserId(createBrandMerchantForAuditRequest.getOperatorId());
            brandTaskLogModule.setPlatform(createBrandMerchantForAuditRequest.getPlatform());
            brandTaskLogModule.setTaskContext(JSON.toJSONString(TaskContextModule.builder()
                    .fileUrl(createBrandMerchantForAuditRequest.getFile())
                    .auditSn(auditSn)
                    .auditId(createBrandMerchantForAuditRequest.getAuditId())
                    .formData(createBrandMerchantForAuditRequest.getFormData())
                    .build()));

            // 增加前置校验逻辑
            AbstractImportExcelHandler handler = importExcelHandlerFactory.getHandler(createBrandMerchantForAuditRequest.getAuditType());
            AbstractImportContext context = handler.initContext(brandTaskLogModule);
            handler.preCheck(context);
            Class dataClass = handler.getDataClass();

            List<BrandSubTaskModule> brandSubTaskModules = new ArrayList<>();
            int limitSize = handler.getLimitQuantity(context);
            AtomicReference<Long> count = new AtomicReference<>(0L);
            try {
                EasyExcel.read(new URL(createBrandMerchantForAuditRequest.getFile()).openStream(), dataClass, new PageReadListener<>(dataList -> {
                    for (Object o : dataList) {
                        handler.checkData(context, o);
                        BrandSubTaskModule brandSubTaskModule = new BrandSubTaskModule();
                        brandSubTaskModule.setTaskId(taskId);
                        brandSubTaskModule.setSubTaskContext(JSON.toJSONString(o));
                        brandSubTaskModules.add(brandSubTaskModule);
                    }
                    count.updateAndGet(v -> v + dataList.size());
                    if (count.get() > limitSize) {
                        throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, "数据量超出限制,最大条数" + limitSize);
                    }
                })).sheet().doRead();
            } catch (IOException e) {
                log.error("插入子任务失败: {}", JSON.toJSONString(createBrandMerchantForAuditRequest), e);
                throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, "读取文件异常");
            }
            if (WosaiCollectionUtils.isEmpty(brandSubTaskModules)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, "excel数据为空");
            }
            brandTaskLogDomainService.insertBrandTaskLog(brandTaskLogModule);
            brandTaskLogDomainService.batchInsertBrandSubTasks(brandSubTaskModules);
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public void exportBrandMerchantBankAccount(String brandId, String brandSn, List<BankCardDetailDTO> exportBankCardList) {
        String fileName = brandSn + "_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        String filePath = tmpFolder + fileName;
        long taskId = idGeneratorSnowflake.nextId();
        BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
        this.createTask(brandId, brandTaskLogModule, taskId, fileName, BrandTaskTypeEnum.BANK_CARD_ACTIVE_STATE_EXPORT.getTaskType());
        String url = OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + fileName);
        ThreadPoolHelper.execute(() -> {
            try {
                if (CollectionUtils.isEmpty(exportBankCardList)) {
                    return;
                }
                List<Map<Integer, String>> context = Lists.newArrayList();
                List<BankCardExportEnum> bankCardExportEnums = BankCardExportEnum.getAllOrderByCol();
                context.add(BankCardExportEnum.getColumnMap());
                exportBankCardList.forEach(bankCardDetail -> {
                    Map<Integer, String> row = Maps.newHashMap();
                    bankCardExportEnums.forEach(bankCardExportEnum -> row.put(bankCardExportEnum.getCol(), CommonHelper.getFieldValue(bankCardDetail, bankCardExportEnum.getFieldName().split("\\."))));
                    context.add(row);
                });
                FileHelper.createExcel(filePath, "商户未激活的银行卡列表", context);
                OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + fileName, new File(filePath));
                brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
                brandTaskLogModule.setTaskResult(JSON.toJSONString(
                        TaskApplyLogResultModule.builder()
                                .result("导出成功：" + context.size() + "条")
                                .downloadUrl(url).build()));
                brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
            } catch (Exception e) {
                log.error("导出品牌商户信息时，发生异常", e);
            } finally {
                ZipHelper.deletefile(filePath);
            }
        });
    }


    public void createTask(String brandId, BrandTaskLogModule brandTaskLogModule, long taskId, String fileName, int taskType) {
        brandTaskLogModule.setTaskId(taskId);
        brandTaskLogModule.setPlatform("SPA");
        brandTaskLogModule.setTaskType(taskType);
        brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.EXECUTION.getTaskStatus());
        brandTaskLogModule.setBrandId(brandId);
        brandTaskLogModule.setTaskName(fileName);
        Long id = brandTaskLogDomainService.insertBrandTaskLog(brandTaskLogModule);
        brandTaskLogModule.setId(id);
    }

    public void exportNotBindCardMerchantExcel(String fileName, String brandId, List<BrandMerchantModule> exportBrandMerchants) {
        if (CollectionUtils.isEmpty(exportBrandMerchants)) {
            return;
        }
        ThreadPoolHelper.execute(() -> {
            BrandTaskLogModule brandTaskLogModule = new BrandTaskLogModule();
            this.createTask(brandId, brandTaskLogModule, idGeneratorSnowflake.nextId(), fileName, BrandTaskTypeEnum.NOT_BIND_BANK_CARD_EXPORT.getTaskType());
            String finalFileName = fileName;
            finalFileName = finalFileName + ".xlsx";
            String filePath = tmpFolder + finalFileName;
            String url = OssFileHelper.STATIC_BASE_URL + "/" + OssFileHelper.getStaticsFileUrl(OssFileHelper.KEY_PREFIX + finalFileName);
            List<Map<Integer, String>> context = Lists.newArrayList();
            Map<Integer, String> header = Maps.newHashMap();
            header.put(0, "商户编号");
            context.add(header);
            exportBrandMerchants.forEach(brandMerchantModule -> {
                Map<Integer, String> row = Maps.newHashMap();
                row.put(0, brandMerchantModule.getMerchantSn());
                context.add(row);
            });
            FileHelper.createExcel(filePath, "商户编号列表", context);
            OssFileHelper.uploadFile(OssFileHelper.IMAGE_BUCKET_NAME, OssFileHelper.KEY_PREFIX + finalFileName, new File(filePath));
            brandTaskLogModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandTaskLogModule.setTaskResult(JSON.toJSONString(
                    TaskApplyLogResultModule.builder()
                            .result("导出成功：" + context.size() + "条")
                            .downloadUrl(url).build()));
            brandTaskLogDomainService.updateBrandTaskLog(brandTaskLogModule);
            ZipHelper.deletefile(filePath);
        });
    }

    public Boolean batchSpecialTreatmentBrandMerchantOpenAccount(OssFileRequestDTO ossFileRequest) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(ossFileRequest.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        if (Objects.isNull(brandModule.getFundManagementCompanyCode()) || !brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            throw new BrandBusinessException("仅中信银行支持此操作。");
        }
        ThreadPoolHelper.execute(() -> asynchronousExecuteOpenAccount(ossFileRequest, brandModule));
        return true;
    }

    public boolean completeBrandMerchantBankCards(OssFileRequestDTO ossFileRequest) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(ossFileRequest.getBrandId());
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        ThreadPoolHelper.execute(() -> asynchronousExecuteCompleteBrandMerchantBankCards(ossFileRequest, brandModule));
        return true;
    }

    private void asynchronousExecuteCompleteBrandMerchantBankCards(OssFileRequestDTO ossFileRequest, BrandModule brandModule) {
        String date = new SimpleDateFormat(TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT).format(new Date());
        String[] split = ossFileRequest.getOssKey().split("/");
        String completeFileName = split[split.length - 1];
        String[] strings = completeFileName.split("\\.");
        String fileName = strings[0] + "-" + date + "." + strings[strings.length - 1];
        String filePath = tmpFolder + fileName;
        try {
            ZipHelper.deletefile(filePath);
            OssFileHelper.downloadFile(ossFileRequest.getBucket(), ossFileRequest.getOssKey(), filePath);
            Map<Integer, List<String[]>> integerListMap = FileHelper.readXlsx(filePath);
            if (MapUtils.isNotEmpty(integerListMap)) {
                List<String[]> sheet0 = integerListMap.get(0);
                if (CollectionUtils.isNotEmpty(sheet0)) {
                    for (String[] rowArray : sheet0) {
                        if (ArrayUtils.isNotEmpty(rowArray)) {
                            Arrays.stream(rowArray).forEach(merchantSn -> {
                                BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByMerchantSn(ossFileRequest.getBrandId(), merchantSn);
                                MerchantInfo merchantBySn = merchantService.getMerchantBySn(merchantSn, null);
                                if (Objects.isNull(brandMerchantModule) || Objects.isNull(merchantBySn)) {
                                    return;
                                }
                                Map<String, Object> req = Maps.newHashMap();
                                req.put("page", 1);
                                req.put("page_size", 10);
                                req.put("biz", "bbs");
                                req.put("merchant_id", merchantBySn.getId());
                                ListResult bizBankAccount = merchantBizBankAccountService.findBizBankAccount(req);
                                List<Map> records = bizBankAccount.getRecords();
                                if (CollectionUtils.isNotEmpty(records)) {
                                    List<MerchantBizBankAccount> merchantBizBankAccounts = JSON.parseArray(JSON.toJSONString(records), MerchantBizBankAccount.class);
                                    merchantBizBankAccounts.forEach(bankAccount -> {
                                        BankCardModule bankCardModule = new BankCardModule();
                                        bankCardModule.setBrandId(brandModule.getBrandId());
                                        bankCardModule.setMerchantId(merchantBySn.getId());
                                        bankCardModule.setBankCardId(bankAccount.getId());
                                        bankCardModule.setAccountType(bankAccount.getType());
                                        bankCardModule.setReservedMobileNumber(merchantBySn.getContact_cellphone());
                                        bankCardModule.setIsDefault(bankAccount.getDefault_status() == 1);
                                        bankCardModule.setBankCardNo(bankAccount.getNumber());
                                        bankCardModule.setStatus(0);
                                        bankCardModule.setOpeningBankNumber(bankAccount.getOpening_number());
                                        try {
                                            bankCardDomainService.createBankCard(bankCardModule);
                                        } catch (Exception e) {
                                            log.warn("保存异常", e);
                                        }
                                    });
                                }
                            });
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理任务失败", e);
        } finally {
            ZipHelper.deletefile(filePath);
        }
    }

    private void asynchronousExecuteOpenAccount(OssFileRequestDTO ossFileRequest, BrandModule brandModule) {
        String date = new SimpleDateFormat(TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT).format(new Date());
        String[] split = ossFileRequest.getOssKey().split("/");
        String completeFileName = split[split.length - 1];
        String[] strings = completeFileName.split("\\.");
        String fileName = strings[0] + "-" + date + "." + strings[strings.length - 1];
        String filePath = tmpFolder + fileName;
        try {
            ZipHelper.deletefile(filePath);
            OssFileHelper.downloadFile(ossFileRequest.getBucket(), ossFileRequest.getOssKey(), filePath);
            Map<Integer, List<String[]>> integerListMap = FileHelper.readXlsx(filePath);
            if (MapUtils.isNotEmpty(integerListMap)) {
                List<String[]> sheet0 = integerListMap.get(0);
                if (CollectionUtils.isNotEmpty(sheet0)) {
                    for (String[] rowArray : sheet0) {
                        if (ArrayUtils.isNotEmpty(rowArray)) {
                            Arrays.stream(rowArray).forEach(merchantSn -> {
                                BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByMerchantSn(ossFileRequest.getBrandId(), merchantSn);
                                if (Objects.isNull(brandMerchantModule) || !brandMerchantModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.IN_OPENING.getStatus())) {
                                    return;
                                }
                                OpenAggregationRequest openAggregationRequest = new OpenAggregationRequest();
                                openAggregationRequest.setAggregationMerchantSn(brandModule.getMerchantSn());
                                openAggregationRequest.setMerchantSn(brandMerchantModule.getMerchantSn());
                                openAggregationRequest.setNotifyUrl(aggregationOpenNotifyUrl);
                                aggregationService.submitOpenAggregation(openAggregationRequest);
                            });
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理任务失败", e);
        } finally {
            ZipHelper.deletefile(filePath);
        }
    }

    private void submitOpenedMerchantInfo(String transactionId, String brandId) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            return;
        }
        List<String> merchantIds = redisHelper.getAllFromSet(String.format(RedisKeyEnum.IMPORT_MERCHANT_TASK_TRANSACTION.getKey(), transactionId, brandId), String.class);
        if (CollectionUtils.isEmpty(merchantIds)) {
            return;
        }
        List<BrandMerchantModule> brandMerchantModules = brandDomainService.getBrandMerchantByBrandIdAndMerchantIds(brandId, merchantIds);
        if (CollectionUtils.isEmpty(brandMerchantModules)) {
            return;
        }
        // 打标签
        brandMerchantModules.forEach(brandMerchantModule -> {
            tagIngestService.ingest(Lists.newArrayList(brandMerchantModule.getMerchantId()), apolloConfig.getStoreTagId(), true);
            if (Boolean.TRUE.equals(apolloConfig.getAutoOpenSubAccountsSwitch())) {
                defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantModule.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandModule, brandMerchantModule)).build());
            }
        });

        redisHelper.deleteKey(String.format(RedisKeyEnum.IMPORT_MERCHANT_TASK_TRANSACTION.getKey(), transactionId, brandId));
    }
}
