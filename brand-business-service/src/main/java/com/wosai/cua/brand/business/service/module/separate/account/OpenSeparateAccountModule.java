package com.wosai.cua.brand.business.service.module.separate.account;

import com.wosai.cua.brand.business.api.dto.request.app.AppOpenSeparateAccountDTO;
import com.wosai.cua.brand.business.api.enums.AccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import lombok.Data;

import java.util.Objects;

@Data
public class OpenSeparateAccountModule {

    private Long id;

    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 子账号类型：FRANCHISE-加盟、BRAND_OWNED-品牌自有、BRAND_SELF_OPERATED-品牌自营、SUPPLIER-供应
     */
    private SeparateAccountTypeEnum accountType;

    /**
     * 类型：PERSONAL-个人、INDIVIDUAL_BUSINESS-个体工商户、COMPANY-企业
     */
    private AccountTypeEnum type;

    /**
     * 证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照，99-统一社会信用代码
     */
    private String idType;

    /**
     * 证件编号
     */
    private String idNumber;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照
     */
    private String legalPersonIdType;

    /**
     * 法人证件编号
     */
    private String legalPersonId;

    private String legalPersonPhone;

    /**
     * 联系人姓名
     */
    private String contractName;

    /**
     * 联系人手机号
     */
    private String contractPhone;

    /**
     * 联系人证件类型
     */
    private String contractIdType;

    /**
     * 联系人证件编号
     */
    private String contractId;

    /**
     *  收钱吧收单门店ID
     */
    private String sqbStoreId;

    /**
     *  收单商户ID
     */
    private String merchantId;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 美团门店ID
     */
    private String meiTuanStoreId;

    /**
     * 饿了么门店ID
     */
    private String elmStoreId;

    /**
     * 抖音门店ID
     */
    private String dyStoreId;

    /**
     * 结算卡信息（富友必填）
     */
    private SettlementCardModule settlementCard;

    /**
     * 是否新开户
     */
    private boolean isNewSeparateAccount;

    @Data
    public static class SettlementCardModule {
        /**
         * 结算卡类型：1-对私、2-对公
         */
        private Integer type;

        /**
         * 户名
         */
        private String holder;

        /**
         * 卡号
         */
        private String cardNumber;

        /**
         * 开户行行号
         */
        private String openingNumber;

        /**
         * 手机号
         */
        private String cellphone;

        /**
         * 银行名称
         */
        private String bankName;

        /**
         * 分支行名称
         */
        private String branchBank;

        public static SettlementCardModule fromDTO(AppOpenSeparateAccountDTO.SettlementCardDTO settlementCardDTO) {
            if (Objects.isNull(settlementCardDTO)) {
                return null;
            }
            SettlementCardModule module = new SettlementCardModule();
            module.setType(settlementCardDTO.getType());
            module.setHolder(settlementCardDTO.getHolder());
            module.setCardNumber(settlementCardDTO.getCardNumber());
            module.setOpeningNumber(settlementCardDTO.getOpeningNumber());
            module.setCellphone(settlementCardDTO.getCellphone());
            module.setBankName(settlementCardDTO.getBankName());
            module.setBranchBank(settlementCardDTO.getBranchBank());
            return module;
        }
    }

    public static OpenSeparateAccountModule fromDTO(AppOpenSeparateAccountDTO request) {
        OpenSeparateAccountModule module = new OpenSeparateAccountModule();
        module.setBrandId(request.getBrandId());
        module.setAccountName(request.getAccountName());
        module.setAccountType(request.getAccountType());
        module.setType(request.getType());
        module.setIdType(request.getIdType());
        module.setIdNumber(request.getIdNumber());
        module.setLegalPersonName(request.getLegalPersonName());
        module.setLegalPersonIdType(request.getLegalPersonIdType());
        module.setLegalPersonPhone(request.getLegalPersonPhone());
        module.setLegalPersonId(request.getLegalPersonId());
        module.setContractName(request.getContractName());
        module.setContractPhone(request.getContractPhone());
        module.setContractIdType(request.getContractIdType());
        module.setContractId(request.getContractId());
        module.setSqbStoreId(request.getSqbStoreId());
        module.setMerchantId(request.getMerchantId());
        module.setOutMerchantNo(request.getOutMerchantNo());
        module.setMeiTuanStoreId(request.getMeiTuanStoreId());
        module.setElmStoreId(request.getElmStoreId());
        module.setDyStoreId(request.getDyStoreId());
        module.setSettlementCard(SettlementCardModule.fromDTO(request.getSettlementCard()));
        return module;
    }
}
