package com.wosai.cua.brand.business.service.externalservice.merchantuser;

import com.wosai.app.dto.GroupSimpleInfo;
import com.wosai.app.dto.V2.CreateUcUserAccountReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.GroupQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.SuperAdminInfoQueryResult;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.UcUserInfoQueryResult;
import com.wosai.uc.common.IdentityType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Slf4j
@Component
public class MerchantUserClient {

    @Autowired
    private UcUserAccountService ucUserAccountService;
    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;
    @Autowired
    private GroupService groupService;

    public UcUserInfoQueryResult getUcUserInfoByIdentifier(String identifier) {
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(identifier);
        if (Objects.isNull(ucUserInfo)) {
            return null;
        }
        return new UcUserInfoQueryResult()
                .setUcUserId(ucUserInfo.getUc_user_id());
    }

    public String createUcUserForEmail(String email) {
        UcUserInfo ucUserInfo = ucUserAccountService.getUcUserByCellphone(email);
        if (Objects.nonNull(ucUserInfo)) {
            throw new CommonInvalidParameterException("用户已存在");
        }
        UcUserInfo ucUser = ucUserAccountService.createUcUser(new CreateUcUserAccountReq()
                .setCellphone(email)
                .setPassword("********")
                .setIdentity_type(IdentityType.EMAIL.getCode()));
        return ucUser.getUc_user_id();
    }

    public SuperAdminInfoQueryResult getSuperAdminInfo(String merchantId) {
        UcMerchantUserInfo superAdmin = merchantUserServiceV2.getSuperAdminByMerchantId(merchantId);
        if (Objects.isNull(superAdmin)) {
            throw new CommonInvalidParameterException("超级管理员信息不存在");
        }
        return new SuperAdminInfoQueryResult()
                .setCellphone(superAdmin.getUcUserInfo().getCellphone())
                .setUcUserId(superAdmin.getUcUserInfo().getUc_user_id());
    }

    public GroupQueryResult getGroupInfoByGroupId(String groupId) {
        if (StringUtils.isEmpty(groupId)) {
            return null;
        }
        GroupSimpleInfo group = null;
        try {
            group = groupService.getGroupById(groupId);
            if (group == null) {
                return null;
            }
            return new GroupQueryResult()
                    .setGroupId(groupId)
                    .setGroupSn(group.getSn())
                    .setGroupName(group.getName())
                    .setOrganizationId(group.getOrganization_id());
        } catch (Exception e) {
            log.warn("集团查询失败，groupId:{}", groupId);
            return null;
        }
    }
}
