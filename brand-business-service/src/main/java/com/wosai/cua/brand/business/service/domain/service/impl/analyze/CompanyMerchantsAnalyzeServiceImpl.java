package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankAccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.service.BrandMerchantsAnalyzeService;
import com.wosai.cua.brand.business.service.enums.AccountIdTypeEnum;
import com.wosai.cua.brand.business.service.enums.BrandImportSheetEnum;
import com.wosai.cua.brand.business.service.enums.CompanyExcelFieldEnum;
import com.wosai.cua.brand.business.service.enums.third.MemberGlobalTypeEnum;
import com.wosai.cua.brand.business.service.helper.FileHelper;
import com.wosai.cua.brand.business.service.helper.ParamsCheckHelper;
import com.wosai.cua.brand.business.service.helper.ThreadLocalHelper;
import com.wosai.cua.brand.business.service.helper.UcUserV2Helper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.ConfigModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.CompanyMerchantAnalyzeModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.CreateMerchantAnalyzeModule;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.AccountReq;
import com.wosai.mc.model.req.CreateMerchantAndStoreReq;
import com.wosai.mc.model.req.CreateMerchantBusinessLicenseReq;
import com.wosai.mc.model.req.CreateMerchantReq;
import com.wosai.mc.model.req.CreateStoreBusinessLicenseWithStoreReq;
import com.wosai.mc.model.req.CreateStoreReq;
import com.wosai.mc.model.req.StoreComplete;
import com.wosai.mc.model.resp.CreateMerchantResp;
import com.wosai.mc.model.resp.StoreCompleteResp;
import com.wosai.uc.dto.CreateUcUserReq;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企业商户解析服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CompanyMerchantsAnalyzeServiceImpl extends BaseMerchantsAnalyzeServiceImpl implements BrandMerchantsAnalyzeService {

    private static final Map<String, Integer> LICENSE_TYPE_MAP = Maps.newConcurrentMap();
    private static final Map<String, Integer> PERSONAL_LICENSE_TYPE_MAP = Maps.newConcurrentMap();

    /**
     * 字段列序号对应的枚举Map
     */
    private static final Map<Integer, CompanyExcelFieldEnum> COMPANY_EXCEL_FIELD_ENUM_MAP = Maps.newConcurrentMap();

    private static final Map<CompanyExcelFieldEnum, Field> FIELD_CACHE = Maps.newHashMap();

    /**
     * 字段名对应的枚举Map
     */
    private static final Map<String, CompanyExcelFieldEnum> FIELD_NAME_EXCEL_FIELD_ENUM_MAP = Maps.newHashMap();

    @PostConstruct
    public void initMap() {
        LICENSE_TYPE_MAP.put("统一社会信用代码", 2);
        PERSONAL_LICENSE_TYPE_MAP.put("身份证", 1);
        PERSONAL_LICENSE_TYPE_MAP.put("外国护照", 2);
        PERSONAL_LICENSE_TYPE_MAP.put("台胞证", 3);
        PERSONAL_LICENSE_TYPE_MAP.put("港澳通行证", 4);
        COMPANY_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(CompanyExcelFieldEnum.values()).collect(Collectors.toMap(CompanyExcelFieldEnum::getColumnNo, Function.identity())));
        FIELD_NAME_EXCEL_FIELD_ENUM_MAP.putAll(Arrays.stream(CompanyExcelFieldEnum.values()).collect(Collectors.toMap(CompanyExcelFieldEnum::getFieldName, Function.identity())));
        this.cacheFields();
    }

    private void cacheFields() {
        Class<?> clazz = CompanyMerchantAnalyzeModule.class;
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                String fieldName = field.getName();
                CompanyExcelFieldEnum companyExcelFieldEnum = FIELD_NAME_EXCEL_FIELD_ENUM_MAP.get(fieldName);
                if (Objects.isNull(companyExcelFieldEnum)) {
                    continue;
                }
                FIELD_CACHE.put(companyExcelFieldEnum, field);
            }
            clazz = clazz.getSuperclass();
        }
    }

    private Field getField(CompanyExcelFieldEnum fieldEnum) throws NoSuchFieldException {
        if (FIELD_CACHE.containsKey(fieldEnum)) {
            return FIELD_CACHE.get(fieldEnum);
        }
        throw new NoSuchFieldException();
    }

    @Override
    public BrandImportSheetEnum getSheetEnum() {
        return BrandImportSheetEnum.COMPANY;
    }

    @Override
    public List<BaseMerchantAnalyzeModule> analyzeData(List<String[]> fields, FundManagementCompanyEnum fundManagementCompanyCode) {
        List<BaseMerchantAnalyzeModule> companyMerchantAnalyzeModules = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fields)) {
            return companyMerchantAnalyzeModules;
        }
        // 处理数据头三行为无效数据从第三行开始
        for (int row = 4; row < fields.size(); row++) {
            CreateMerchantAnalyzeModule analyzeModule = this.analyze(row, fields.get(row));
            if (Objects.nonNull(analyzeModule)) {
                analyzeModule.setFundManagementCompanyCode(fundManagementCompanyCode);
                companyMerchantAnalyzeModules.add(analyzeModule);
            }
        }
        return companyMerchantAnalyzeModules;
    }

    @Override
    public List<BrandMerchantModule> getBrandMerchantModuleList(String brandId, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules, Long strategyId) {
        List<BrandMerchantModule> brandMerchantModules = Lists.newArrayList();
        BrandDO brandDO = brandMapper.selectBrandByBrandId(brandId);
        if (Objects.isNull(brandDO)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND.getCode(), "未查到品牌信息！");
        }
        MerchantInfo adminMerchant = merchantService.getMerchantBySn(brandDO.getMerchantSn(), null);
        if (Objects.isNull(adminMerchant)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_MERCHANT.getCode(), "未查到商户！");
        }
        BrandConfigDO brandConfig = brandConfigMapper.selectOne(BrandMerchantsAnalyzeService.getBrandIdQueryWrapper(brandId));
        boolean allowLogin = Objects.isNull(brandConfig) || StringUtils.isBlank(brandConfig.getConfig());
        boolean needCreateStore;
        if (Objects.nonNull(brandConfig) && StringUtils.isNotBlank(brandConfig.getConfig())) {
            ConfigModule configModule = JSON.parseObject(brandConfig.getConfig(), ConfigModule.class);
            allowLogin = Objects.isNull(configModule.getAllowLogin()) || configModule.getAllowLogin();
            needCreateStore = Objects.nonNull(configModule.getNeedCreateStore()) && configModule.getNeedCreateStore();
        } else {
            needCreateStore = false;
        }
        boolean needSendSms = allowLogin;
        List<String> sqbStoreSnList = Lists.newArrayList();
        List<String> outMerchantNoList = Lists.newArrayList();
        Map<String,String> documentIdMerchantMap = Maps.newHashMap();
        // 创建商户的完整逻辑
        merchantAnalyzeModules.forEach(baseMerchantAnalyzeModule -> {
            if (Objects.isNull(baseMerchantAnalyzeModule)) {
                return;
            }
            CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule = (CompanyMerchantAnalyzeModule) baseMerchantAnalyzeModule;
            String ucUserId = ucUserServiceV2.getUcUserIdByIdentifier(UcUserV2Helper.identifierReq(companyMerchantAnalyzeModule.getContactPhone()));
            BrandMerchantModule brandMerchantModule;
            try {
                if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getStoreSn())){
                    if (sqbStoreSnList.contains(baseMerchantAnalyzeModule.getStoreSn())){
                        throw new BrandBusinessException("门店编号重复！");
                    }
                    sqbStoreSnList.add(baseMerchantAnalyzeModule.getStoreSn());
                }
                if (StringUtils.isNotBlank(baseMerchantAnalyzeModule.getOutMerchantNo())){
                    if (outMerchantNoList.contains(baseMerchantAnalyzeModule.getOutMerchantNo())){
                        throw new BrandBusinessException("外部商户编号重复！");
                    }
                    outMerchantNoList.add(baseMerchantAnalyzeModule.getOutMerchantNo());
                }
                // 判断下非平安账户校验下证件号是否是身份证
                if (Boolean.TRUE.equals(apolloConfig.getIdCardTypeCheckSwitch()) && !FundManagementCompanyEnum.PAB.equals(baseMerchantAnalyzeModule.getFundManagementCompanyCode()) && companyMerchantAnalyzeModule.getLegalPersonLicenseType() != 1 && companyMerchantAnalyzeModule.getPersonalLicenseType() != 1) {
                    throw new BrandBusinessException("资管机构非平安银行的企业法人证件或联系人证件暂不支持除身份证以外的证件。");
                }
                // 校验第三方门店编号是否重复
                BrandMerchantsAnalyzeService.checkThirdStoreSn(baseMerchantAnalyzeModule, brandMerchantMapper);
                // 如果没有账号的话先创建ucUser
                if (StringUtils.isEmpty(ucUserId)) {
                    CreateUcUserReq createUcUserReq = new CreateUcUserReq();
                    createUcUserReq.setIdentifier(companyMerchantAnalyzeModule.getContactPhone());
                    createUcUserReq.setIdentity_type(1);
                    createUcUserReq.setApp("trade");
                    createUcUserReq.setPassword("123456");
                    createUcUserReq.setStatus(-1);
                    UcUserInfoResp ucUser = ucUserServiceV2.createUcUser(UcUserV2Helper.build(createUcUserReq));
                    ucUserId = ucUser.getId();
                }
                if (!needCreateStore) {
                    // 创建商户&商户下门店
                    StoreInfo storeInfo = null;
                    if (StringUtils.isNotBlank(companyMerchantAnalyzeModule.getStoreSn())) {
                        storeInfo = storeService.getStoreBySn(companyMerchantAnalyzeModule.getStoreSn(), null);
                        if (Objects.isNull(storeInfo) && !apolloConfig.getStoreSnCheckWhiteList().contains(brandId)) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_STORE);
                        }
                        if (Objects.nonNull(storeInfo) && !storeInfo.getMerchant_id().equals(adminMerchant.getId())) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_NOT_BELONG_BRAND);
                        }
                        LambdaQueryWrapper<BrandMerchantDO> brandMerchantQueryWrapper = new LambdaQueryWrapper<>();
                        brandMerchantQueryWrapper.eq(BrandMerchantDO::getAssociatedSqbStoreId, storeInfo.getId());
                        brandMerchantQueryWrapper.eq(BrandMerchantDO::getDeleted, 0);
                        List<BrandMerchantDO> brandMerchants = brandMerchantMapper.selectList(brandMerchantQueryWrapper);
                        if (CollectionUtils.isNotEmpty(brandMerchants)) {
                            throw new BrandBusinessException(BrandBusinessExceptionEnum.STORE_WAS_BIND_BRAND);
                        }
                    }
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(companyMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    brandMerchantModule = this.getBrandMerchantModule(brandId, createMerchantResp, companyMerchantAnalyzeModule, storeInfo, strategyId);
                    if (Objects.isNull(brandMerchantModule)) {
                        return;
                    }
                    companyMerchantAnalyzeModule.setMerchantId(createMerchantResp.getMerchantId());
                    brandMerchantModules.add(brandMerchantModule);
                } else {
                    StoreComplete storeComplete = this.getStoreComplete(adminMerchant.getId(), companyMerchantAnalyzeModule);
                    StoreCompleteResp storeCompleteResp = storeService.createStoreComplete(storeComplete);
                    StoreInfo storeInfo = storeService.getStoreBySn(storeCompleteResp.getStoreInfo().getSn(), null);
                    CreateMerchantAndStoreReq merchantComplete = this.getMerchantComplete(companyMerchantAnalyzeModule, ucUserId, storeInfo);
                    merchantComplete.setNeedSendSms(needSendSms);
                    CreateMerchantResp createMerchantResp = merchantService.createMerchantAndStore(merchantComplete);
                    // 创建管理商户下的门店
                    brandMerchantModule = this.getBrandMerchantModule(brandId, createMerchantResp, companyMerchantAnalyzeModule, storeInfo, strategyId);
                }
                if (Objects.isNull(brandMerchantModule)) {
                    return;
                }
                companyMerchantAnalyzeModule.setMerchantId(brandMerchantModule.getMerchantId());
                baseMerchantAnalyzeModule.setMatchedAccountNumber(
                        super.checkAlreadyExistJoin(
                                brandMerchantModule,
                                companyMerchantAnalyzeModule.getLicenseNumber(),
                                MemberGlobalTypeEnum.MEMBER_GLOBAL_TYPE_COMPANY.getSeparateAccountType(),
                                documentIdMerchantMap
                        )
                );
                brandMerchantModules.add(brandMerchantModule);
            } catch (Exception e) {
                log.error("创建商户失败", e);
                Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
                if (MapUtils.isEmpty(errorMapMsg)) {
                    errorMapMsg = Maps.newHashMap();
                }
                analyzeHelper.recordErrorMsg(errorMapMsg, baseMerchantAnalyzeModule, e.getMessage());
                ThreadLocalHelper.set(BrandImportSheetEnum.COMPANY.name(), errorMapMsg);
            }
        });
        return brandMerchantModules;
    }

    @Override
    public List<BankCardModule> getBankCarModules(String brandId, FundManagementCompanyEnum fundManagementCompanyCode, List<BaseMerchantAnalyzeModule> merchantAnalyzeModules) {
        List<BankCardModule> bankCardModules = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(merchantAnalyzeModules) && Boolean.TRUE.equals(fundManagementCompanyCode.getNeedCreateBankAccount())) {
            merchantAnalyzeModules.forEach(merchantAnalyzeModule -> {
                if (Objects.isNull(merchantAnalyzeModule) || StringUtils.isBlank(merchantAnalyzeModule.getMerchantId())) {
                    return;
                }
                CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule = (CompanyMerchantAnalyzeModule) merchantAnalyzeModule;
                BizBankAccountAddReq req = BrandMerchantsAnalyzeService.getBizBankAccountAddReq(companyMerchantAnalyzeModule, biz, BankAccountTypeEnum.COMPANY.getAccountType(), 1);
                MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
                BankCardModule bankCardModule = new BankCardModule();
                bankCardModule.setHolder(req.getHolder());
                bankCardModule.setMerchantId(companyMerchantAnalyzeModule.getMerchantId());
                bankCardModule.setBrandId(brandId);
                bankCardModule.setAccountType(2);
                bankCardModule.setBankCardId(merchantBizBankAccount.getId());
                bankCardModule.setReservedMobileNumber(companyMerchantAnalyzeModule.getCellPhoneNumber());
                bankCardModule.setIsDefault(true);
                bankCardModule.setMobile(companyMerchantAnalyzeModule.getCellPhoneNumber());
                bankCardModule.setBankCardNo(merchantBizBankAccount.getNumber());
                bankCardModule.setOpeningBankNumber(merchantBizBankAccount.getOpening_number());
                bankCardModules.add(bankCardModule);
                super.checkAlreadyHasSeparateAccountSettlementCard(brandId, companyMerchantAnalyzeModule, bankCardModules);
            });
        }
        return bankCardModules;
    }

    @Override
    public void createErrorMsgIntoExcel(String filePath) {
        Map<Integer, String> errorMapMsg = this.getErrorMapMsg();
        if (errorMapMsg == null) return;
        FileHelper.createExcelErrorMsg(filePath, BrandImportSheetEnum.COMPANY.getSheet(), BrandImportSheetEnum.COMPANY.getErrorMsgColNum(), errorMapMsg);
    }

    private Map<Integer, String> getErrorMapMsg() {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.COMPANY.name());
        if (Objects.isNull(o)) {
            return Collections.emptyMap();
        }
        Map errorMap = JSON.parseObject(JSON.toJSONString(o), Map.class);
        HashMap<Integer, String> errorMapMsg = Maps.newHashMap();
        errorMap.forEach((key, value) -> {
            boolean flag = Objects.nonNull(key) && NumberUtils.isCreatable(key.toString()) && Objects.nonNull(value);
            if (flag) {
                errorMapMsg.put(Integer.valueOf(key.toString()), value.toString());
            }
        });
        return errorMapMsg;
    }

    private BrandMerchantModule getBrandMerchantModule(String brandId, CreateMerchantResp createMerchantResp, CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule, StoreInfo storeInfo, Long strategyId) {
        if (StringUtils.isEmpty(brandId) || Objects.isNull(createMerchantResp) || Objects.isNull(companyMerchantAnalyzeModule)) {
            log.error("【CompanyMerchantsAnalyzeServiceImpl】创建商户失败");
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setMerchantId(createMerchantResp.getMerchantId());
        brandMerchantModule.setMerchantSn(createMerchantResp.getMerchantSn());
        brandMerchantModule.setMerchantName(companyMerchantAnalyzeModule.getCompanyName());
        brandMerchantModule.setBrandId(brandId);
        brandMerchantModule.setMerchantType(companyMerchantAnalyzeModule.getBrandMerchantType());
        if (Objects.nonNull(storeInfo)) {
            brandMerchantModule.setAssociatedSqbStoreId(storeInfo.getId());
            brandMerchantModule.setSqbStoreSn(storeInfo.getSn());
        }
        brandMerchantModule.setAssociatedMeituanStoreSn(companyMerchantAnalyzeModule.getMeiTuanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(companyMerchantAnalyzeModule.getElmStoreSn());
        brandMerchantModule.setDyStoreSn(companyMerchantAnalyzeModule.getDyStoreSn());
        brandMerchantModule.setStrategyId(strategyId);
        brandMerchantModule.setType(BrandMerchantTypeEnum.COMPANY.getType());
        brandMerchantModule.setOutMerchantNo(companyMerchantAnalyzeModule.getOutMerchantNo());
        BrandMerchantModule.ContactInfo contactInfo = this.getContactInfo(companyMerchantAnalyzeModule);
        brandMerchantModule.setContactInfo(contactInfo);
        return brandMerchantModule;
    }

    private BrandMerchantModule.ContactInfo getContactInfo(CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule) {
        BrandMerchantModule.ContactInfo contactInfo = new BrandMerchantModule.ContactInfo();
        contactInfo.setContactId(companyMerchantAnalyzeModule.getPersonalId());
        contactInfo.setContactName(companyMerchantAnalyzeModule.getContactName());
        contactInfo.setContactEmail(companyMerchantAnalyzeModule.getContactEmail());
        contactInfo.setContactPhone(companyMerchantAnalyzeModule.getContactPhone());
        contactInfo.setContactIdType(companyMerchantAnalyzeModule.getPersonalLicenseType());
        return contactInfo;
    }

    private CreateMerchantAndStoreReq getMerchantComplete(CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule, String ucUserId, StoreInfo storeInfo) {
        CreateMerchantAndStoreReq merchantComplete = new CreateMerchantAndStoreReq();
        // 创建商户
        CreateMerchantReq merchantReq = new CreateMerchantReq();
        merchantReq.setId(UUID.randomUUID().toString());
        merchantReq.setName(companyMerchantAnalyzeModule.getCompanyName());
        merchantReq.setBusinessName(companyMerchantAnalyzeModule.getCompanyName());
        merchantReq.setProvince(companyMerchantAnalyzeModule.getProvince());
        merchantReq.setCity(companyMerchantAnalyzeModule.getCity());
        merchantReq.setDistrict(companyMerchantAnalyzeModule.getDistrict());
        merchantReq.setContactName(companyMerchantAnalyzeModule.getContactName());
        merchantReq.setContactCellphone(companyMerchantAnalyzeModule.getContactPhone());
        merchantReq.setContactEmail(companyMerchantAnalyzeModule.getContactEmail());
        merchantReq.setStreetAddress(companyMerchantAnalyzeModule.getAddress());
        merchantComplete.setMerchant(merchantReq);
        CreateMerchantBusinessLicenseReq license = this.getCreateMerchantBusinessLicenseReq(companyMerchantAnalyzeModule, merchantReq);
        merchantComplete.setLicense(license);
        AccountReq account = this.getAccountReq(companyMerchantAnalyzeModule, ucUserId);
        merchantComplete.setAccount(account);
        CreateStoreReq store;
        if (Objects.nonNull(storeInfo)){
            store = JSON.parseObject(JSON.toJSONString(storeInfo), CreateStoreReq.class);
            store.setMerchantId(merchantReq.getId());
        }else {
            store = new CreateStoreReq();
            store.setMerchantId(merchantReq.getId());
            store.setName(companyMerchantAnalyzeModule.getCompanyName());
        }
        store.setId(UUID.randomUUID().toString());
        merchantComplete.setStore(store);
        return merchantComplete;
    }

    private AccountReq getAccountReq(CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule, String ucUserId) {
        AccountReq account = new AccountReq();
        account.setIdentifier(companyMerchantAnalyzeModule.getContactPhone());
        account.setIdentityType(1);
        account.setUcUserId(ucUserId);
        account.setName(companyMerchantAnalyzeModule.getContactName());
        // 证件类型：1 身份证；2 港澳居民来往内地通行证； 3 台湾居民来往大陆通行证； 4 非中华人民共和国护照； 5 中国护照；
        account.setIdType(AccountIdTypeEnum.getAccountIdTypeByAnalyzeIdType(companyMerchantAnalyzeModule.getPersonalLicenseType()));
        account.setIdNumber(companyMerchantAnalyzeModule.getPersonalId());
        account.setIdCardFrontPhoto("https://images.wosaimg.com/c9/8fb1c032d51d7ed1539d6d54d014400ebb601c.png");
        account.setIdCardValidity("********-********");
        return account;
    }

    private CreateMerchantBusinessLicenseReq getCreateMerchantBusinessLicenseReq(CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule, CreateMerchantReq merchantReq) {
        CreateMerchantBusinessLicenseReq license = new CreateMerchantBusinessLicenseReq();
        license.setMerchantId(merchantReq.getId());
        license.setType(companyMerchantAnalyzeModule.getLicenseType());
        license.setNumber(companyMerchantAnalyzeModule.getLicenseNumber());
        license.setLegalPersonName(companyMerchantAnalyzeModule.getLegalPersonName());
        license.setLegalPersonIdType(companyMerchantAnalyzeModule.getLegalPersonLicenseType());
        license.setLegalPersonIdNumber(companyMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        license.setName(companyMerchantAnalyzeModule.getCompanyName());
        return license;
    }

    private StoreComplete getStoreComplete(String merchantId, CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule) {
        StoreComplete storeComplete = new StoreComplete();
        CreateStoreReq createStoreReq = new CreateStoreReq();
        createStoreReq.setId(UUID.randomUUID().toString());
        createStoreReq.setMerchantId(merchantId);
        createStoreReq.setName(companyMerchantAnalyzeModule.getCompanyName());
        createStoreReq.setContactCellphone(companyMerchantAnalyzeModule.getContactPhone());
        createStoreReq.setContactName(companyMerchantAnalyzeModule.getContactName());
        createStoreReq.setContactPhone(companyMerchantAnalyzeModule.getContactPhone());
        createStoreReq.setContactEmail(companyMerchantAnalyzeModule.getContactEmail());
        createStoreReq.setDistrict(companyMerchantAnalyzeModule.getDistrict());
        createStoreReq.setProvince(companyMerchantAnalyzeModule.getProvince());
        createStoreReq.setCity(companyMerchantAnalyzeModule.getCity());
        createStoreReq.setStreetAddress(companyMerchantAnalyzeModule.getAddress());
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = this.getCreateStoreBusinessLicenseWithStore(merchantId, companyMerchantAnalyzeModule);
        storeComplete.setCreateStoreReq(createStoreReq);
        storeComplete.setStoreBusinessLicenseReq(storeBusinessLicenseReq);
        return storeComplete;
    }

    private CreateStoreBusinessLicenseWithStoreReq getCreateStoreBusinessLicenseWithStore(String merchantId, CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule) {
        CreateStoreBusinessLicenseWithStoreReq storeBusinessLicenseReq = new CreateStoreBusinessLicenseWithStoreReq();
        storeBusinessLicenseReq.setMerchantId(merchantId);
        storeBusinessLicenseReq.setType(companyMerchantAnalyzeModule.getLicenseType());
        storeBusinessLicenseReq.setNumber(companyMerchantAnalyzeModule.getLicenseNumber());
        storeBusinessLicenseReq.setLegalPersonName(companyMerchantAnalyzeModule.getLegalPersonName());
        storeBusinessLicenseReq.setLegalPersonIdType(companyMerchantAnalyzeModule.getLegalPersonLicenseType());
        storeBusinessLicenseReq.setLegalPersonIdNumber(companyMerchantAnalyzeModule.getLegalPersonLicenseNumber());
        return storeBusinessLicenseReq;
    }

    private CreateMerchantAnalyzeModule analyze(int row, String[] fieldArray) {
        CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule = new CompanyMerchantAnalyzeModule();
        companyMerchantAnalyzeModule.setRow(row);
        for (int i = 0; i < fieldArray.length; i++) {
            CompanyExcelFieldEnum companyExcelFieldEnum = COMPANY_EXCEL_FIELD_ENUM_MAP.get(i);
            if (Objects.isNull(companyExcelFieldEnum)) {
                continue;
            }
            try {
                Field field = this.getField(companyExcelFieldEnum);
                this.analyzeField(companyExcelFieldEnum, field, companyMerchantAnalyzeModule, fieldArray[i]);
            } catch (NoSuchFieldException e) {
                log.warn("解析excel字段错误：{}", companyExcelFieldEnum.getFieldName());
                return null;
            } catch (IllegalAccessException e) {
                log.warn("解析excel字段异常", e);
                return null;
            } catch (BrandBusinessException bx) {
                return null;
            }
        }
        return companyMerchantAnalyzeModule;
    }

    private void analyzeField(
            CompanyExcelFieldEnum companyExcelFieldEnum,
            Field field,
            CompanyMerchantAnalyzeModule companyMerchantAnalyzeModule,
            String fieldValue
    ) throws IllegalAccessException {
        String value = fieldValue;
        if (StringUtils.isBlank(value)) {
            return;
        }
        value = StringUtils.trim(value);
        field.setAccessible(true);
        if (companyExcelFieldEnum.equals(CompanyExcelFieldEnum.CONTACT_LICENSE_TYPE) || companyExcelFieldEnum.equals(CompanyExcelFieldEnum.LEGAL_PERSON_LICENSE_TYPE)) {
            field.set(companyMerchantAnalyzeModule, PERSONAL_LICENSE_TYPE_MAP.get(value));
            return;
        }
        if (companyExcelFieldEnum.equals(CompanyExcelFieldEnum.LICENSE_TYPE)) {
            field.set(companyMerchantAnalyzeModule, LICENSE_TYPE_MAP.get(value));
            return;
        }
        if (companyExcelFieldEnum.equals(CompanyExcelFieldEnum.BRAND_MERCHANT_TYPE)) {
            value = MerchantTypeEnum.getMerchantTypeByDesc(value);
        }
        field.set(companyMerchantAnalyzeModule, value);
    }

    public void checkParams(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                BaseMerchantAnalyzeModule analyzeModule = iterator.next();
                Class<? extends BaseMerchantAnalyzeModule> analyzeModuleClass = analyzeModule.getClass();
                boolean needRemove = analyzeHelper.checkFields(analyzeModuleClass, analyzeModule, errorMap, BrandImportSheetEnum.COMPANY);
                if (needRemove) {
                    iterator.remove();
                }
            }
            ThreadLocalHelper.set(BrandImportSheetEnum.COMPANY.name(), errorMap);
        }
    }

    @Override
    public void checkIsValidChineseID(List<BaseMerchantAnalyzeModule> createMerchantAnalyzeModules) {
        Object o = ThreadLocalHelper.get(BrandImportSheetEnum.COMPANY.name());
        Map<Integer, String> errorMap = Maps.newHashMap();
        if (Objects.nonNull(o)) {
            errorMap.putAll((Map<Integer, String>) o);
        }
        if (CollectionUtils.isNotEmpty(createMerchantAnalyzeModules)) {
            Iterator<BaseMerchantAnalyzeModule> iterator = createMerchantAnalyzeModules.iterator();
            while (iterator.hasNext()) {
                CompanyMerchantAnalyzeModule analyzeModule = (CompanyMerchantAnalyzeModule) iterator.next();
                boolean isValid = true;
                if (analyzeModule.getLegalPersonLicenseType() == 1) {
                    isValid = ParamsCheckHelper.isValidChineseID(analyzeModule.getLegalPersonLicenseNumber());
                    analyzeHelper.recordErrorMsg(errorMap, analyzeModule, "法人身份证号不合法");
                }
                if (analyzeModule.getPersonalLicenseType() == 1) {
                    isValid = ParamsCheckHelper.isValidChineseID(analyzeModule.getPersonalId());
                    analyzeHelper.recordErrorMsg(errorMap, analyzeModule, "联系人身份证号不合法");
                }
                if (!isValid) {
                    iterator.remove();
                }
            }
        }
        ThreadLocalHelper.set(BrandImportSheetEnum.COMPANY.name(), errorMap);
    }
}
