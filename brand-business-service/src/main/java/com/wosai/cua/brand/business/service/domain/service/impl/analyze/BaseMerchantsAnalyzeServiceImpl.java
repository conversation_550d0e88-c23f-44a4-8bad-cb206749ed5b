package com.wosai.cua.brand.business.service.domain.service.impl.analyze;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountSettlementCardDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.helper.AnalyzeHelper;
import com.wosai.cua.brand.business.service.helper.CryptHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.merchant.analyze.BaseMerchantAnalyzeModule;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.tools.service.InfoQueryService;
import com.wosai.uc.v2.service.UcUserServiceV2;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountAddReq;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.bank.model.bizbankaccount.MerchantBizBankAccount;
import com.wosai.upay.bank.model.bizbankaccount.QueryBizBankAccountReq;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class BaseMerchantsAnalyzeServiceImpl {

    @Autowired
    protected UcUserServiceV2 ucUserServiceV2;

    @Autowired
    protected MerchantService merchantService;

    @Autowired
    protected StoreService storeService;

    @Autowired
    protected MerchantBizBankAccountService merchantBizBankAccountService;

    @Autowired
    protected InfoQueryService infoQueryService;

    @Autowired
    protected BrandMapper brandMapper;

    @Autowired
    protected BrandConfigDOMapper brandConfigMapper;

    @Value("${spring.application.biz}")
    protected String biz;

    @Autowired
    protected ApolloConfig apolloConfig;

    @Autowired
    protected CryptHelper cryptHelper;

    @Autowired
    protected AnalyzeHelper analyzeHelper;

    @Autowired
    protected BrandMerchantMapper brandMerchantMapper;

    @Autowired
    protected SeparateAccountDOMapper separateAccountDOMapper;

    @Autowired
    protected SeparateAccountSettlementCardDOMapper separateAccountSettlementCardDOMapper;

    @Autowired
    protected MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    protected MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    protected BankService bankService;

    protected String checkAlreadyExistJoin(BrandMerchantModule brandMerchantModule, String documentId, String documentType, Map<String,String> merchantSnDocumentIdMap) {
        if (MapUtils.isNotEmpty(merchantSnDocumentIdMap) && merchantSnDocumentIdMap.containsKey(documentId)){
            brandMerchantModule.setSameBatchImportAndSameIdNumberMerchant(true);
            brandMerchantModule.setSameBatchMerchantSn(merchantSnDocumentIdMap.get(documentId));
        }else {
            merchantSnDocumentIdMap.put(documentId, brandMerchantModule.getMerchantSn());
        }
        LambdaQueryWrapper<SeparateAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountDO::getBrandId, brandMerchantModule.getBrandId());
        queryWrapper.eq(SeparateAccountDO::getIdNumber, cryptHelper.encrypt(documentId));
        queryWrapper.eq(SeparateAccountDO::getIdType, documentType);
        queryWrapper.eq(SeparateAccountDO::getDeleted, 0);
        List<SeparateAccountDO> separateAccounts = separateAccountDOMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(separateAccounts)) {
            Optional<SeparateAccountDO> any = separateAccounts.stream().filter(separateAccountDO -> StringUtils.isNotBlank(separateAccountDO.getSubAccountNo())).findAny();
            if (any.isPresent()) {
                SeparateAccountDO separateAccountDO = any.get();
                log.info("checkAlreadyExistJoin 查询到已开户的数据，对应的分账账户编号为：【{}】", separateAccountDO.getSubAccountNo());
                brandMerchantModule.setAccountOpenStatus(separateAccountDO.getAccountOpenStatus());
                brandMerchantModule.setSubAccountNo(separateAccountDO.getSubAccountNo());
                brandMerchantModule.setMemberId(separateAccountDO.getMemberId());
                return separateAccountDO.getAccountNumber();
            }
        }
        return null;
    }

    protected void checkAlreadyHasSeparateAccountSettlementCard(String brandId, BaseMerchantAnalyzeModule analyzeModule, List<BankCardModule> bankCardModules) {
        if (StringUtils.isBlank(analyzeModule.getMatchedAccountNumber())) {
            return;
        }
        LambdaQueryWrapper<SeparateAccountSettlementCardDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountSettlementCardDO::getAccountNumber, analyzeModule.getMatchedAccountNumber());
        queryWrapper.eq(SeparateAccountSettlementCardDO::getDefaultStatus, 1);
        queryWrapper.eq(SeparateAccountSettlementCardDO::getDeleted, 0);
        queryWrapper.orderByDesc(SeparateAccountSettlementCardDO::getId);
        List<SeparateAccountSettlementCardDO> separateAccountSettlementCards = separateAccountSettlementCardDOMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(separateAccountSettlementCards)) {
            return;
        }
        BankCardModule bankCardModule = new BankCardModule();
        bankCardModule.setHolder(separateAccountSettlementCards.get(0).getHolder());
        bankCardModule.setMerchantId(analyzeModule.getMerchantId());
        bankCardModule.setBrandId(brandId);
        bankCardModule.setAccountType(separateAccountSettlementCards.get(0).getType());
        bankCardModule.setReservedMobileNumber(cryptHelper.decrypt(separateAccountSettlementCards.get(0).getCellphone()));
        bankCardModule.setMobile(cryptHelper.decrypt(separateAccountSettlementCards.get(0).getCellphone()));
        bankCardModule.setStatus(separateAccountSettlementCards.get(0).getDefaultStatus());
        bankCardModule.setThirdBankCardId(separateAccountSettlementCards.get(0).getThirdBankCardId());
        bankCardModule.setIsDefault(true);
        QueryBizBankAccountReq queryBizBankAccountReq = new QueryBizBankAccountReq();
        queryBizBankAccountReq.setMerchantId(analyzeModule.getMerchantId());
        queryBizBankAccountReq.setNumber(separateAccountSettlementCards.get(0).getCardNumber());
        queryBizBankAccountReq.setBiz(biz);
        BizBankAccountRes account = merchantBizBankAccountService.getMerchantBizBankAccountByMerchantIdAndNumber(queryBizBankAccountReq);
        if (Objects.nonNull(account)) {
            bankCardModule.setBankCardId(account.getId());
            bankCardModule.setBankCardNo(account.getNumber());
            bankCardModule.setOpeningBankNumber(account.getOpeningNumber());
        } else {
            BizBankAccountAddReq req = new BizBankAccountAddReq();
            req.setSet_default(true);
            req.setBiz(biz);
            req.setId_type(separateAccountSettlementCards.get(0).getIdType());
            req.setType(separateAccountSettlementCards.get(0).getType());
            req.setMerchant_id(analyzeModule.getMerchantId());
            req.setOpening_number(separateAccountSettlementCards.get(0).getOpeningNumber());
            req.setNumber(cryptHelper.decrypt(separateAccountSettlementCards.get(0).getCardNumber()));
            req.setHolder(separateAccountSettlementCards.get(0).getHolder());
            MerchantBizBankAccount merchantBizBankAccount = merchantBizBankAccountService.saveBizBankAccountWithoutApply(req);
            bankCardModule.setBankCardId(merchantBizBankAccount.getId());
            bankCardModule.setBankCardNo(merchantBizBankAccount.getNumber());
            bankCardModule.setOpeningBankNumber(merchantBizBankAccount.getOpening_number());
        }
        if (CollectionUtils.isNotEmpty(bankCardModules)) {
            bankCardModules.forEach(module -> module.setIsDefault(module.getBankCardId().equals(bankCardModule.getBankCardId())));
        }
        if (bankCardModules.stream().noneMatch(module -> module.getBankCardId().equals(bankCardModule.getBankCardId()))){
            bankCardModules.add(bankCardModule);
        }
    }
}
