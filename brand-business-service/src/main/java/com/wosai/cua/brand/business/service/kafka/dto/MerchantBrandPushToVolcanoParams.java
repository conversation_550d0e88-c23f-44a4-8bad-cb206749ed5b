package com.wosai.cua.brand.business.service.kafka.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MerchantBrandPushToVolcanoParams {

    private String merchantId;

    @JSONField(name = "brand_sn")
    private List<String> brandSns;

    @JSONField(name = "brand_name")
    private List<String> brandNames;

    @JSONField(name = "brand_org_name")
    private List<String> brandOrganizations;

    @JSONField(name = "group_sn")
    private List<String> groupSns;

    @JSONField(name = "group_name")
    private List<String> groupNames;

    @JSONField(name = "group_org_name")
    private List<String> groupOrganizations;
}
