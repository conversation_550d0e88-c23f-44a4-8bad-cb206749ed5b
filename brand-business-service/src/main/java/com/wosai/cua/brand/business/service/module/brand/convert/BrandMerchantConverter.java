package com.wosai.cua.brand.business.service.module.brand.convert;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.ChangeBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class BrandMerchantConverter {
    private BrandMerchantConverter() {
        throw new IllegalStateException("Utility class");
    }

    public static List<BrandMerchantModule> convertModules(List<BrandMerchantDO> brandMerchantDOList) {
        if (CollectionUtils.isEmpty(brandMerchantDOList)){
            return Lists.newArrayList();
        }
        return brandMerchantDOList.stream().map(BrandMerchantConverter::convertModule).collect(Collectors.toList());
    }

    public static BrandMerchantModule convertModule(BrandMerchantDO brandMerchantDO) {
        if (brandMerchantDO == null) {
            return null;
        }
        BrandMerchantModule brandMerchantModule = new BrandMerchantModule();
        brandMerchantModule.setId(brandMerchantDO.getId());
        brandMerchantModule.setBrandId(brandMerchantDO.getBrandId());
        brandMerchantModule.setParentBrandId(brandMerchantDO.getParentBrandId());
        brandMerchantModule.setMerchantId(brandMerchantDO.getMerchantId());
        brandMerchantModule.setMerchantName(brandMerchantDO.getMerchantName());
        brandMerchantModule.setMerchantSn(brandMerchantDO.getMerchantSn());
        brandMerchantModule.setType(brandMerchantDO.getType());
        brandMerchantModule.setPaymentMode(brandMerchantDO.getPaymentMode());
        brandMerchantModule.setMerchantType(brandMerchantDO.getMerchantType());
        brandMerchantModule.setSubAccountNo(brandMerchantDO.getSubAccountNo());
        brandMerchantModule.setMemberId(brandMerchantDO.getMemberId());
        brandMerchantModule.setAssociatedSqbStoreId(brandMerchantDO.getAssociatedSqbStoreId());
        brandMerchantModule.setAssociatedMeituanStoreSn(brandMerchantDO.getAssociatedMeituanStoreSn());
        brandMerchantModule.setAssociatedElmStoreSn(brandMerchantDO.getAssociatedElmStoreSn());
        brandMerchantModule.setDyStoreSn(brandMerchantDO.getDyStoreSn());
        brandMerchantModule.setStrategyId(brandMerchantDO.getStrategyId());
        brandMerchantModule.setAccountOpenStatus(brandMerchantDO.getAccountOpenStatus());
        brandMerchantModule.setAccountOpenFailureReason(brandMerchantDO.getAccountOpenFailureReason());
        if (StringUtils.isNotBlank(brandMerchantDO.getExtra())) {
            brandMerchantModule.setExtra(JSON.parseObject(brandMerchantDO.getExtra(), BrandMerchantExtraModule.class));
        }
        brandMerchantModule.setDeleted(brandMerchantDO.getDeleted());
        brandMerchantModule.setCreatedTime(brandMerchantDO.getCreatedTime());
        brandMerchantModule.setUpdatedTime(brandMerchantDO.getUpdatedTime());
        brandMerchantModule.setAssociatedTime(brandMerchantDO.getAssociatedTime());
        brandMerchantModule.setVersion(brandMerchantDO.getVersion());
        brandMerchantModule.setOutMerchantNo(brandMerchantDO.getOutMerchantNo());
        brandMerchantModule.setTopUpAccountNo(brandMerchantDO.getTopUpAccountNo());
        brandMerchantModule.setTopUpAccountName(brandMerchantDO.getTopUpAccountName());
        brandMerchantModule.setSqbStoreSn(brandMerchantDO.getSqbStoreSn());
        brandMerchantModule.setBankCardActivateStatus(brandMerchantDO.getBankCardActivateStatus());
        brandMerchantModule.setAccountOpenedTime(brandMerchantDO.getAccountOpenedTime());
        brandMerchantModule.setMerchantDockingMode(brandMerchantDO.getMerchantDockingMode());
        return brandMerchantModule;
    }

    public static BrandMerchantDO convertDO(BrandMerchantModule brandMerchantModule) {
        if (brandMerchantModule == null) {
            return null;
        }
        BrandMerchantDO brandMerchantDO = new BrandMerchantDO();
        brandMerchantDO.setId(brandMerchantModule.getId());
        brandMerchantDO.setBrandId(brandMerchantModule.getBrandId());
        brandMerchantDO.setParentBrandId(brandMerchantModule.getParentBrandId());
        brandMerchantDO.setMerchantId(brandMerchantModule.getMerchantId());
        brandMerchantDO.setMerchantName(brandMerchantModule.getMerchantName());
        brandMerchantDO.setMerchantSn(brandMerchantModule.getMerchantSn());
        brandMerchantDO.setType(brandMerchantModule.getType());
        brandMerchantDO.setPaymentMode(brandMerchantModule.getPaymentMode());
        brandMerchantDO.setMerchantType(brandMerchantModule.getMerchantType());
        brandMerchantDO.setSubAccountNo(StringUtils.isBlank(brandMerchantModule.getSubAccountNo()) ? "" : brandMerchantModule.getSubAccountNo());
        brandMerchantDO.setMemberId(StringUtils.isBlank(brandMerchantModule.getMemberId()) ? "" : brandMerchantModule.getMemberId());
        brandMerchantDO.setAssociatedSqbStoreId(brandMerchantModule.getAssociatedSqbStoreId());
        brandMerchantDO.setAssociatedMeituanStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        brandMerchantDO.setAssociatedElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        brandMerchantDO.setDyStoreSn(brandMerchantModule.getDyStoreSn());
        brandMerchantDO.setStrategyId(brandMerchantModule.getStrategyId());
        brandMerchantDO.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
        brandMerchantDO.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
        brandMerchantDO.setSqbStoreSn(brandMerchantModule.getSqbStoreSn());
        if (Objects.nonNull(brandMerchantModule.getExtra())) {
            brandMerchantDO.setExtra(JSON.toJSONString(brandMerchantModule.getExtra()));
        }
        brandMerchantDO.setExtra(brandMerchantDO.getExtra());
        brandMerchantDO.setDeleted(brandMerchantModule.getDeleted());
        brandMerchantDO.setCreatedTime(brandMerchantModule.getCreatedTime());
        brandMerchantDO.setUpdatedTime(brandMerchantModule.getUpdatedTime());
        brandMerchantDO.setAssociatedTime(brandMerchantModule.getAssociatedTime());
        brandMerchantDO.setOutMerchantNo(brandMerchantModule.getOutMerchantNo());
        brandMerchantDO.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
        brandMerchantDO.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
        brandMerchantDO.setVersion(Objects.isNull(brandMerchantModule.getVersion()) ? 1 : brandMerchantModule.getVersion() + 1);
        brandMerchantDO.setBankCardActivateStatus(brandMerchantModule.getBankCardActivateStatus());
        brandMerchantDO.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
        return brandMerchantDO;
    }

    public static BrandMerchantDO convertChangeDO(ChangeBrandMerchantModule changeBrandMerchantModule, BrandMerchantDO targetDO) {
        if (changeBrandMerchantModule == null) {
            return targetDO;
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getMerchantSn())) {
            targetDO.setMerchantSn(changeBrandMerchantModule.getMerchantSn());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getMerchantName())) {
            targetDO.setMerchantName(changeBrandMerchantModule.getMerchantName());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getMerchantType())) {
            targetDO.setMerchantType(changeBrandMerchantModule.getMerchantType());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getSubAccountNo())) {
            targetDO.setSubAccountNo(changeBrandMerchantModule.getSubAccountNo());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getMemberId())) {
            targetDO.setMemberId(changeBrandMerchantModule.getMemberId());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getTopUpAccountNo())) {
            targetDO.setTopUpAccountNo(changeBrandMerchantModule.getTopUpAccountNo());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getTopUpAccountName())) {
            targetDO.setTopUpAccountName(changeBrandMerchantModule.getTopUpAccountName());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getAssociatedSqbStoreId())) {
            targetDO.setAssociatedSqbStoreId(changeBrandMerchantModule.getAssociatedSqbStoreId());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getAssociatedMeituanStoreSn())) {
            targetDO.setAssociatedMeituanStoreSn(changeBrandMerchantModule.getAssociatedMeituanStoreSn());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getAssociatedElmStoreSn())) {
            targetDO.setAssociatedElmStoreSn(changeBrandMerchantModule.getAssociatedElmStoreSn());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getDyStoreSn())) {
            targetDO.setDyStoreSn(changeBrandMerchantModule.getDyStoreSn());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getOutMerchantNo())) {
            targetDO.setOutMerchantNo(changeBrandMerchantModule.getOutMerchantNo());
        }
        if (Objects.nonNull(changeBrandMerchantModule.getStrategyId())) {
            targetDO.setStrategyId(changeBrandMerchantModule.getStrategyId());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getAccountOpenFailureReason())) {
            targetDO.setAccountOpenFailureReason(changeBrandMerchantModule.getAccountOpenFailureReason());
        }
        if (Objects.nonNull(changeBrandMerchantModule.getDeleted())) {
            targetDO.setDeleted(changeBrandMerchantModule.getDeleted());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getAccountOpenStatus())) {
            targetDO.setAccountOpenStatus(changeBrandMerchantModule.getAccountOpenStatus());
        }
        targetDO.setUpdatedTime(new Date());
        if (Objects.nonNull(changeBrandMerchantModule.getExtra())) {
            targetDO.setExtra(JSON.toJSONString(changeBrandMerchantModule.getExtra()));
        } else {
            targetDO.setExtra("");
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getSqbStoreSn())) {
            targetDO.setSqbStoreSn(changeBrandMerchantModule.getSqbStoreSn());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getBankCardActivateStatus())) {
            targetDO.setBankCardActivateStatus(changeBrandMerchantModule.getBankCardActivateStatus());
        }
        if (StringUtils.isNotBlank(changeBrandMerchantModule.getThreePartyMerchantSn())) {
            targetDO.setThreePartyMerchantSn(changeBrandMerchantModule.getThreePartyMerchantSn());
        }
        targetDO.setAccountOpenedTime(changeBrandMerchantModule.getAccountOpenedTime());
        targetDO.setVersion(targetDO.getVersion() + 1);
        return targetDO;
    }
}
