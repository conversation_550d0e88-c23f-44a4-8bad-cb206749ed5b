package com.wosai.cua.brand.business.service.event.listener;

import com.alibaba.fastjson.JSON;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import com.wosai.cua.brand.business.service.event.model.MerchantOpenStatusEvent;
import com.wosai.cua.brand.business.service.event.type.OpenStatusChangeEventType;
import com.wosai.cua.brand.business.service.kafka.DataCenterKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.dto.DataCenterMessageBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class MerchantOpenStatusChangeListener implements Listener<MerchantOpenStatusEvent> {

    private final DataCenterKafkaProducer dataCenterKafkaProducer;


    public MerchantOpenStatusChangeListener(DataCenterKafkaProducer dataCenterKafkaProducer) {
        this.dataCenterKafkaProducer = dataCenterKafkaProducer;
    }

    @Override
    public EventType getEventType() {
        return OpenStatusChangeEventType.CHANGED;
    }

    @Override
    public void handle(MerchantOpenStatusEvent event) {
        if (Objects.isNull(event.getParams())) {
            log.error("event params is null");
            return;
        }
        log.info("event params:{}", JSON.toJSONString(event.getParams()));
        DataCenterMessageBody body = DataCenterMessageBody.builder()
                .userUniqueId(event.getMerchantId())
                .eventName("SftApply")
                .eventParams(event.getParams()).build();
        dataCenterKafkaProducer.publishEvent(body);
    }
}
