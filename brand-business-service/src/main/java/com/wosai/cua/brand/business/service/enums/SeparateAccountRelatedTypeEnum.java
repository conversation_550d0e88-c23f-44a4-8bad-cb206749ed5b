package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

@Getter
public enum SeparateAccountRelatedTypeEnum {
    /**
     * MERCHANT_ID-商户id、STORE_SN-收单门店编号、STORE_ID-收单门店id、OUT_MERCHANT-外部商户、SUB_ACCOUNT-资管机构子账号、MEMBER_ID-第三方机构会员id、MT_STORE-美团门店、ELM_STORE-饿了么门店、DY_STORE-抖音门店
     */
    MERCHANT_ID("MERCHANT_ID", "商户id"),
    STORE_SN("STORE_SN", "收单门店编号"),
    STORE_ID("STORE_ID", "收单门店id"),
    OUT_MERCHANT("OUT_MERCHANT", "外部商户"),
    SUB_ACCOUNT("SUB_ACCOUNT", "资管机构子账号"),
    MEMBER_ID("MEMBER_ID", "第三方机构会员id"),
    MT_STORE("MT_STORE", "美团门店"),
    ELM_STORE("ELM_STORE", "饿了么门店"),
    DY_STORE("DY_STORE", "抖音门店");

    private final String type;

    private final String desc;


    SeparateAccountRelatedTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
