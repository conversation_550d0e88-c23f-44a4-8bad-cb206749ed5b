package com.wosai.cua.brand.business.service.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountRelatedDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountRelatedDOService;
import com.wosai.cua.brand.business.service.enums.SeparateAccountRelatedTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【separate_account_related(分账账户关联表)】的数据库操作Service实现
 * @createDate 2025-06-05 10:17:57
 */
@Service
public class SeparateAccountRelatedDOServiceImpl extends ServiceImpl<SeparateAccountRelatedDOMapper, SeparateAccountRelatedDO>
        implements SeparateAccountRelatedDOService {

    @Override
    public SeparateAccountRelatedDO addSeparateAccountRelated(String accountNumber, SeparateAccountRelatedTypeEnum type, String relatedSn) {
        LambdaQueryWrapper<SeparateAccountRelatedDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SeparateAccountRelatedDO::getAccountNumber, accountNumber);
        lambdaQueryWrapper.eq(SeparateAccountRelatedDO::getType, type.getType());
        lambdaQueryWrapper.eq(SeparateAccountRelatedDO::getRelatedSn, relatedSn);
        SeparateAccountRelatedDO separateAccountRelatedDO = getOne(lambdaQueryWrapper);
        if (separateAccountRelatedDO != null) {
            separateAccountRelatedDO.setDeleted(0);
            separateAccountRelatedDO.setMtime(new Date());
            separateAccountRelatedDO.setCtime(new Date());
            updateById(separateAccountRelatedDO);
            return separateAccountRelatedDO;
        }
        separateAccountRelatedDO = new SeparateAccountRelatedDO();
        separateAccountRelatedDO.setAccountNumber(accountNumber);
        separateAccountRelatedDO.setType(type.getType());
        separateAccountRelatedDO.setRelatedSn(relatedSn);
        save(separateAccountRelatedDO);
        return separateAccountRelatedDO;
    }

    @Override
    public Boolean deleteSeparateAccountRelated(String accountNumber, SeparateAccountRelatedTypeEnum type, String relatedSn) {
        LambdaUpdateWrapper<SeparateAccountRelatedDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(SeparateAccountRelatedDO::getAccountNumber, accountNumber);
        lambdaUpdateWrapper.eq(SeparateAccountRelatedDO::getType, type.getType());
        lambdaUpdateWrapper.eq(SeparateAccountRelatedDO::getRelatedSn, relatedSn);
        lambdaUpdateWrapper.set(SeparateAccountRelatedDO::getDeleted, 1);
        return update(lambdaUpdateWrapper);
    }

    @Override
    public List<SeparateAccountRelatedDO> getSeparateAccountRelatedList(String accountNumber) {
        LambdaQueryWrapper<SeparateAccountRelatedDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountRelatedDO::getAccountNumber, accountNumber);
        queryWrapper.eq(SeparateAccountRelatedDO::getDeleted, 0);
        return list(queryWrapper);
    }

    @Override
    public Map<String, List<SeparateAccountRelatedDO>> getSeparateAccountRelatedMap(List<String> accountNumbers) {
        if (CollectionUtils.isEmpty(accountNumbers)){
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<SeparateAccountRelatedDO> queryWrapper = new LambdaQueryWrapper<>();
        List<SeparateAccountRelatedDO> relatedList = list(
                queryWrapper
                        .in(SeparateAccountRelatedDO::getAccountNumber, accountNumbers)
                        .eq(SeparateAccountRelatedDO::getDeleted, 0)
        );
        Map<String, List<SeparateAccountRelatedDO>> accountNumberRelatedList = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(relatedList)) {
            accountNumberRelatedList.putAll(relatedList.stream().collect(Collectors.groupingBy(SeparateAccountRelatedDO::getAccountNumber)));
        }
        return accountNumberRelatedList;
    }

    @Override
    public boolean checkRelatedSnExist(String accountNumber, SeparateAccountRelateTypeEnum type, String relatedSn) {
        LambdaQueryWrapper<SeparateAccountRelatedDO> queryWrapper = new LambdaQueryWrapper<>();
        List<SeparateAccountRelatedDO> separateAccountRelatedDOList = list(queryWrapper.eq(SeparateAccountRelatedDO::getRelatedSn, relatedSn).eq(SeparateAccountRelatedDO::getType, type.getType()).eq(SeparateAccountRelatedDO::getDeleted, 0));
        if (CollectionUtils.isNotEmpty(separateAccountRelatedDOList)){
            return separateAccountRelatedDOList.stream().anyMatch(separateAccountRelatedDO -> separateAccountRelatedDO.getAccountNumber().equals(accountNumber));
        }
        return false;
    }

    @Override
    public List<String> getAccountNumberByRelatedSn(String brandId,String relatedSn, SeparateAccountRelateTypeEnum type) {
        LambdaQueryWrapper<SeparateAccountRelatedDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountRelatedDO::getBrandId, brandId);
        queryWrapper.eq(SeparateAccountRelatedDO::getRelatedSn, relatedSn);
        queryWrapper.eq(SeparateAccountRelatedDO::getType, type.getType());
        List<SeparateAccountRelatedDO> separateAccountRelatedDOList = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(separateAccountRelatedDOList)){
            return separateAccountRelatedDOList.stream().map(SeparateAccountRelatedDO::getAccountNumber).distinct().collect(Collectors.toList());
        }
        return null;
    }


}




