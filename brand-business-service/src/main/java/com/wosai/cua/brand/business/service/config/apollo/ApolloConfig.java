package com.wosai.cua.brand.business.service.config.apollo;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.config.apollo.dto.AppIdBrandMapping;
import com.wosai.pantheon.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:
 * @Description: apollo配置
 * @version: 1.0
 */
@Component
@Data
@Slf4j
public class ApolloConfig {

    @Value("${store_tag_id}")
    private String storeTagId;

    @Value("${brand_tag_id}")
    private String brandTagId;

    @Value("${brand_tag_entity_id}")
    private String brandTagEntityId;

    @Value("${collection_mode_merchant_tag_id}")
    private String collectionModeMerchantTagId;

    @Value("${format_check_switch}")
    private Boolean formatCheckSwitch = true;

    @Value("${default_page}")
    private Integer defaultPage = 1;

    @Value("${default_page_size}")
    private Integer defaultPageSize = 10;

    @Value("${min_amount}")
    private Long minAmount = 0L;

    @Value("${max_amount}")
    private Long maxAmount = 10L;

    @Value("${base_bank_info}")
    private String bankOfDepositString;

    @Value("${mybank_activate_qrcode_url}")
    private String myBankActivateQrcodeUrl;

    @Value("${lock_expire_time}")
    private long lockExpireTime;

    @Value("${mybank_handler_switch}")
    private Boolean mybankHandlerSwitch = true;

    @Value("${fy_private_key}")
    private String fyPrivateKey;

    @Value("${fy_public_key}")
    private String fyPublicKey;

    @Value("${store_sn_check_white_list}")
    private String storeSnCheckWhiteList;

    @Value("${check_valid_chinese_id_switch}")
    private Boolean checkValidChineseIdSwitch = true;

    @Value("${appid_brand_mapping}")
    private String appidBrandMapping;

    @Value("${used_short_url}")
    private Boolean usedShortUrl = false;

    @Value("${citic_user_type_map}")
    private String citicUserTypeMap;

    @Value("${sms_template_config}")
    private String smsTemplateConfig;

    @Value("${batch_processing_merchant_count}")
    private Integer batchProcessingMerchantCount = 100;

    @Value("${auto_open_sub_accounts_switch}")
    private Boolean autoOpenSubAccountsSwitch = false;

    @Value("${citic_key_algorithm}")
    private String citicKeyAlgorithm;

    @Value("${citic_length}")
    private String citicLength;

    @Value("${citic_subject}")
    private String citicSubject;

    @Value("${citic_signature_algorithm}")
    private String citicSignatureAlgorithm;

    @Value("${citic_validity}")
    private String citicValidity;

    @Value("${citic_password_length}")
    private Integer citicPasswordLength = 6;

    @Value("${citic_public_key}")
    private String citicPublicKey;

    @Value("${sleep_time}")
    private Long sleepTime = 1000L;

    @Value("${import_merchant_task_transaction_key_expire_time}")
    private Integer importMerchantTaskTransactionKeyExpireTime = 7;

    @Value("${synchronize_separate_account_flag}")
    private Boolean synchronizeSeparateAccountFlag = true;

    @Value("${id_card_type_check_switch}")
    private Boolean idCardTypeCheckSwitch = true;


    public List<String> getStoreSnCheckWhiteList() {
        if (StringUtil.isBlank(storeSnCheckWhiteList)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(storeSnCheckWhiteList.split(","));
    }

    public List<AppIdBrandMapping> getAppIdBrandMappingList() {
        if (StringUtil.isBlank(appidBrandMapping)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(appidBrandMapping, AppIdBrandMapping.class);
    }

    public JSONObject getCiticUserTypeMap() {
        if (StringUtil.isBlank(citicUserTypeMap)){
            return new JSONObject();
        }
        return JSON.parseObject(citicUserTypeMap);
    }
}
