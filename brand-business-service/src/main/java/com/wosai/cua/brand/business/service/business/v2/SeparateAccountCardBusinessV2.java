package com.wosai.cua.brand.business.service.business.v2;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountSettlementCardDOService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.BindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.SetDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.card.ModifyAccountInCardRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.ChangeDefaultBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.UnbindBankCardRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.BindBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.SetDefaultBankCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.card.ModifyAccountInCardResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.VfinanceBaseResponse;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountSettlementCardModule;
import com.wosai.upay.bank.info.api.dto.BankInfoResponse;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.model.dto.BankImageDTO;
import com.wosai.upay.bank.service.BankService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SeparateAccountCardBusinessV2 {

    private final SeparateAccountSettlementCardDOService separateAccountSettlementCardService;

    private final SeparateAccountDOService separateAccountDOService;

    private final BankService bankService;

    private final BankInfoService bankInfoService;

    private final BrandConfigDomainService brandConfigDomainService;

    private final VfinanceInterfaceService vfinanceInterfaceService;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private final BrandDomainService brandDomainService;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = new EnumMap<>(FundManagementCompanyEnum.class);

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    public SeparateAccountCardBusinessV2(SeparateAccountSettlementCardDOService separateAccountSettlementCardService, SeparateAccountDOService separateAccountDOService, BankService bankService, BankInfoService bankInfoService, BrandConfigDomainService brandConfigDomainService, VfinanceInterfaceService vfinanceInterfaceService, List<TripartiteSystemCallService> tripartiteSystemCallServices, BrandDomainService brandDomainService, IdGeneratorSnowflake idGeneratorSnowflake) {
        this.separateAccountSettlementCardService = separateAccountSettlementCardService;
        this.separateAccountDOService = separateAccountDOService;
        this.bankService = bankService;
        this.bankInfoService = bankInfoService;
        this.brandConfigDomainService = brandConfigDomainService;
        this.vfinanceInterfaceService = vfinanceInterfaceService;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.brandDomainService = brandDomainService;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
    }

    @PostConstruct
    public void init() {
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    public List<SeparateAccountSettlementCardModule> getCardListModule(String accountNumber) {
        List<SeparateAccountSettlementCardDO> cardList = separateAccountSettlementCardService.getCardList(accountNumber);
        List<SeparateAccountSettlementCardModule> settlementCardModules = SeparateAccountSettlementCardModule.convert(cardList);
        settlementCardModules.forEach(settlementCardModule -> {
            if (StringUtils.isNotBlank(settlementCardModule.getBankName())) {
                BankImageDTO bankImage = bankService.getBankImage(settlementCardModule.getBankName());
                settlementCardModule.setBankIcon(bankImage.getBankIconImage());
                settlementCardModule.setBankBackPicture(bankImage.getBankBackImage());
            }
        });
        return settlementCardModules;
    }

    public boolean createSettlementCard(SeparateAccountSettlementCardModule separateAccountSettlementCardModule, String accountNumber,String brandId) {
        SeparateAccountDO separateAccount = separateAccountDOService.getSeparateAccount(brandId, accountNumber);
        if (Objects.isNull(separateAccount)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SEPARATE_ACCOUNT_NOT_EXIST);
        }
        // 检查下当前卡的数量
        List<SeparateAccountSettlementCardDO> cardList = separateAccountSettlementCardService.getCardList(accountNumber);
        if (CollectionUtils.isNotEmpty(cardList) && cardList.size() == 3) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.CARD_COUNT_OUT_OF_RANGE);
        }
        // 检查下是否有默认卡
        SeparateAccountSettlementCardDO defaultCard = separateAccountSettlementCardService.getDefaultCard(accountNumber);
        if (Objects.nonNull(defaultCard)) {
            separateAccountSettlementCardModule.setDefaultStatus(0);
        }else {
            separateAccountSettlementCardModule.setDefaultStatus(1);
            separateAccount.setSettleCardStatus(BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus());
        }
        if (separateAccountSettlementCardModule.getType() == 2){
            // 补充参数
            BankInfoResponse bankInfo = bankInfoService.getBankInfoByOpenNumber(separateAccountSettlementCardModule.getOpeningNumber());
            if (Objects.isNull(bankInfo)) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_NOT_EXIST);
            }
            separateAccountSettlementCardModule.setBankName(bankInfo.getBankName());
            separateAccountSettlementCardModule.setBranchName(bankInfo.getBranchName());
            separateAccountSettlementCardModule.setClearingNumber(bankInfo.getClearingNumber());
        }
        separateAccountDOService.updateSeparateAccount(separateAccount);
        return separateAccountSettlementCardService.add(SeparateAccountSettlementCardDO.convert(separateAccountSettlementCardModule));
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSettlementCard(String brandId, Long id, String accountNumber) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        SeparateAccountDO separateAccount = separateAccountDOService.getSeparateAccount(brandId, accountNumber);
        if (Objects.isNull(separateAccount)){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SEPARATE_ACCOUNT_NOT_EXIST);
        }
        SeparateAccountSettlementCardDO currentCard = separateAccountSettlementCardService.getById(id);
        if (Objects.isNull(currentCard)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        // 有多张卡的话需要判断当前卡是否是默认卡
        List<SeparateAccountSettlementCardDO> cardList = separateAccountSettlementCardService.getCardList(accountNumber);
        if (CollectionUtils.isNotEmpty(cardList) && currentCard.getDefaultStatus() == 1) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.DEFAULT_BANK_CARD_CAN_NOT_DELETE);
        }
        if (currentCard.getActiveStatus() == 1) {
            // 已激活的需要调用资管机构解绑后删除
            unbindBankCard(brandModule, separateAccount, currentCard);
        }
        return separateAccountSettlementCardService.update(new LambdaUpdateWrapper<SeparateAccountSettlementCardDO>().set(SeparateAccountSettlementCardDO::getDeleted, 1).eq(SeparateAccountSettlementCardDO::getId, id));
    }

    private void unbindBankCard(BrandModule brandModule, SeparateAccountDO separateAccount, SeparateAccountSettlementCardDO currentCard) {
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB) && StringUtils.isNotEmpty(currentCard.getThirdBankCardId()) && StringUtils.isNotEmpty(separateAccount.getMemberId())) {
            PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandModule.getBrandId(), PabConfigModule.class);
            if (Objects.nonNull(pabConfigModule)) {
                // 修改商户银行卡系统的默认卡
                UnbindBankCardRequest unbindBankCardRequest = new UnbindBankCardRequest(pabConfigModule.getPartnerId());
                unbindBankCardRequest.setBankCardId(currentCard.getThirdBankCardId());
                unbindBankCardRequest.setMemberId(separateAccount.getMemberId());
                log.info("调用维金接口UnbindBankCard，入参为：{}", JSON.toJSONString(unbindBankCardRequest));
                String s = vfinanceInterfaceService.invokeService(unbindBankCardRequest);
                VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
                if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                    log.warn("调用维金接口UnbindBankCard失败，失败原因为：{}", response.getErrorMessage());
                }
            }
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU)) {
            //富友解绑银行卡
            this.handleFuiouCard(brandModule.getBrandId(), separateAccount, currentCard, "2");
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            //中信解绑银行卡
            this.handleCiticCard(brandModule.getBrandId(), separateAccount, currentCard);
        }
    }

    private void handleCiticCard(String brandId, SeparateAccountDO separateAccount, SeparateAccountSettlementCardDO currentCard) {
        CiticBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, CiticBankConfigModule.class);
        BindBankCardRequest bindBankCardRequest = BindBankCardRequest.buildUnbindRequest(configModule, separateAccount, currentCard);
        BindBankCardResponse call = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(bindBankCardRequest, BindBankCardResponse.class, configModule);
        if (!call.isSuccess()) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, call.getResultMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultSettlementCard(String brandId, Long id, String accountNumber) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        SeparateAccountDO separateAccount = separateAccountDOService.getSeparateAccount(brandId, accountNumber);
        if (Objects.isNull(separateAccount)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SEPARATE_ACCOUNT_NOT_EXIST);
        }
        // 获取默认卡
        SeparateAccountSettlementCardDO defaultCard = separateAccountSettlementCardService.getDefaultCard(accountNumber);
        // 获取当前卡
        SeparateAccountSettlementCardDO currentCard = separateAccountSettlementCardService.getById(id);
        if (Objects.isNull(currentCard)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        if (Objects.isNull(defaultCard)) {
            currentCard.setDefaultStatus(1);
            return separateAccountSettlementCardService.updateById(currentCard);
        }
        // 情况1：如果两张卡都是未激活的，则直接修改当前卡为默认卡
        if (defaultCard.getActiveStatus() == 0 && currentCard.getActiveStatus() == 0) {
            // 先将全部卡改成非默认卡
            separateAccountSettlementCardService.update(new LambdaUpdateWrapper<SeparateAccountSettlementCardDO>().set(SeparateAccountSettlementCardDO::getDefaultStatus, 0).eq(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber));
            currentCard.setDefaultStatus(1);
            return separateAccountSettlementCardService.updateById(currentCard);
        }
        // 情况2：如果默认卡是已激活的，当前卡是未激活的，则不允许切换
        if (defaultCard.getActiveStatus() == 1 && currentCard.getActiveStatus() == 0) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.CAN_NOT_SET_DEFAULT_ACTIVATED_CARD);
        }
        // 情况3：如果默认卡是未激活的，当前卡是已激活的，需要调用资管机构设置默认卡接口
        if (defaultCard.getActiveStatus() == 0 && currentCard.getActiveStatus() == 1) {
            // 如果已激活需要调用资管机构的设置默认卡接口
            changeDefaultCard(brandModule, separateAccount, currentCard);
            separateAccountSettlementCardService.update(new LambdaUpdateWrapper<SeparateAccountSettlementCardDO>().set(SeparateAccountSettlementCardDO::getDefaultStatus, 0).eq(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber));
            currentCard.setDefaultStatus(1);
            separateAccount.setSettleCardStatus(BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus());
            separateAccountDOService.updateSeparateAccount(separateAccount);
            return separateAccountSettlementCardService.updateById(currentCard);
        }
        // 情况4：如果默认卡是已激活的，当前卡是已激活的，则先将全部卡改成非默认卡，再将当前卡改成默认卡，且调用资管机构设置默认卡接口
        if (defaultCard.getActiveStatus() == 1 && currentCard.getActiveStatus() == 1) {
            // 如果已激活需要调用资管机构的设置默认卡接口
            changeDefaultCard(brandModule, separateAccount, currentCard);
            // 先将全部卡改成非默认卡
            separateAccountSettlementCardService.update(new LambdaUpdateWrapper<SeparateAccountSettlementCardDO>().set(SeparateAccountSettlementCardDO::getDefaultStatus, 0).eq(SeparateAccountSettlementCardDO::getAccountNumber, accountNumber));
            currentCard.setDefaultStatus(1);
            return separateAccountSettlementCardService.updateById(currentCard);
        }
        return false;
    }

    private void changeDefaultCard(BrandModule brandModule, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO) {
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.PAB)) {
            this.pabSetDefault(brandModule.getBrandId(), separateAccountDO.getMemberId(), settlementCardDO);
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.CITIC)) {
            this.citicSetDefault(brandModule.getBrandId(), separateAccountDO, settlementCardDO);
        }
    }

    private void citicSetDefault(String brandId, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO) {
        CiticBankConfigModule citicBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, CiticBankConfigModule.class);
        SetDefaultBankCardRequest setDefaultBankCardRequest = SetDefaultBankCardRequest.builder(citicBankConfigModule, separateAccountDO, settlementCardDO);
        // 修改商户银行卡系统的默认卡
        SetDefaultBankCardResponse call = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(setDefaultBankCardRequest, SetDefaultBankCardResponse.class, citicBankConfigModule);
        if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(call.getResultCode())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), call.getResultMsg());
        }
    }

    private void pabSetDefault(String brandId, String memberId, SeparateAccountSettlementCardDO settlementCardDO) {
        PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, PabConfigModule.class);
        // 修改商户银行卡系统的默认卡
        ChangeDefaultBankCardRequest changeDefaultBankCardRequest = new ChangeDefaultBankCardRequest(pabConfigModule.getPartnerId());
        changeDefaultBankCardRequest.setBankCardId(settlementCardDO.getThirdBankCardId());
        changeDefaultBankCardRequest.setMemberId(memberId);
        log.info("调用维金接口ChangeDefaultBankCard，入参为：{}", JSON.toJSONString(changeDefaultBankCardRequest));
        String s = vfinanceInterfaceService.invokeService(changeDefaultBankCardRequest);
        VfinanceBaseResponse response = JSON.parseObject(s, VfinanceBaseResponse.class);
        if (VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.EXTERNAL_INTERFACE_INVOKE_FAIL.getCode(), response.getErrorMessage());
        }
    }

    public void activateSettlementCard(String brandId, Long id) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        SeparateAccountSettlementCardDO currentCard = separateAccountSettlementCardService.getById(id);
        if (Objects.isNull(currentCard)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BANK_CARD);
        }
        SeparateAccountDO separateAccount = separateAccountDOService.getSeparateAccount(brandId, currentCard.getAccountNumber());
        if (Objects.isNull(separateAccount)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SEPARATE_ACCOUNT_NOT_EXIST);
        }
        if (currentCard.getActiveStatus() == 1) {
            return;
        }
        // 调用资管机构激活接口
        switch (brandModule.getFundManagementCompanyCode()) {
            case PAB:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.OPEN_API_THIS_OPERATION_IS_NOT_SUPPORTED);
            case CITIC:
                this.bindCiticBankCard(brandId, separateAccount, currentCard);
                break;
            case MY_BANK:
                break;
            case FUIOU:
                this.handleFuiouCard(brandId, separateAccount, currentCard, "1");
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_SERVICE);
        }
        if (currentCard.getDefaultStatus() == 1){
            separateAccount.setSettleCardStatus(BankCardActivateStatusEnum.ACTIVATED.getActivateStatus());
            separateAccountDOService.updateSeparateAccount(separateAccount);
        }
    }

    private void handleFuiouCard(String brandId, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO, String type) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, FuiouConfigModule.class);
        ModifyAccountInCardRequest modifyAccountInCardRequest = new ModifyAccountInCardRequest();
        ModifyAccountInCardRequestBody body = new ModifyAccountInCardRequestBody(configModule.getMerchantNo());
        body.setTraceNo(idGeneratorSnowflake.nextSerialNumber());
        body.setAccountIn(separateAccountDO.getSubAccountNo());
        body.setOutAcntNm(settlementCardDO.getHolder());
        body.setOutAcntNo(settlementCardDO.getCardNumber());
        body.setInterBankNo(settlementCardDO.getOpeningNumber());
        body.setCheckType(fuiouCheckType);
        body.setType(type);
        body.setMobile(settlementCardDO.getCellphone());
        //个体工商户
        if ("INDIVIDUAL_BUSINESS".equals(separateAccountDO.getType())) {
            if (settlementCardDO.getType() == 1) {
                body.setOutAcntNoType("01");
                body.setShxyNo(separateAccountDO.getIdNumber());
                body.setCertNo(separateAccountDO.getLegalPersonId());
            }
            if (settlementCardDO.getType() == 2) {
                body.setOutAcntNoType("02");
//                body.setShxyNo(separateAccountDO.getIdNumber());
//                body.setCertNo(separateAccountDO.getLegalPersonId());
            }
        }
        if ("COMPANY".equals(separateAccountDO.getType())) {
            body.setShxyNo(separateAccountDO.getIdNumber());
        }
        modifyAccountInCardRequest.setBody(body);
        ModifyAccountInCardResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(modifyAccountInCardRequest, ModifyAccountInCardResponse.class, configModule);
        if (response.isSuccess()) {
            if (StringUtils.isBlank(body.getOutAcntNoType()) || "01".equals(body.getOutAcntNoType())) {
                settlementCardDO.setActiveStatus(1);
                settlementCardDO.setActivationTime(new Date());
            }
            if (StringUtils.isNotBlank(body.getOutAcntNoType()) && "02".equals(body.getOutAcntNoType())) {
                settlementCardDO.setActiveStatus(0);
            }
            settlementCardDO.setActiveFailReason(response.getBody().getKsCheckUrl());
            separateAccountSettlementCardService.updateById(settlementCardDO);
        } else {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, response.getResultMsg());
        }
    }

    private void bindCiticBankCard(String brandId, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO) {
        CiticBankConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, CiticBankConfigModule.class);
        BindBankCardRequest bindBankCardRequest;
        try {
            bindBankCardRequest = BindBankCardRequest.buildBindRequest(configModule, separateAccountDO, settlementCardDO);
        } catch (BrandBusinessException e) {
            settlementCardDO.setActiveFailReason(e.getMessage());
            separateAccountSettlementCardService.updateById(settlementCardDO);
            throw e;
        }
        BindBankCardResponse callResponse = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(bindBankCardRequest, BindBankCardResponse.class, configModule);
        if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(callResponse.getResultCode())) {
            log.error("绑定银行卡失败，失败原因：{}", callResponse.getResultMsg());
            settlementCardDO.setActiveFailReason(callResponse.getResultMsg());
            separateAccountSettlementCardService.updateById(settlementCardDO);
            throw new BrandBusinessException(BrandBusinessExceptionEnum.SYSTEM_ERROR, callResponse.getResultMsg());
        }
        if (TripartiteSystemCallResponse.ResultCodeEnum.SUCCESS.equals(callResponse.getResultCode())) {
            settlementCardDO.setActiveStatus(1);
            settlementCardDO.setThirdBankCardId(bindBankCardRequest.getPan());
            settlementCardDO.setActivationTime(new Date());
            settlementCardDO.setActiveFailReason("");
            separateAccountSettlementCardService.updateById(settlementCardDO);
        }
    }

    public SeparateAccountSettlementCardModule getDefaultCardModule(String accountNumber) {
        SeparateAccountSettlementCardDO defaultCard = separateAccountSettlementCardService.getDefaultCard(accountNumber);
        if (Objects.isNull(defaultCard)) {
            return null;
        }
        SeparateAccountSettlementCardModule separateAccountSettlementCardModule = SeparateAccountSettlementCardModule.convert(defaultCard);
        if (StringUtils.isNotBlank(separateAccountSettlementCardModule.getBankName())) {
            BankImageDTO bankImage = bankService.getBankImage(separateAccountSettlementCardModule.getBankName());
            separateAccountSettlementCardModule.setBankIcon(bankImage.getBankIconImage());
            separateAccountSettlementCardModule.setBankBackPicture(bankImage.getBankBackImage());
        }
        return separateAccountSettlementCardModule;
    }
}
