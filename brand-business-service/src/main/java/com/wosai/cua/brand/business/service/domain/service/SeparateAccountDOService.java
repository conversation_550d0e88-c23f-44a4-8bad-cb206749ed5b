package com.wosai.cua.brand.business.service.domain.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wosai.cua.brand.business.service.domain.entity.QueryPage;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountQueryDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【separate_account(分账账户信息表)】的数据库操作Service
* @createDate 2025-06-05 10:17:43
*/
public interface SeparateAccountDOService extends IService<SeparateAccountDO> {

    /**
     * 添加分账账户
     * @param separateAccountDO 分账账户对象
     * @return 添加结果
     */
    boolean addSeparateAccount(SeparateAccountDO separateAccountDO);

    /**
     * 分页获取分账账户列表
     * @param query 分账账户查询条件
     * @return 分账账户列表
     */
    QueryPage<SeparateAccountDO> pageList(Page<SeparateAccountDO> page, SeparateAccountQueryDO query);

    /**
     * 获取分账账户
     * @param accountNumber 分账账户编号
     * @return 分账账户
     */
    SeparateAccountDO getSeparateAccount(String brandId,String accountNumber);

    /**
     * 更新分账账户
     * @param separateAccountDO 分账账户
     * @return 是否更新成功
     */
    boolean updateSeparateAccount(SeparateAccountDO separateAccountDO);

    /**
     * 获取分账账户
     * @param query 查询条件
     * @return 分账账户列表
     */
    List<SeparateAccountDO> getSeparateAccountListByBrandIdAndIdNumber(SeparateAccountQueryDO query);
}
