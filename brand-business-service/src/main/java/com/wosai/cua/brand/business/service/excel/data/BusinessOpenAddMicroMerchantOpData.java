package com.wosai.cua.brand.business.service.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.service.excel.converters.MerchantTypeEnumConverter;
import com.wosai.cua.brand.business.service.excel.groups.Default;
import com.wosai.cua.brand.business.service.excel.groups.Indirect;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Data
public class BusinessOpenAddMicroMerchantOpData {

    @ExcelProperty("序号")
    @NotEmpty(message = "序号为空", groups = Default.class)
    private String sequenceNo;
    @ExcelProperty(value = "合作关系", converter = MerchantTypeEnumConverter.class)
    @NotNull(message = "合作关系", groups = Default.class)
    private MerchantTypeEnum cooperation;
    @NotEmpty(message = "商户经营名称为空", groups = Default.class)
    @ExcelProperty("商户经营名称")
    private String businessName;
    @ExcelProperty("行业代码")
    @NotEmpty(message = "行业代码为空", groups = Default.class)
    private String industryCode;
    @ExcelProperty("所在地区代码")
    @NotEmpty(message = "所在地区代码为空", groups = Default.class)
    private String districtCode;
    @ExcelProperty("详细地址")
    @NotEmpty(message = "详细地址为空", groups = Default.class)
    private String streetAddress;
    @ExcelProperty("联系人")
    @NotEmpty(message = "联系人为空", groups = Default.class)
    private String contactName;
    @ExcelProperty("联系电话")
    @NotEmpty(message = "联系电话为空", groups = Default.class)
    private String contactPhone;
    @ExcelProperty("超级管理员登录账号")
    @NotEmpty(message = "超级管理员登录账号为空", groups = Default.class)
    private String loginPhone;
    @ExcelProperty("身份证姓名")
    @NotEmpty(message = "身份证姓名为空", groups = Default.class)
    private String holder;
    @ExcelProperty("身份证号")
    @NotEmpty(message = "身份证号为空", groups = Default.class)
    private String identity;
    @ExcelProperty("证件有效开始日期")
    @NotEmpty(message = "证件有效开始日期为空", groups = Default.class)
    private String idValidityStart;
    @ExcelProperty("证件有效截止日期")
    @NotEmpty(message = "证件有效截止日期为空", groups = Default.class)
    private String idValidityEnd;
    @ExcelProperty("银行卡号")
    @NotEmpty(message = "银行卡号为空", groups = Indirect.class)
    private String number;
    @ExcelProperty("开户行行号")
    @NotEmpty(message = "开户行行号为空", groups = Indirect.class)
    private String openingNumber;
    @ExcelProperty("银行卡有效期（年/月）")
    @NotEmpty(message = "银行卡有效期（年/月）为空", groups = Indirect.class)
    private String cardValidity;


    // ---以下是导入结果
    @ExcelProperty("商户号")
    private String merchantSn;
    @ExcelProperty("商户ID")
    private String merchantId;
    @ExcelProperty("间连扫码结果")
    private String indirectResult;
}
