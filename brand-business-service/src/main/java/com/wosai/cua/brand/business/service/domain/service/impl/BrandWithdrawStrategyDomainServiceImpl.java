package com.wosai.cua.brand.business.service.domain.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.WithdrawCycleTypeEnum;
import com.wosai.cua.brand.business.service.domain.dao.BrandWithdrawStrategyMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandWithdrawStrategyDO;
import com.wosai.cua.brand.business.service.domain.service.BrandWithdrawStrategyDomainService;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.cua.brand.business.service.module.withdraw.PageBrandWithdrawStrategyModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class BrandWithdrawStrategyDomainServiceImpl implements BrandWithdrawStrategyDomainService {

    private final BrandWithdrawStrategyMapper brandWithdrawStrategyMapper;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    @Autowired
    public BrandWithdrawStrategyDomainServiceImpl(BrandWithdrawStrategyMapper brandWithdrawStrategyMapper, IdGeneratorSnowflake idGeneratorSnowflake) {
        this.brandWithdrawStrategyMapper = brandWithdrawStrategyMapper;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
    }

    @Override
    public Long createBrandWithdrawStrategy(BrandWithdrawStrategyModule brandWithdrawStrategyModule) {
        long strategyId = idGeneratorSnowflake.nextId();
        brandWithdrawStrategyModule.setStrategyId(strategyId);
        brandWithdrawStrategyMapper.insert(JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), BrandWithdrawStrategyDO.class));
        return strategyId;
    }

    @Override
    public Long modifyBrandWithdrawStrategy(BrandWithdrawStrategyModule brandWithdrawStrategyModule) {
        brandWithdrawStrategyMapper.updateBrandWithdrawStrategyById(JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), BrandWithdrawStrategyDO.class));
        return brandWithdrawStrategyModule.getStrategyId();
    }

    @Override
    public BrandWithdrawStrategyModule getBrandWithdrawStrategyModuleByStrategyId(Long strategyId) {
        BrandWithdrawStrategyDO brandWithdrawStrategy = brandWithdrawStrategyMapper.getBrandWithdrawStrategyByStrategyId(strategyId);
        if (Objects.isNull(brandWithdrawStrategy)) {
            return null;
        }
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = BrandWithdrawStrategyModule.convert(brandWithdrawStrategy);
        if (StringUtils.isNotEmpty(brandWithdrawStrategyModule.getWithdrawCycleTimes())){
            String[] split = brandWithdrawStrategyModule.getWithdrawCycleTimes().split(",");
            brandWithdrawStrategyModule.setWithdrawCycleDesc(WithdrawCycleTypeEnum.getDescByWithdrawCycleTypeAndNumbers(brandWithdrawStrategyModule.getWithdrawCycleType(), Lists.newArrayList(split)));
        }
        return brandWithdrawStrategyModule;
    }

    @Override
    public List<BrandWithdrawStrategyModule> getBrandWithdrawStrategyModuleByBrandId(String brandId) {
        List<BrandWithdrawStrategyDO> strategyList = brandWithdrawStrategyMapper.getBrandWithdrawStrategyListByBrandId(brandId);
        return this.getBrandWithdrawStrategyModules(strategyList);
    }

    private List<BrandWithdrawStrategyModule> getBrandWithdrawStrategyModules(List<BrandWithdrawStrategyDO> strategyList) {
        if (CollectionUtils.isEmpty(strategyList)) {
            return Lists.newArrayList();
        }
        List<BrandWithdrawStrategyModule> brandWithdrawStrategyModules = BrandWithdrawStrategyModule.convert(strategyList);
        brandWithdrawStrategyModules.forEach(brandWithdrawStrategyModule -> {
            if (StringUtils.isNotEmpty(brandWithdrawStrategyModule.getWithdrawCycleTimes())){
                String[] split = brandWithdrawStrategyModule.getWithdrawCycleTimes().split(",");
                brandWithdrawStrategyModule.setWithdrawCycleDesc(WithdrawCycleTypeEnum.getDescByWithdrawCycleTypeAndNumbers(brandWithdrawStrategyModule.getWithdrawCycleType(), Lists.newArrayList(split)));
            }
            brandWithdrawStrategyModule.setWithdrawStrategyDesc(this.getWithdrawStrategyDesc(brandWithdrawStrategyModule));
        });
        return brandWithdrawStrategyModules;
    }

    @Override
    public int deleteBrandWithdrawStrategyByStrategyIdList(List<Long> strategyIdList) {
        return brandWithdrawStrategyMapper.deleteStrategyByStrategyIdList(strategyIdList);
    }

    @Override
    public List<BrandWithdrawStrategyModule> getWithdrawStrategyListByStrategyIdList(List<Long> strategyIdList) {
        if (CollectionUtils.isEmpty(strategyIdList)) {
            return Lists.newArrayList();
        }
        List<BrandWithdrawStrategyDO> brandWithdrawStrategyByStrategyIdList = brandWithdrawStrategyMapper.getBrandWithdrawStrategyByStrategyIdList(strategyIdList);
        return this.getBrandWithdrawStrategyModules(brandWithdrawStrategyByStrategyIdList);
    }

    @Override
    public PageBrandWithdrawStrategyModule pageBrandWithdrawStrategy(int page, int pageSize, String brandId) {
        int total = brandWithdrawStrategyMapper.countStrategyByBrandId(brandId);
        List<BrandWithdrawStrategyDO> strategyList = brandWithdrawStrategyMapper.pageStrategyByBrandId(brandId, (page - 1) * pageSize, pageSize);
        List<BrandWithdrawStrategyModule> brandWithdrawStrategyModules = this.getBrandWithdrawStrategyModules(strategyList);
        PageBrandWithdrawStrategyModule module = new PageBrandWithdrawStrategyModule();
        module.setTotal(total);
        module.setRecords(brandWithdrawStrategyModules);
        return module;
    }

    private String getWithdrawStrategyDesc(BrandWithdrawStrategyModule brandWithdrawStrategyModule) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(brandWithdrawStrategyModule.getApplicableSceneDesc());
        if (StringUtils.isNotEmpty(brandWithdrawStrategyModule.getWithdrawTypeDesc())) {
            stringBuilder.append("/");
            stringBuilder.append(brandWithdrawStrategyModule.getWithdrawTypeDesc());
        }
        if (StringUtils.isNotEmpty(brandWithdrawStrategyModule.getWithdrawCycleDesc())) {
            stringBuilder.append("/");
            stringBuilder.append(brandWithdrawStrategyModule.getWithdrawCycleDesc());
        }
        if (Objects.nonNull(brandWithdrawStrategyModule.getMinWithdrawalAmount()) && brandWithdrawStrategyModule.getMinWithdrawalAmount() > 0){
            stringBuilder.append("/");
            stringBuilder.append("最少提现");
            stringBuilder.append(new BigDecimal(brandWithdrawStrategyModule.getMinWithdrawalAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            stringBuilder.append("元");
        }
        if (Objects.nonNull(brandWithdrawStrategyModule.getReservedAmount()) && brandWithdrawStrategyModule.getReservedAmount() > 0) {
            stringBuilder.append("/");
            stringBuilder.append("提现预留");
            stringBuilder.append(new BigDecimal(brandWithdrawStrategyModule.getReservedAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            stringBuilder.append("元");
        }
        return stringBuilder.toString();
    }


}
