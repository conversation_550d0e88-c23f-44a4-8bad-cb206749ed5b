package com.wosai.cua.brand.business.service.helper;

import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.core.crypto.util.SignUtil;
import com.wosai.upay.common.util.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class CryptHelper implements InitializingBean {

    @Autowired
    private CryptoService cryptoService;

    private CryptoClient cryptoClient;

    @Value("${core.crypto.client.access_id}")
    private String accessId;

    @Value("${core.crypto.client.access_secret}")
    private String accessSecret;

    @Override
    public void afterPropertiesSet() {
        cryptoClient = new CryptoClient(cryptoService, accessId, accessSecret);
    }

    public String encrypt( String value) {
        if (StringUtils.isEmpty(value)){
            return "";
        }
        return cryptoClient.encrypt(value);
    }

    public String decrypt(String value){
        if (StringUtils.isEmpty(value)){
            return value;
        }
        return cryptoClient.decrypt(value);
    }


    public static String mask(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        int length = input.length();

        if (length <= 4) {
            return repeatStar(length);
        } else {
            return "****" + input.substring(length - 4);
        }
    }

    private static String repeatStar(int count) {
        char[] stars = new char[count];
        java.util.Arrays.fill(stars, '*');
        return new String(stars);
    }
    public static void main(String[] args) {
        Map<String, Object> request = new HashMap();
        request.put("plaintext", "18538182060");
        request.put("access_id", "a80c8c7c-cd08-4426-b866-c0f2d1e2f9df");
        request.put("sign", SignUtil.getSign(request, "********************************", "utf8"));
        System.out.println(JacksonUtil.toJsonString(request));
    }
}
