package com.wosai.cua.brand.business.service.helper;

import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.MerchantUserClient;
import com.wosai.cua.brand.business.service.externalservice.merchantuser.model.GroupQueryResult;
import com.wosai.cua.brand.business.service.externalservice.salessystem.SalesSystemClient;
import com.wosai.cua.brand.business.service.kafka.DataCenterKafkaProducer;
import com.wosai.cua.brand.business.service.kafka.dto.DataCenterMessageBody;
import com.wosai.cua.brand.business.service.kafka.dto.MerchantBrandPushToVolcanoParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MerchantBrandPushHelper {

    @Autowired
    private DataCenterKafkaProducer dataCenterKafkaProducer;

    @Autowired
    private SalesSystemClient salesSystemClient;

    @Autowired
    private BrandMerchantMapper brandMerchantMapper;

    @Autowired
    private MerchantUserClient merchantUserClient;

    @Autowired
    private BrandMapper brandMapper;

    public void pushBrandMerchant(List<String> merchantIds) {
        // 去重
        merchantIds = merchantIds.stream().distinct().collect(Collectors.toList());
        QueryMerchantConditionsDO queryMerchantConditions = QueryMerchantConditionsDO.builder()
                .merchantIds(merchantIds)
                .deleted(0)
                .build();
        List<BrandMerchantDO> brandMerchantDOS = brandMerchantMapper.selectBrandMerchantByConditions(queryMerchantConditions);
        // 按 merchantId 分组
        Map<String, List<BrandMerchantDO>> brandMerchantByMerchantId = CollectionUtils.isEmpty(brandMerchantDOS)
                ? Collections.emptyMap()
                : brandMerchantDOS.stream().collect(Collectors.groupingBy(BrandMerchantDO::getMerchantId));
        // 每个 merchant 都发一次
        merchantIds.parallelStream().forEach(merchantId -> {
            List<BrandMerchantDO> brandMerchantDOList = brandMerchantByMerchantId.get(merchantId);
            List<BrandDO> brandDOList;
            if (CollectionUtils.isEmpty(brandMerchantDOList)) {
                brandDOList = Collections.emptyList();
            } else {
                List<String> brandIdList = brandMerchantDOList.stream()
                        .map(BrandMerchantDO::getBrandId)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());
                if (brandIdList.isEmpty()) {
                    brandDOList = Collections.emptyList();
                } else {
                    brandDOList = brandMapper.selectBrandByBrandIdListOrBrandSnList(brandIdList, null);
                    if (CollectionUtils.isEmpty(brandDOList)) {
                        brandDOList = Collections.emptyList();
                    }
                }
            }
            MerchantBrandPushToVolcanoParams params = new MerchantBrandPushToVolcanoParams();
            params.setBrandNames(brandDOList.stream().map(BrandDO::getName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            params.setBrandSns(brandDOList.stream().map(BrandDO::getSn).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            params.setBrandOrganizations(brandDOList.stream()
                    .map(b -> salesSystemClient.queryOrganizationNamePath(b.getOrganizationId())).filter(Objects::nonNull).collect(Collectors.toList()));
            params.setGroupSns(brandDOList.stream()
                    .map(b -> merchantUserClient.getGroupInfoByGroupId(b.getGroupId())).filter(Objects::nonNull).map(GroupQueryResult::getGroupSn).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            params.setGroupNames(brandDOList.stream()
                    .map(b -> merchantUserClient.getGroupInfoByGroupId(b.getGroupId())).filter(Objects::nonNull).map(GroupQueryResult::getGroupName).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            params.setGroupOrganizations(brandDOList.stream()
                    .map(b -> merchantUserClient.getGroupInfoByGroupId(b.getGroupId())).filter(Objects::nonNull).map(GroupQueryResult::getOrganizationId).map(b -> salesSystemClient.queryOrganizationNamePath(b)).filter(Objects::nonNull).distinct().collect(Collectors.toList()));

            dataCenterKafkaProducer.publishMerchantAnalyzeProfile(
                    DataCenterMessageBody.builder()
                            .userUniqueId(merchantId)
                            .eventParams(params)
                            .build()
            );
        });
    }
}
