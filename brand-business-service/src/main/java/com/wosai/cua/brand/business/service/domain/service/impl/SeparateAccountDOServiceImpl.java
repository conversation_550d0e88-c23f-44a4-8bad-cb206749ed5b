package com.wosai.cua.brand.business.service.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveMethod;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.QueryPage;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountQueryDO;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【separate_account(分账账户信息表)】的数据库操作Service实现
 * @createDate 2025-06-05 10:17:43
 */
@Service
@SensitiveClass
@Slf4j
public class SeparateAccountDOServiceImpl extends ServiceImpl<SeparateAccountDOMapper, SeparateAccountDO>
        implements SeparateAccountDOService {

    @Override
    @SensitiveMethod
    public boolean addSeparateAccount(SeparateAccountDO separateAccountDO) {
        try {
            save(separateAccountDO);
        } catch (Exception e) {
            log.error("添加分账账户信息失败", e);
            return false;
        }
        return true;
    }

    @Override
    @SensitiveMethod
    public QueryPage<SeparateAccountDO> pageList(Page<SeparateAccountDO> page, SeparateAccountQueryDO query) {
        LambdaQueryWrapper<SeparateAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(query.getSelectedIds())) {
            queryWrapper.in(SeparateAccountDO::getId, query.getSelectedIds());
        }
        if (CollectionUtils.isNotEmpty(query.getExcludeIds())) {
            queryWrapper.notIn(SeparateAccountDO::getId, query.getExcludeIds());
        }
        queryWrapper.eq(SeparateAccountDO::getBrandId, query.getBrandId());
        if (StringUtils.isNotBlank(query.getAccountNumber())) {
            queryWrapper.eq(SeparateAccountDO::getAccountNumber, query.getAccountNumber());
        }
        if (StringUtils.isNotBlank(query.getAccountName())) {
            queryWrapper.like(SeparateAccountDO::getAccountName, query.getAccountName());
        }
        if (StringUtils.isNotBlank(query.getAccountOpenStatus())) {
            queryWrapper.eq(SeparateAccountDO::getAccountOpenStatus, query.getAccountOpenStatus());
        }
        if (StringUtils.isNotBlank(query.getSettleCardStatus())) {
            queryWrapper.eq(SeparateAccountDO::getSettleCardStatus, query.getSettleCardStatus());
        }
        queryWrapper.eq(SeparateAccountDO::getDeleted, 0);
        Page<SeparateAccountDO> separateAccountDOPage = page(page, queryWrapper);
        QueryPage<SeparateAccountDO> queryPage = new QueryPage<>();
        queryPage.setRecords(separateAccountDOPage.getRecords());
        queryPage.setTotal(separateAccountDOPage.getTotal());
        return queryPage;
    }

    @Override
    @SensitiveMethod
    public SeparateAccountDO getSeparateAccount(String brandId,String accountNumber) {
        LambdaQueryWrapper<SeparateAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(brandId)){
            queryWrapper.eq(SeparateAccountDO::getBrandId, brandId);
        }
        queryWrapper.eq(SeparateAccountDO::getAccountNumber, accountNumber).eq(SeparateAccountDO::getDeleted, 0);
        List<SeparateAccountDO> list = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    @SensitiveMethod
    public boolean updateSeparateAccount(SeparateAccountDO separateAccountDO) {
        return updateById(separateAccountDO);
    }

    @Override
    @SensitiveMethod
    public List<SeparateAccountDO> getSeparateAccountListByBrandIdAndIdNumber(SeparateAccountQueryDO query) {
        if (Objects.isNull(query)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<SeparateAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(query.getBrandId())){
            queryWrapper.eq(SeparateAccountDO::getBrandId, query.getBrandId());
        }
        if (StringUtils.isNotBlank(query.getIdNumber())){
            queryWrapper.like(SeparateAccountDO::getIdNumber, query.getIdNumber());
        }
        if (StringUtils.isNotBlank(query.getIdType())){
            queryWrapper.eq(SeparateAccountDO::getIdType, query.getIdType());
        }
        if (CollectionUtils.isNotEmpty(query.getExcludeIds())){
            queryWrapper.notIn(SeparateAccountDO::getId, query.getExcludeIds());
        }
        queryWrapper.eq(SeparateAccountDO::getDeleted, 0);
        return list(queryWrapper);
    }
}




