package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.PageSeparateAccountInfoRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.QuerySeparateAccountRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageSeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.SeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.SeparateAccountFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountBusinessV2;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountCardBusinessV2;
import com.wosai.cua.brand.business.service.domain.entity.QueryPage;
import com.wosai.cua.brand.business.service.module.separate.account.PageSeparateAccountQueryModule;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class SeparateAccountFacadeImpl implements SeparateAccountFacade {

    private final SeparateAccountBusinessV2 separateAccountBusiness;

    private final SeparateAccountCardBusinessV2 separateAccountCardBusiness;

    private final BrandBusiness brandBusiness;

    @Autowired
    public SeparateAccountFacadeImpl(SeparateAccountBusinessV2 separateAccountBusiness, SeparateAccountCardBusinessV2 separateAccountCardBusiness, BrandBusiness brandBusiness) {
        this.separateAccountBusiness = separateAccountBusiness;
        this.separateAccountCardBusiness = separateAccountCardBusiness;
        this.brandBusiness = brandBusiness;
    }

    @Override
    public PageSeparateAccountInfoResponseDTO pageSeparateAccountInfo(PageSeparateAccountInfoRequestDTO request) {
        QueryPage<SeparateAccountModule> separateAccountModulePage = separateAccountBusiness.pageSeparateAccountInfo(PageSeparateAccountQueryModule.build(request));
        return new PageSeparateAccountInfoResponseDTO(separateAccountModulePage.getTotal(), SeparateAccountModule.fromModuleList(separateAccountModulePage.getRecords()));
    }

    @Override
    public SeparateAccountInfoResponseDTO getSeparateAccountInfoByAccountNumber(QuerySeparateAccountRequestDTO request) {
        if (StringUtils.isBlank(request.getAccountNumber())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "账户编号不能为空");
        }
        SeparateAccountModule separateAccountModule = separateAccountBusiness.getSeparateAccountInfoByAccountNumber(request.getBrandId(), request.getAccountNumber());
        if (Objects.isNull(separateAccountModule)){
            return null;
        }
        SeparateAccountInfoResponseDTO response = SeparateAccountModule.fromModule(separateAccountModule);
        if (Objects.isNull(response)){
            return null;
        }
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(separateAccountModule.getBrandId(), false);
        response.setBrandName(brandDetailInfo.getName());
        response.setBrandSn(brandDetailInfo.getSn());
        return response;
    }
}
