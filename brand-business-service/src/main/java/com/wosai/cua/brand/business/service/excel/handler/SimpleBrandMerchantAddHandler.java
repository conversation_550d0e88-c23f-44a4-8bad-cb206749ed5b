package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.ImportExcelAuditContext;
import com.wosai.cua.brand.business.service.excel.data.SimpleBrandMerchantOpData;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class SimpleBrandMerchantAddHandler extends AbstractImportExcelHandler<ImportExcelAuditContext, SimpleBrandMerchantOpData> {

    @Autowired
    private BrandDomainService brandDomainService;
    @Autowired
    private MerchantCenterClient merchantCenterClient;

    @Override
    public ImportExcelAuditContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return ImportExcelAuditContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.RECONCILIATION_ADD_MERCHANT;
    }

    @Override
    public void preCheck(ImportExcelAuditContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        context.bindBrandModule(brandModule);
    }

    @Override
    protected void doHandleData(ImportExcelAuditContext context, BrandSubTaskModule brandSubTaskModule) {
        String brandId = context.getBrandModule().getBrandId();
        String parentId = context.getBrandModule().getParentId();
        SimpleBrandMerchantOpData data = JSON.parseObject(brandSubTaskModule.getSubTaskContext(), SimpleBrandMerchantOpData.class);
        try {
            MerchantInfoQueryResult merchantInfo = merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(new MerchantInfoQueryRequest().setMerchantSn(data.getMerchantSn()));
            if (Objects.isNull(merchantInfo)) {
                data.setResult("失败:商户不存在");
            } else {
                BrandMerchantModule brandMerchantModule = brandDomainService.getBrandMerchantByBrandIdAndMerchantId(brandId, merchantInfo.getMerchantId());
                if (Objects.nonNull(brandMerchantModule)) {
                    data.setResult("失败:商户已关联该品牌");
                } else {
                    brandDomainService.createBrandMerchantFromAudit(new BrandMerchantModule().setBrandId(brandId)
                            .setParentBrandId(parentId)
                            .setMerchantId(merchantInfo.getMerchantId())
                            .setMerchantName(merchantInfo.getName())
                            .setMerchantSn(merchantInfo.getMerchantSn())
                            .setPaymentMode(PaymentModeEnum.PAYMENT_MODE_RECONCILIATION.getCode())
                            .setType(Integer.valueOf(0).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.PERSONAL.getType() : Integer.valueOf(1).equals(merchantInfo.getMerchantType()) ? BrandMerchantTypeEnum.INDIVIDUAL_BUSINESS.getType() : BrandMerchantTypeEnum.COMPANY.getType())
                            .setMerchantType(MerchantTypeEnum.BRAND_OPERATED_STORES.getMerchantType()), context.getAuditSn());
                    data.setResult("成功");
                }
            }
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", brandSubTaskModule.getId(), e);
            data.setResult("失败：" + e.getMessage());
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败:" + e.getMessage())));
        }
    }
}
