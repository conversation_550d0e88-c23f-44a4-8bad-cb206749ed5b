package com.wosai.cua.brand.business.service.externalservice.salessystem;

import com.alibaba.fastjson2.JSON;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.service.externalservice.salessystem.model.SalesUserModel;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.core.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
@Slf4j
@Component
public class SalesSystemClient {

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizationService organizationService;

    public SalesUserModel queryUser(String userId) {
        Map user = userService.getSimpleUser(userId);
        if (WosaiMapUtils.isEmpty(user)) {
            throw new CommonInvalidParameterException("用户信息不存在");
        }
        return JSON.parseObject(JSON.toJSONString(user), SalesUserModel.class);
    }

    public String queryOrganizationNamePath(String organizationId) {
        if (StringUtils.isEmpty(organizationId)) {
            return null;
        }
        Map<String, Object> organization = organizationService.getOrganization(organizationId);
        if (MapUtils.isEmpty(organization)) {
            log.warn("未查询到组织，organizationId:{}", organizationId);
            return null;
        }
        return MapUtils.getString(organization, "name_path");
    }

}
