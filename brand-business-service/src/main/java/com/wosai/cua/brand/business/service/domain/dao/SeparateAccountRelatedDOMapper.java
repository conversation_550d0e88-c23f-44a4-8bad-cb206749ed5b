package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;

/**
* <AUTHOR>
* @description 针对表【separate_account_related(分账账户关联表)】的数据库操作Mapper
* @createDate 2025-06-05 10:17:57
* @Entity com.wosai.cua.brand.business.service.domain.entity.AccountAllocationRelatedDO
*/
public interface SeparateAccountRelatedDOMapper extends BaseMapper<SeparateAccountRelatedDO> {

}




