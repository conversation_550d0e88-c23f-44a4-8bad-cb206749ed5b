package com.wosai.cua.brand.business.service.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum IndividualBusinessExcelFieldEnum {
    /**
     *
     */
    BRAND_MERCHANT_TYPE(0,"brandMerchantType","未填写或填写错误品牌商户类型",true, false, "", ""),
    MERCHANT_NAME(1,"merchantName","未填写品牌商户名称！",true, false, "", ""),

    /**
     *
     */
    PROVINCE(2,"province","未填写所在省份",true, false, "", ""),
    /**
     *
     */
    CITY(3,"city","未填写所在市",true, false, "", ""),
    /**
     *
     */
    DISTRICT(4,"district","",false, false, "", ""),

    MERCHANT_ADDRESS(5,"address","未填写商户地址",true, false, "", ""),
    /**
     *
     */
    LICENSE_TYPE(6,"licenseType","未填写证照类型或证照类型填写错误",true, false, "", ""),
    /**
     *
     */
    LICENSE_NUMBER(7,"licenseNumber","未填写证件号码",true, false, "", ""),

    /**
     *
     */
    CONTACT_NAME(8,"contactName","未填写联系人姓名",true, false, "", ""),
    /**
     *
     */
    CONTACT_PHONE(9,"contactPhone","未填写联系人手机号",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "联系人手机号格式错误"),

    CONTACT_EMAIL(10,"contactEmail","",false, true, "^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$", "联系人邮箱格式不正确"),

    /**
     *
     */
    LEGAL_PERSON_NAME(11,"legalPersonName","法人姓名未填写",true, false, "", ""),
    /**
     *
     */
    LEGAL_PERSON_LICENSE_TYPE(12,"legalPersonLicenseType","法人证件类型未填写或未填写正确内容",true, false, "", ""),
    /**
     *
     */
    LEGAL_PERSON_LICENSE_NUMBER(13,"legalPersonLicenseNumber","法人证件号未填写",true, false, "", ""),
    BANK_CARD_TYPE(14,"bankCardType","银行卡类型未填写或未填写正确内容",true, false, "", ""),
    CORRESPONDENT_NUMBER(15,"openingNumber","银行卡开户行联行号未填写",false, false, "", ""),
    BANK_NUMBER(16,"bankNumber","银行卡卡号未填写",true, false, "", ""),
    CELL_PHONE_NUMBER(17,"cellPhoneNumber","预留手机号未填写",true, true, "^(13[0-9]|14[579]|15[*********]|16[6]|17[0135678]|18[0-9]|19[189])\\d{8}$", "预留手机号格式错误"),
    BANK_NAME(18,"bankName","银行卡开户行未填写",true, false, "", ""),
    STORE_NAME(19,"storeSn","门店编号未填写",true, false, "", ""),
    /**
     *
     */
    MEI_TUAN_STORE_SN(20,"meiTuanStoreSn","",false, false, "", ""),
    /**
     *
     */
    ELM_STORE_SN(21,"elmStoreSn","",false, false, "", ""),
    DY_STORE_SN(22,"dyStoreSn","",false, false, "", ""),
    /**
     *
     */
    OUT_MERCHANT_ID(23,"outMerchantNo","",false, false, "", ""),
    ;

    private final int columnNo;

    private final String fieldName;

    private final String checkMessage;

    private final boolean needRequired;

    private final boolean needFormatCheck;

    private final String regex;

    private final String formatCheckMsg;

    IndividualBusinessExcelFieldEnum(int columnNo, String fieldName, String checkMessage, boolean needRequired, boolean needFormatCheck, String regex, String formatCheckMsg) {
        this.columnNo = columnNo;
        this.fieldName = fieldName;
        this.checkMessage = checkMessage;
        this.needRequired = needRequired;
        this.needFormatCheck = needFormatCheck;
        this.regex = regex;
        this.formatCheckMsg = formatCheckMsg;
    }
}
