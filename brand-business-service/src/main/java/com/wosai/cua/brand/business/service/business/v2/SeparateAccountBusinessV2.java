package com.wosai.cua.brand.business.service.business.v2;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.domain.entity.QueryPage;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountQueryDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.service.BrandConfigDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountRelatedDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountSettlementCardDOService;
import com.wosai.cua.brand.business.service.domain.service.TripartiteSystemCallService;
import com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService;
import com.wosai.cua.brand.business.service.domain.tripartite.request.citic.RegisteredUserRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.CloseAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.InvalidAllocateAccountRequestBody;
import com.wosai.cua.brand.business.service.domain.tripartite.request.fuiou.account.OpenAccountRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantAppletPreRegisterRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.vfinance.CreateMemberRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.response.TripartiteSystemCallResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.citic.RegisteredResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.CloseAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.InvalidAllocateAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.fuiou.account.OpenAccountResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.mybank.merchant.MerchantAppletPreRegisterResponse;
import com.wosai.cua.brand.business.service.domain.tripartite.response.vfinance.CreateMemberResponse;
import com.wosai.cua.brand.business.service.enums.RedisKeyEnum;
import com.wosai.cua.brand.business.service.enums.VFinanceResponseCodeEnum;
import com.wosai.cua.brand.business.service.helper.CommonHelper;
import com.wosai.cua.brand.business.service.helper.IdGeneratorSnowflake;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantCreationRecordModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.fuiou.FuiouConfigModule;
import com.wosai.cua.brand.business.service.module.config.mybank.MyBankConfigModule;
import com.wosai.cua.brand.business.service.module.config.pab.PabConfigModule;
import com.wosai.cua.brand.business.service.module.separate.account.OpenSeparateAccountModule;
import com.wosai.cua.brand.business.service.module.separate.account.OpenSeparateAccountResultModule;
import com.wosai.cua.brand.business.service.module.separate.account.PageSeparateAccountQueryModule;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountModule;
import com.wosai.cua.brand.business.service.module.tripartite.fuiou.FuiouToBeActiveRecordModule;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.bank.info.api.dto.BankInfoResponse;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.model.dto.BankImageDTO;
import com.wosai.upay.bank.service.BankService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SeparateAccountBusinessV2 {

    private final SeparateAccountDOService separateAccountService;

    private final SeparateAccountRelatedDOService separateAccountRelatedService;

    private final SeparateAccountSettlementCardDOService separateAccountSettlementCardService;

    private final BrandDomainService brandDomainService;

    private final IdGeneratorSnowflake idGeneratorSnowflake;

    private final StoreService storeService;

    private final BankInfoService bankInfoService;

    private final BankService bankService;

    private final ApolloConfig apolloConfig;

    private final BrandConfigDomainService brandConfigDomainService;

    private final CommonHelper commonHelper;

    private final List<TripartiteSystemCallService> tripartiteSystemCallServices;

    private final RedisHelper redisHelper;

    @Value("${fuiou.open.account.check_type}")
    private String fuiouCheckType;

    private static final Map<FundManagementCompanyEnum, TripartiteSystemCallService> TRIPARTITE_SYSTEM_CALL_SERVICE_MAP = Maps.newConcurrentMap();

    @Autowired
    public SeparateAccountBusinessV2(SeparateAccountDOService separateAccountService, SeparateAccountRelatedDOService separateAccountRelatedService, SeparateAccountSettlementCardDOService separateAccountSettlementCardService, BrandDomainService brandDomainService, IdGeneratorSnowflake idGeneratorSnowflake, StoreService storeService, BankInfoService bankInfoService, BankService bankService, ApolloConfig apolloConfig, BrandConfigDomainService brandConfigDomainService, CommonHelper commonHelper, List<TripartiteSystemCallService> tripartiteSystemCallServices, RedisHelper redisHelper) {
        this.separateAccountService = separateAccountService;
        this.separateAccountRelatedService = separateAccountRelatedService;
        this.separateAccountSettlementCardService = separateAccountSettlementCardService;
        this.brandDomainService = brandDomainService;
        this.idGeneratorSnowflake = idGeneratorSnowflake;
        this.storeService = storeService;
        this.bankInfoService = bankInfoService;
        this.bankService = bankService;
        this.apolloConfig = apolloConfig;
        this.brandConfigDomainService = brandConfigDomainService;
        this.commonHelper = commonHelper;
        this.tripartiteSystemCallServices = tripartiteSystemCallServices;
        this.redisHelper = redisHelper;
    }

    @PostConstruct
    public void initMap() {
        TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.putAll(tripartiteSystemCallServices.stream().collect(Collectors.toMap(TripartiteSystemCallService::getFundManagementCompanyEnum, Function.identity())));
    }

    public QueryPage<SeparateAccountModule> pageSeparateAccountInfo(PageSeparateAccountQueryModule module) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(module.getBrandId());
        if (brandModule == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        Page<SeparateAccountDO> page = new Page<SeparateAccountDO>(module.getPage(), module.getPageSize()).addOrder(OrderItem.desc("ctime"));
        SeparateAccountQueryDO query = new SeparateAccountQueryDO();
        query.setBrandId(module.getBrandId());
        query.setAccountNumber(module.getAccountNumber());
        query.setAccountName(module.getAccountName());
        query.setAccountOpenStatus(module.getOpenStatus());
        query.setSettleCardStatus(module.getBankCardActivateStatus());
        query.setSelectedIds(module.getSelectedIdList());
        query.setExcludeIds(module.getExcludeIdList());
        QueryPage<SeparateAccountDO> separateAccountDOPage = separateAccountService.pageList(page, query);
        List<SeparateAccountDO> records = separateAccountDOPage.getRecords();
        Map<String, List<SeparateAccountRelatedDO>> separateAccountRelatedMap = separateAccountRelatedService.getSeparateAccountRelatedMap(
                records.stream().map(SeparateAccountDO::getAccountNumber).collect(Collectors.toList())
        );
        List<SeparateAccountSettlementCardDO> defaultCardList = separateAccountSettlementCardService.getDefaultCardList(
                records.stream().map(SeparateAccountDO::getAccountNumber).collect(Collectors.toList())
        );
        Map<String, SeparateAccountSettlementCardDO> separateAccountSettlementCardMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(defaultCardList)) {
            separateAccountSettlementCardMap.putAll(defaultCardList.stream().collect(Collectors.toMap(SeparateAccountSettlementCardDO::getAccountNumber, Function.identity(), (old, newOne) -> newOne)));
        }
        QueryPage<SeparateAccountModule> separateAccountModulePage = new QueryPage<>();
        List<SeparateAccountModule> separateAccountModules = SeparateAccountModule.fromDOList(separateAccountDOPage.getRecords(), separateAccountRelatedMap, separateAccountSettlementCardMap);
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.MY_BANK)) {
            MyBankConfigModule config = brandConfigDomainService.getConfigByBrandId(module.getBrandId(), MyBankConfigModule.class);
            separateAccountModules.forEach(separateAccountModule -> {
                if (separateAccountModule.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus())) {
                    String myBankQrCodeUrl = CommonHelper.getMyBankQrCodeUrl(apolloConfig.getMyBankActivateQrcodeUrl(), config.getAppId(), separateAccountModule.getAccountNumber(), config.getIsvOrgId());
                    separateAccountModule.setActivationUrl(myBankQrCodeUrl);
                    separateAccountModule.setActivationShortUrl(commonHelper.getShortUrl(myBankQrCodeUrl));
                }
            });
        }
        separateAccountModulePage.setTotal(separateAccountDOPage.getTotal());
        separateAccountModulePage.setRecords(separateAccountModules);
        return separateAccountModulePage;
    }

    public SeparateAccountModule getSeparateAccountInfoByAccountNumber(String brandId, String accountNumber) {
        SeparateAccountDO separateAccountDO = separateAccountService.getSeparateAccount(brandId, accountNumber);
        List<SeparateAccountRelatedDO> separateAccountRelatedDOList = separateAccountRelatedService.getSeparateAccountRelatedList(accountNumber);
        SeparateAccountSettlementCardDO defaultSettlementCardDO = separateAccountSettlementCardService.getDefaultCard(accountNumber);
        SeparateAccountModule separateAccountModule = SeparateAccountModule.fromDO(separateAccountDO, separateAccountRelatedDOList, defaultSettlementCardDO);
        if (Objects.nonNull(defaultSettlementCardDO) && Objects.nonNull(separateAccountModule) && StringUtils.isNotBlank(defaultSettlementCardDO.getBankName())) {
            BankImageDTO bankImage = bankService.getBankImage(defaultSettlementCardDO.getBankName());
            separateAccountModule.setBankIcon(bankImage.getBankIconImage());
            separateAccountModule.setBankBackPicture(bankImage.getBankBackImage());
        }

        return separateAccountModule;
    }

    @Transactional(rollbackFor = Exception.class)
    public OpenSeparateAccountResultModule openSeparateAccount(OpenSeparateAccountModule openSeparateAccountModule, String brandId, FundManagementCompanyEnum fundManagementCompanyEnum) {
        // 创建分账账户DO对象
        SeparateAccountDO separateAccount = createSeparateAccount(openSeparateAccountModule);
        // 创建关联关系
        createSeparateAccountRelated(openSeparateAccountModule, separateAccount.getAccountNumber());
        // 创建结算卡
        createSettleCard(openSeparateAccountModule, separateAccount.getAccountNumber());
        SeparateAccountSettlementCardDO settlementCardDO = separateAccountSettlementCardService.getDefaultCard(separateAccount.getAccountNumber());
        if (openSeparateAccountModule.isNewSeparateAccount()) {
            // 调用资管机构开户
            BrandMerchantCreationRecordModule recordModule = new BrandMerchantCreationRecordModule();
            recordModule.setBrandId(brandId);
            recordModule.setMerchantId(separateAccount.getAccountNumber());
            recordModule.setMerchantSn(separateAccount.getAccountNumber());
            recordModule.setRecordId(idGeneratorSnowflake.nextSerialNumber());
            switch (fundManagementCompanyEnum) {
                case PAB:
                    this.invokePabService(separateAccount, recordModule, brandId);
                    break;
                case MY_BANK:
                    this.invokeMyBankService(separateAccount, recordModule, brandId);
                    break;
                case CITIC:
                    this.invokeCiticService(separateAccount, recordModule, brandId);
                    break;
                case FUIOU:
                    this.createFuiouMember(separateAccount, settlementCardDO, recordModule, brandId);
                    break;
                default:
                    break;
            }
            separateAccountService.updateSeparateAccount(separateAccount);
            if (Objects.nonNull(settlementCardDO)) {
                separateAccountSettlementCardService.updateCard(settlementCardDO);
            }
            this.updateSameSeparateAccountAndBrandMerchant(openSeparateAccountModule, brandId, separateAccount);
        }
        if (separateAccount.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus())) {
            return OpenSeparateAccountResultModule.builder().accountNumber(separateAccount.getAccountNumber()).result("FAILURE").failReason(separateAccount.getAccountOpenFailureReason()).build();
        }
        if (separateAccount.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus())) {
            return OpenSeparateAccountResultModule.builder().accountNumber(separateAccount.getAccountNumber()).result("SUCCESS").build();
        }
        return OpenSeparateAccountResultModule.builder().accountNumber(separateAccount.getAccountNumber()).result("PROCESSING").failReason("").build();
    }

    private void updateSameSeparateAccountAndBrandMerchant(OpenSeparateAccountModule openSeparateAccountModule, String brandId, SeparateAccountDO separateAccount) {
        SeparateAccountQueryDO separateAccountQueryDO = new SeparateAccountQueryDO();
        separateAccountQueryDO.setBrandId(brandId);
        separateAccountQueryDO.setIdNumber(openSeparateAccountModule.getIdNumber());
        separateAccountQueryDO.setIdType(openSeparateAccountModule.getIdType());
        List<SeparateAccountDO> separateAccountList = separateAccountService.getSeparateAccountListByBrandIdAndIdNumber(separateAccountQueryDO);
        if (CollectionUtils.isNotEmpty(separateAccountList)){
            Map<String, List<SeparateAccountRelatedDO>> separateAccountRelatedMap = separateAccountRelatedService.getSeparateAccountRelatedMap(separateAccountList.stream().map(SeparateAccountDO::getAccountNumber).collect(Collectors.toList()));
            separateAccountList.forEach(accountDO -> {
                accountDO.setSubAccountNo(separateAccount.getSubAccountNo());
                accountDO.setMemberId(separateAccount.getMemberId());
                accountDO.setAccountOpenFailureReason(separateAccount.getAccountOpenFailureReason());
                accountDO.setAccountOpenStatus(separateAccount.getAccountOpenStatus());
                accountDO.setAccountOpenedTime(separateAccount.getAccountOpenedTime());
                accountDO.setSettleCardStatus(separateAccount.getSettleCardStatus());
                separateAccountService.updateSeparateAccount(accountDO);
                List<SeparateAccountRelatedDO> separateAccountRelatedList = separateAccountRelatedMap.get(separateAccount.getAccountNumber());
                if (CollectionUtils.isNotEmpty(separateAccountRelatedList)){
                    List<SeparateAccountRelatedDO> collect = separateAccountRelatedList.stream().filter(separateAccountRelatedDO -> separateAccountRelatedDO.getType().equals(SeparateAccountRelateTypeEnum.MERCHANT_SN.getType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)){
                        collect.forEach(separateAccountRelatedDO -> {
                            BrandMerchantModule brandMerchantByBrandIdAndMerchantSn = brandDomainService.getBrandMerchantByBrandIdAndMerchantSn(brandId, separateAccountRelatedDO.getRelatedSn());
                            if (Objects.nonNull(brandMerchantByBrandIdAndMerchantSn)) {
                                brandMerchantByBrandIdAndMerchantSn.setSubAccountNo(separateAccount.getSubAccountNo());
                                brandMerchantByBrandIdAndMerchantSn.setMemberId(separateAccount.getMemberId());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenFailureReason(separateAccount.getAccountOpenFailureReason());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenStatus(separateAccount.getAccountOpenStatus());
                                brandMerchantByBrandIdAndMerchantSn.setAccountOpenedTime(separateAccount.getAccountOpenedTime());
                                brandMerchantByBrandIdAndMerchantSn.setBankCardActivateStatus(separateAccount.getSettleCardStatus());
                                brandDomainService.updateBrandMerchant(brandMerchantByBrandIdAndMerchantSn);
                            }
                        });
                    }
                }
            });
        }
    }

    private void createSettleCard(OpenSeparateAccountModule openSeparateAccountModule, String accountNumber) {
        SeparateAccountSettlementCardDO defaultCard = separateAccountSettlementCardService.getDefaultCard(accountNumber);
        if (Objects.nonNull(openSeparateAccountModule.getSettlementCard())) {
            int defaultStatus = 1;
            if (Objects.nonNull(defaultCard) && defaultCard.getCardNumber().equals(openSeparateAccountModule.getSettlementCard().getCardNumber())) {
                return;
            }
            if (Objects.nonNull(defaultCard)) {
                defaultStatus = 0;
            }
            // 补充参数
            SeparateAccountSettlementCardDO separateAccountSettlementCardDO = new SeparateAccountSettlementCardDO();
            separateAccountSettlementCardDO.setHolder(openSeparateAccountModule.getSettlementCard().getHolder());
            separateAccountSettlementCardDO.setCardNumber(openSeparateAccountModule.getSettlementCard().getCardNumber());
            separateAccountSettlementCardDO.setOpeningNumber(openSeparateAccountModule.getSettlementCard().getOpeningNumber());
            separateAccountSettlementCardDO.setType(openSeparateAccountModule.getSettlementCard().getType());
            separateAccountSettlementCardDO.setCellphone(openSeparateAccountModule.getSettlementCard().getCellphone());
            if (separateAccountSettlementCardDO.getType() == 1) {
                separateAccountSettlementCardDO.setBankName(openSeparateAccountModule.getSettlementCard().getBankName());
                separateAccountSettlementCardDO.setBranchName(openSeparateAccountModule.getSettlementCard().getBranchBank());
            }
            if (separateAccountSettlementCardDO.getType() == 2) {
                BankInfoResponse bankInfoByOpenNumber = bankInfoService.getBankInfoByOpenNumber(separateAccountSettlementCardDO.getOpeningNumber());
                if (Objects.isNull(bankInfoByOpenNumber)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_NOT_EXIST);
                }
                separateAccountSettlementCardDO.setBankName(bankInfoByOpenNumber.getBankName());
                separateAccountSettlementCardDO.setBranchName(bankInfoByOpenNumber.getBranchName());
            }
            separateAccountSettlementCardDO.setAccountNumber(accountNumber);
            separateAccountSettlementCardDO.setDefaultStatus(defaultStatus);
            separateAccountSettlementCardService.add(separateAccountSettlementCardDO);
            // 避免加密后被覆盖
            separateAccountSettlementCardDO.setCellphone(openSeparateAccountModule.getSettlementCard().getCellphone());
        }
    }

    private SeparateAccountDO createSeparateAccount(OpenSeparateAccountModule openSeparateAccountModule) {
        String newAccountNumber = String.valueOf(idGeneratorSnowflake.nextId());
        SeparateAccountQueryDO separateAccountQueryDO = new SeparateAccountQueryDO();
        separateAccountQueryDO.setBrandId(openSeparateAccountModule.getBrandId());
        separateAccountQueryDO.setIdNumber(openSeparateAccountModule.getIdNumber());
        List<SeparateAccountDO> separateAccountListByBrandIdAndIdNumber = separateAccountService.getSeparateAccountListByBrandIdAndIdNumber(separateAccountQueryDO);
        SeparateAccountDO separateAccountDO;
        if (CollectionUtils.isNotEmpty(separateAccountListByBrandIdAndIdNumber)) {
            separateAccountDO = separateAccountListByBrandIdAndIdNumber.get(0);
            SeparateAccountSettlementCardDO defaultCard = separateAccountSettlementCardService.getDefaultCard(separateAccountDO.getAccountNumber());
            if (Objects.nonNull(defaultCard)) {
                defaultCard.setId(null);
                defaultCard.setAccountNumber(newAccountNumber);
                defaultCard.setCtime(new Date());
                defaultCard.setMtime(new Date());
                separateAccountSettlementCardService.add(defaultCard);
            }
            separateAccountDO.setId(null);
        } else {
            separateAccountDO = new SeparateAccountDO();
            openSeparateAccountModule.setNewSeparateAccount(true);
        }
        separateAccountDO.setBrandId(openSeparateAccountModule.getBrandId());
        separateAccountDO.setAccountNumber(newAccountNumber);
        separateAccountDO.setAccountName(openSeparateAccountModule.getAccountName());
        separateAccountDO.setAccountType(openSeparateAccountModule.getAccountType().getCode());
        separateAccountDO.setType(openSeparateAccountModule.getType().getType());
        separateAccountDO.setIdType(openSeparateAccountModule.getIdType());
        separateAccountDO.setIdNumber(openSeparateAccountModule.getIdNumber());
        separateAccountDO.setLegalPersonPhone(openSeparateAccountModule.getLegalPersonPhone());
        separateAccountDO.setLegalPersonName(openSeparateAccountModule.getLegalPersonName());
        separateAccountDO.setLegalPersonIdType(openSeparateAccountModule.getLegalPersonIdType());
        separateAccountDO.setLegalPersonId(openSeparateAccountModule.getLegalPersonId());
        separateAccountDO.setContractIdType(openSeparateAccountModule.getContractIdType());
        separateAccountDO.setContractId(openSeparateAccountModule.getContractId());
        separateAccountDO.setContractName(openSeparateAccountModule.getContractName());
        separateAccountDO.setContractPhone(openSeparateAccountModule.getContractPhone());
        separateAccountDO.setCtime(new Date());
        separateAccountDO.setMtime(new Date());
        separateAccountService.addSeparateAccount(separateAccountDO);
        openSeparateAccountModule.setId(separateAccountDO.getId());
        if (StringUtils.isBlank(separateAccountDO.getSubAccountNo())){
            openSeparateAccountModule.setNewSeparateAccount(true);
        }
        separateAccountDO.setIdNumber(openSeparateAccountModule.getIdNumber());
        separateAccountDO.setLegalPersonPhone(openSeparateAccountModule.getLegalPersonPhone());
        separateAccountDO.setLegalPersonId(openSeparateAccountModule.getLegalPersonId());
        separateAccountDO.setContractId(openSeparateAccountModule.getContractId());
        separateAccountDO.setContractPhone(openSeparateAccountModule.getContractPhone());
        return separateAccountDO;
    }

    private void createSeparateAccountRelated(OpenSeparateAccountModule openSeparateAccountModule, String accountNumber) {
        List<SeparateAccountRelatedDO> separateAccountRelatedDOList = Lists.newArrayList();
        if (StringUtils.isNotBlank(openSeparateAccountModule.getMerchantId())) {
            separateAccountRelatedDOList.add(
                    SeparateAccountRelatedDO.builder()
                            .brandId(openSeparateAccountModule.getBrandId())
                            .accountNumber(accountNumber)
                            .type(SeparateAccountRelateTypeEnum.MERCHANT_ID.getType())
                            .relatedSn(openSeparateAccountModule.getMerchantId())
                            .build()
            );
        }

        if (StringUtils.isNotBlank(openSeparateAccountModule.getOutMerchantNo())) {
            separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                    .brandId(openSeparateAccountModule.getBrandId())
                    .accountNumber(accountNumber)
                    .type(SeparateAccountRelateTypeEnum.OUT_MERCHANT.getType())
                    .relatedSn(openSeparateAccountModule.getOutMerchantNo())
                    .build()
            );
        }
        if (StringUtils.isNotBlank(openSeparateAccountModule.getSqbStoreId())) {
            separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                    .brandId(openSeparateAccountModule.getBrandId())
                    .accountNumber(accountNumber)
                    .type(SeparateAccountRelateTypeEnum.STORE_ID.getType())
                    .relatedSn(openSeparateAccountModule.getSqbStoreId())
                    .build()
            );
            StoreInfo store = storeService.getStoreById(openSeparateAccountModule.getSqbStoreId(), null);
            if (Objects.nonNull(store)) {
                separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                        .brandId(openSeparateAccountModule.getBrandId())
                        .accountNumber(accountNumber)
                        .type(SeparateAccountRelateTypeEnum.STORE_SN.getType())
                        .relatedSn(store.getSn())
                        .build()
                );
            }
        }
        if (StringUtils.isNotBlank(openSeparateAccountModule.getElmStoreId())) {
            separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                    .brandId(openSeparateAccountModule.getBrandId())
                    .accountNumber(accountNumber)
                    .type(SeparateAccountRelateTypeEnum.ELM_STORE.getType())
                    .relatedSn(openSeparateAccountModule.getElmStoreId())
                    .build()
            );
        }
        if (StringUtils.isNotBlank(openSeparateAccountModule.getMeiTuanStoreId())) {
            separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                    .brandId(openSeparateAccountModule.getBrandId())
                    .accountNumber(accountNumber)
                    .type(SeparateAccountRelateTypeEnum.MT_STORE.getType())
                    .relatedSn(openSeparateAccountModule.getMeiTuanStoreId())
                    .build()
            );
        }
        if (StringUtils.isNotBlank(openSeparateAccountModule.getDyStoreId())) {
            separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                    .brandId(openSeparateAccountModule.getBrandId())
                    .accountNumber(accountNumber)
                    .type(SeparateAccountRelateTypeEnum.DY_STORE.getType())
                    .relatedSn(openSeparateAccountModule.getDyStoreId())
                    .build()
            );
        }
        separateAccountRelatedService.saveBatch(separateAccountRelatedDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSeparateAccount(String brandId, String accountNumber) {
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        SeparateAccountDO separateAccount = separateAccountService.getSeparateAccount(brandId, accountNumber);
        if (separateAccount == null) {
            return false;
        }
        if (brandModule.getFundManagementCompanyCode().equals(FundManagementCompanyEnum.FUIOU) && StringUtils.isNotBlank(separateAccount.getSubAccountNo())) {
            this.handleFuiouCase(brandId, separateAccount);
        }
        separateAccount.setDeleted(1);
        separateAccountService.updateSeparateAccount(separateAccount);
        List<SeparateAccountRelatedDO> separateAccountRelatedList = separateAccountRelatedService.getSeparateAccountRelatedList(accountNumber);
        if (CollectionUtils.isNotEmpty(separateAccountRelatedList)) {
            separateAccountRelatedList.forEach(separateAccountRelated -> separateAccountRelated.setDeleted(1));
            separateAccountRelatedService.updateBatchById(separateAccountRelatedList);
        }
        List<SeparateAccountSettlementCardDO> cardList = separateAccountSettlementCardService.getCardList(accountNumber);
        if (CollectionUtils.isNotEmpty(cardList)) {
            cardList.forEach(card -> card.setDeleted(1));
            separateAccountSettlementCardService.updateBatchById(cardList);
        }
        return true;
    }

    private void handleFuiouCase(String brandId, SeparateAccountDO separateAccountDO) {
        FuiouConfigModule configModule = brandConfigDomainService.getConfigByBrandId(brandId, FuiouConfigModule.class);
        if (Objects.isNull(configModule)) {
            return;
        }
        if (separateAccountDO.getAccountOpenStatus().equals(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus())) {
            CloseAccountRequest closeAccountRequest = new CloseAccountRequest();
            CloseAccountRequestBody body = new CloseAccountRequestBody(configModule.getMerchantNo());
            body.setAccountIn(separateAccountDO.getSubAccountNo());
            body.setTraceNo(String.valueOf(System.currentTimeMillis()));
            closeAccountRequest.setBody(body);
            CloseAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(closeAccountRequest, CloseAccountResponse.class, configModule);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.warn("【商户: {}】删除失败，会员销户接口调用失败，失败原因：{}.", separateAccountDO.getSubAccountNo(), response.getResultMsg());
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CLOSE_ACCOUNT_FAIL, "分账账户：" + separateAccountDO.getAccountNumber() + " 会员销户接口调用失败，请联系客服");
            }
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.CLOSED_ACCOUNT.getStatus());
            separateAccountDO.setExt(response.getBody().getImg());
        } else {
            InvalidAllocateAccountRequest request = new InvalidAllocateAccountRequest();
            InvalidAllocateAccountRequestBody body = new InvalidAllocateAccountRequestBody(configModule.getMerchantNo());
            body.setAccountIn(separateAccountDO.getSubAccountNo());
            body.setTraceNo(String.valueOf(System.currentTimeMillis()));
            request.setBody(body);
            InvalidAllocateAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, InvalidAllocateAccountResponse.class, configModule);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.warn("【商户: {}】删除失败，会员删除接口调用失败，失败原因：{}.", separateAccountDO.getSubAccountNo(), response.getResultMsg());
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CLOSE_ACCOUNT_FAIL, "分账账户：" + separateAccountDO.getAccountNumber() + " 会员删除接口调用失败，请联系客服。");
            }
        }
    }

    private void invokePabService(SeparateAccountDO separateAccountDO, BrandMerchantCreationRecordModule recordModule, String brandId) {
        PabConfigModule pabConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, PabConfigModule.class);
        try {
            CreateMemberRequest createMemberRequest = VfinanceInterfaceService.getCreateMemberRequest(separateAccountDO, pabConfigModule);
            CreateMemberResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.PAB).call(createMemberRequest, CreateMemberResponse.class, pabConfigModule);
            // 调用失败
            if (Objects.isNull(response) || VFinanceResponseCodeEnum.FAIL.getCode().equals(response.getSuccess())) {
                log.warn("调用维金接口失败！返回值为：{}", JSON.toJSONString(response));
                recordModule.setResult(response.getErrorMessage());
                recordModule.setStatus(0);
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                separateAccountDO.setAccountOpenFailureReason(response.getErrorMessage());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
            //调用成功回写会员memberId和子账号
            separateAccountDO.setMemberId(response.getMemberId());
            separateAccountDO.setSubAccountNo(response.getMerchantAccountId());
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
            recordModule.setStatus(1);
            separateAccountDO.setAccountOpenedTime(new Date());
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
        } catch (Exception e) {
            log.error("调用维金系统异常。", e);
            recordModule.setResult("调用维金系统异常");
            recordModule.setStatus(0);
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason("调用PAB系统异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
        }
    }

    private void invokeMyBankService(SeparateAccountDO separateAccountDO, BrandMerchantCreationRecordModule recordModule, String brandId) {
        MyBankConfigModule myBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, MyBankConfigModule.class);
        if (Objects.isNull(myBankConfigModule) || StringUtils.isBlank(myBankConfigModule.getAppId()) || StringUtils.isBlank(myBankConfigModule.getIsvOrgId()) || StringUtils.isBlank(myBankConfigModule.getIsvPrivateKey()) || StringUtils.isBlank(myBankConfigModule.getIsvPublicKey())) {
            recordModule.setResult("配置信息异常");
            recordModule.setStatus(0);
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason("网商配置信息异常");
            return;
        }
        MerchantAppletPreRegisterRequest request = MerchantAppletPreRegisterRequest.buildRequest(myBankConfigModule, separateAccountDO, recordModule, apolloConfig.getMybankHandlerSwitch());
        try {
            MerchantAppletPreRegisterResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.MY_BANK).call(request, MerchantAppletPreRegisterResponse.class, myBankConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用网商银行接口未响应");
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用网商银行接口失败," + response.getResultMsg());
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                separateAccountDO.setAccountOpenFailureReason(response.getResultMsg());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用网商银行接口异常," + e.getMessage());
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason("调用网商银行接口异常，调用时间：" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
            return;
        }
        recordModule.setStatus(3);
        recordModule.setResult("预入驻请求成功，等待网商回调！");
        separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.UNDER_REVIEW.getStatus());
        brandDomainService.createBrandMerchantCreationRecord(recordModule);
    }

    private void invokeCiticService(SeparateAccountDO separateAccountDO, BrandMerchantCreationRecordModule recordModule, String brandId) {
        CiticBankConfigModule citicBankConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, CiticBankConfigModule.class);
        try {
            if (StringUtils.isBlank(citicBankConfigModule.getMerchantId()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKey()) || StringUtils.isBlank(citicBankConfigModule.getPrivateKeyPassword()) || StringUtils.isBlank(citicBankConfigModule.getPublicKey())) {
                recordModule.setResult("中信配置信息异常");
                recordModule.setStatus(0);
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                separateAccountDO.setAccountOpenFailureReason("网商配置信息异常");
                return;
            }
            RegisteredUserRequest request = RegisteredUserRequest.build(citicBankConfigModule, separateAccountDO, apolloConfig.getCiticUserTypeMap().getString(separateAccountDO.getAccountType()));
            RegisteredResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.CITIC).call(request, RegisteredResponse.class, citicBankConfigModule);
            // 调用失败
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用中信银行接口未响应");
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
            if (response.getResultCode().equals(TripartiteSystemCallResponse.ResultCodeEnum.FAIL)) {
                recordModule.setResult(response.getResultMsg());
                recordModule.setStatus(0);
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                separateAccountDO.setAccountOpenFailureReason(response.getResultMsg());
                brandDomainService.createBrandMerchantCreationRecord(recordModule);
                return;
            }
            //调用成功回写会员memberId和子账号
            separateAccountDO.setMemberId(response.getUserId());
            separateAccountDO.setSubAccountNo(response.getUserId());
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
            separateAccountDO.setAccountOpenedTime(new Date());
            recordModule.setStatus(1);
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
        } catch (Exception e) {
            log.error("中信系统调用异常。", e);
            recordModule.setResult("调用中信系统异常");
            recordModule.setStatus(0);
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason("调用CICIT系统异常：" + e.getMessage());
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
        }
    }

    private void createFuiouMember(SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO defaultSettlementCardDO, BrandMerchantCreationRecordModule recordModule, String brandId) {
        FuiouConfigModule fuiouConfigModule = brandConfigDomainService.getConfigByBrandId(brandId, FuiouConfigModule.class);
        if (StringUtils.isBlank(fuiouConfigModule.getMerchantNo()) || StringUtils.isBlank(fuiouConfigModule.getPrivateKey()) || StringUtils.isBlank(fuiouConfigModule.getPublicKey())) {
            recordModule.setResult("富友配置信息异常");
            recordModule.setStatus(0);
            brandDomainService.createBrandMerchantCreationRecord(recordModule);
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason("富友配置信息异常");
            return;
        }
        fuiouConfigModule.setCheckType(fuiouCheckType);
        recordModule.setRecordId(separateAccountDO.getAccountNumber());
        // 创建富友会员
        OpenAccountRequest request = new OpenAccountRequest();
        try {
            request.setBody(OpenAccountRequest.buildBody(separateAccountDO, defaultSettlementCardDO, fuiouConfigModule, recordModule));
            OpenAccountResponse response = TRIPARTITE_SYSTEM_CALL_SERVICE_MAP.get(FundManagementCompanyEnum.FUIOU).call(request, OpenAccountResponse.class, fuiouConfigModule);
            if (response == null) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口未响应");
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                return;
            }
            if (TripartiteSystemCallResponse.ResultCodeEnum.FAIL.equals(response.getResultCode()) || TripartiteSystemCallResponse.ResultCodeEnum.UNKNOWN.equals(response.getResultCode())) {
                recordModule.setStatus(0);
                recordModule.setResult("调用富友接口失败," + response.getResultMsg());
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
                separateAccountDO.setAccountOpenFailureReason(response.getResultMsg());
                return;
            }
            separateAccountDO.setMemberId(response.getBody().getAccountIn());
            separateAccountDO.setSubAccountNo(response.getBody().getAccountIn());
            separateAccountDO.setAccountOpenFailureReason(response.getBody().getCheckUrl());
        } catch (Exception e) {
            recordModule.setStatus(0);
            recordModule.setResult("调用富友接口异常," + e.getMessage());
            separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPEN_FAILURE.getStatus());
            separateAccountDO.setAccountOpenFailureReason(e.getMessage());
            return;
        }
        recordModule.setStatus(3);
        recordModule.setResult("预入驻请求成功，等待客户激活后，富友回调！");
        separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_ACTIVATED.getStatus());
        separateAccountDO.setSettleCardStatus(BankCardActivateStatusEnum.UNACTIVATED.getActivateStatus());
        redisHelper.saveAllToSet(RedisKeyEnum.FUIOU_OPEN_ACCOUNT_RECORDS.getKey(), Lists.newArrayList(
                        FuiouToBeActiveRecordModule.builder()
                                .brandId(brandId)
                                .merchantId(separateAccountDO.getAccountNumber())
                                .tradeNo(recordModule.getRecordId())
                                .build()
                )
        );
        brandDomainService.createBrandMerchantCreationRecord(recordModule);
    }
}
