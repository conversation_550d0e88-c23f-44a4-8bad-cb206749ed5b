package com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen;

import com.alibaba.fastjson2.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.enums.AppInfoStatusEnum;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppInfoOpenResult;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.AppOpenResultNoticeRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantbusinessopen.model.BusinessOpenResult;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.merchant.business.bean.BusinessOpenSubmitResult;
import com.wosai.sales.merchant.business.bean.MerchantAppOpenResult;
import com.wosai.sales.merchant.business.bean.open.MerchantOpenRequest;
import com.wosai.sales.merchant.business.bean.open.SaveDraftAndOpenRequest;
import com.wosai.sales.merchant.business.model.SyncAppOpenResultModel;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.TimeTaskService;
import com.wosai.sales.merchant.business.service.open.OpenBusinessOpenService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Slf4j
@Component
public class MerchantBusinessOpenClient {

    @Autowired
    private OpenBusinessOpenService openBusinessOpenService;
    @Autowired
    private CommonAppInfoService commonAppInfoService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private TimeTaskService timeTaskService;

    /**
     * 开通应用
     *
     * @param request 请求参数
     * @return 开通结果
     */
    public BusinessOpenResult openApp(BusinessOpenRequest request) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(request.getMerchantId(), null);
        if (Objects.nonNull(merchantInfo)) {
            Map<String, Object> firstStore = storeService.getFirstStore(CollectionUtil.hashMap(Store.MERCHANT_ID, request.getMerchantId()));
            if (WosaiMapUtils.isEmpty(firstStore)) {
                return new BusinessOpenResult().setSuccess(false).setMessage("门店信息不存在");
            }
            return existOpenApp(request, firstStore);
        } else {
            return newOpenApp(request);
        }
    }

    private BusinessOpenResult existOpenApp(BusinessOpenRequest request, Map firstStore) {
        MerchantOpenRequest merchantOpenRequest = new MerchantOpenRequest();
        merchantOpenRequest.setMerchantId(request.getMerchantId());
        merchantOpenRequest.setStoreId(WosaiMapUtils.getString(firstStore, DaoConstants.ID));
        merchantOpenRequest.setUserId(request.getUserId());
        MerchantOpenRequest.AppInfoRequest appInfoRequest = new MerchantOpenRequest.AppInfoRequest();
        appInfoRequest.setAppId(request.getAppId());
        appInfoRequest.setAppInfo(request.getAppInfo());
        merchantOpenRequest.setAppInfoList(Collections.singletonList(appInfoRequest));
        try {
            BusinessOpenSubmitResult openResult = openBusinessOpenService.merchantOpen(merchantOpenRequest);
            if (openResult.isResult()) {
                return new BusinessOpenResult()
                        .setSuccess(openResult.isResult())
                        .setMessage(openResult.getMessage())
                        .setMerchantId(request.getMerchantId())
                        .setMerchantSn(merchantService.getMerchantById(request.getMerchantId(), null).getSn());
            } else {
                return new BusinessOpenResult()
                        .setSuccess(openResult.isResult())
                        .setMessage(openResult.getMessage());
            }
        } catch (Exception e) {
            String merchantName = WosaiMapUtils.getString(request.getMerchantInfo(), Merchant.NAME);
            log.error("开通失败 {} {}", merchantName, e.getMessage());
            return new BusinessOpenResult()
                    .setSuccess(false)
                    .setMessage(e.getMessage());
        }
    }

    private BusinessOpenResult newOpenApp(BusinessOpenRequest request) {
        SaveDraftAndOpenRequest.MerchantInfoRequest merchantInfoRequest = new SaveDraftAndOpenRequest.MerchantInfoRequest();
        merchantInfoRequest.setId(request.getMerchantId());
        merchantInfoRequest.setMerchant(request.getMerchantInfo());
        merchantInfoRequest.setAccount(request.getAccountInfo());
        merchantInfoRequest.setLicense(request.getLicenseInfo());
        SaveDraftAndOpenRequest.StoreInfoRequest storeInfoRequest = new SaveDraftAndOpenRequest.StoreInfoRequest();
        storeInfoRequest.setStoreId(request.getStoreId());
        storeInfoRequest.setStoreInfo(request.getStoreInfo());
        SaveDraftAndOpenRequest.AppInfoRequest appInfoRequest = new SaveDraftAndOpenRequest.AppInfoRequest();
        appInfoRequest.setAppInfo(request.getAppInfo());

        SaveDraftAndOpenRequest saveDraftAndOpenRequest = new SaveDraftAndOpenRequest();
        saveDraftAndOpenRequest.setMerchantInfoRequest(merchantInfoRequest);
        saveDraftAndOpenRequest.setStoreInfoRequest(storeInfoRequest);
        saveDraftAndOpenRequest.setAppInfoRequest(appInfoRequest);
        saveDraftAndOpenRequest.setAppIds(Collections.singletonList(request.getAppId()));
        saveDraftAndOpenRequest.setUserId(request.getUserId());
        try {
            BusinessOpenSubmitResult openResult = openBusinessOpenService.saveDraftAndOpen(saveDraftAndOpenRequest);
            if (openResult.isResult()) {
                return new BusinessOpenResult()
                        .setSuccess(openResult.isResult())
                        .setMessage(openResult.getMessage())
                        .setMerchantId(request.getMerchantId())
                        .setMerchantSn(merchantService.getMerchantById(request.getMerchantId(), null).getSn());
            } else {
                return new BusinessOpenResult()
                        .setSuccess(openResult.isResult())
                        .setMessage(openResult.getMessage());
            }
        } catch (Exception e) {
            String merchantName = WosaiMapUtils.getString(request.getMerchantInfo(), Merchant.NAME);
            log.error("开通失败 {} {}", merchantName, e.getMessage());
            return new BusinessOpenResult()
                    .setSuccess(false)
                    .setMessage(e.getMessage());
        }
    }

    public List<AppInfoOpenResult> queryAppInfoOpenResultByMerchantId(String merchantId) {
        Map<String, List<MerchantAppOpenResult>> merchantAppOpen = commonAppInfoService.getMerchantAppOpen(Collections.singletonList(merchantId));
        List<MerchantAppOpenResult> merchantAppOpenResults = merchantAppOpen.get(merchantId);
        if (WosaiCollectionUtils.isEmpty(merchantAppOpenResults)) {
            return new ArrayList<>();
        }
        return merchantAppOpenResults.stream().map(r ->
                        new AppInfoOpenResult()
                                .setAppName(r.getAppName())
                                .setAppId(r.getAppId())
                                .setStatus(AppInfoStatusEnum.getByCode(BeanUtil.getPropInt(r, "status"))))
                .collect(Collectors.toList());
    }

    public void noticeAppOpenResult(AppOpenResultNoticeRequest request) {
        try {
            if (WosaiStringUtils.isEmpty(request.getStoreId())) {
                request.setStoreId(WosaiMapUtils.getString(storeService.getFirstStore(CollectionUtil.hashMap(Store.MERCHANT_ID, request.getMerchantId())), DaoConstants.ID));
            }
            Map syncAppReq = CollectionUtil.hashMap(SyncAppOpenResultModel.MERCHANT_ID, request.getMerchantId(), SyncAppOpenResultModel.STORE_ID, request.getStatus(), SyncAppOpenResultModel.DEV_CODE, request.getDevCode());
            syncAppReq.put(SyncAppOpenResultModel.DEV_PARAM, CollectionUtil.hashMap(
                    SyncAppOpenResultModel.STATUS, request.getStatus().getCode(),
                    SyncAppOpenResultModel.APP_SUB_STATUS_TEXT, request.getStatus().equals(AppInfoStatusEnum.SUCCESS) ? "开通成功" : request.getAppSubStatusText(),
                    SyncAppOpenResultModel.FAIL_CODE, request.getFailCode(),
                    SyncAppOpenResultModel.FAIL_MSG, request.getFailMsg())
            );
            timeTaskService.noticeAppOpenResult(syncAppReq);
        } catch (Exception e) {
            log.error("调用业务开通接口通知结果异常: {}", JSON.toJSONString(request), e);
        }

    }
}
