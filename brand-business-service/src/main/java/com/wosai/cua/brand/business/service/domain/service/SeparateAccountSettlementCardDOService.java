package com.wosai.cua.brand.business.service.domain.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountCardQueryDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【separate_account_settlement_card(分账账号结算卡信息表)】的数据库操作Service
* @createDate 2025-06-05 10:18:07
*/
public interface SeparateAccountSettlementCardDOService extends IService<SeparateAccountSettlementCardDO> {

    /**
     * 添加分账账号结算卡信息
     * @param separateAccountSettlementCardDO 结算卡信息
     * @return 添加结果
     */
    boolean add(SeparateAccountSettlementCardDO separateAccountSettlementCardDO);

    /**
     * 分页查询
     * @param page 分页对象
     * @param query 查询条件
     * @return 分页结果
     */
    Page<SeparateAccountSettlementCardDO> pageList(Page<SeparateAccountSettlementCardDO> page, SeparateAccountCardQueryDO query);

    /**
     * 根据账户编号查询结算卡信息
     * @param accountNumber 账户编号
     * @return 结算卡信息
     */
    List<SeparateAccountSettlementCardDO> getCardList(String accountNumber);

    /**
     * 获取默认结算卡信息
     * @param accountNumber 账户编号
     * @return 结算卡信息
     */
    SeparateAccountSettlementCardDO getDefaultCard(String accountNumber);

    /**
     * 批量获取默认结算卡信息
     * @param accountNumber 账户编号
     * @return 结算卡信息
     */
    List<SeparateAccountSettlementCardDO> getDefaultCardList(List<String> accountNumber);

    /**
     * 根据银行卡号获取结算卡信息
     * @param separateAccountCardQueryDO 查询条件
     * @return 结算卡信息
     */
    SeparateAccountSettlementCardDO getCardByAccountNumberAndCardNumber(SeparateAccountCardQueryDO separateAccountCardQueryDO);
    /**
     * 根据银行卡号获取结算卡信息
     * @param separateAccountCardQueryDO 查询条件
     * @return 结算卡信息
     */
    List<SeparateAccountSettlementCardDO> getCardByCardNumber(SeparateAccountCardQueryDO separateAccountCardQueryDO);

    /**
     * 修改结算卡信息
     * @param separateAccountSettlementCardDO 结算卡信息
     * @return 修改结果
     */
    boolean updateCard(SeparateAccountSettlementCardDO separateAccountSettlementCardDO);
}
