package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.OssFileRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.PageRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ExportBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.BankCardDetailDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantBankCardResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.ExportMerchantBankAccountResponse;
import com.wosai.cua.brand.business.api.dto.response.ImportMerchantTaskResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.BrandFileFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandFileBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class BrandFileFacadeImpl implements BrandFileFacade {

    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";
    private static final String CSV = "csv";

    private final BrandFileBusiness brandFileBusiness;

    private final BrandBusiness brandBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    public BrandFileFacadeImpl(BrandFileBusiness brandFileBusiness, BrandBusiness brandBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness) {
        this.brandFileBusiness = brandFileBusiness;
        this.brandBusiness = brandBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
    }

    @Override
    public Boolean importBrandMerchant(OssFileRequestDTO ossFileRequest) {
        String[] split = ossFileRequest.getOssKey().split("\\.");
        if (!XLS.equals(split[split.length - 1]) && !XLSX.equals(split[split.length - 1])) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FILE);
        }
        // 导入分账类型的商户
        if (BrandMerchantDockingModeEnum.SEPARATE_ACCOUNT.equals(ossFileRequest.getDockingMode()) && Objects.isNull(ossFileRequest.getStrategyId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.STRATEGY_ID_IS_NULL);
        }
        if (BrandMerchantDockingModeEnum.COLLECTION.equals(ossFileRequest.getDockingMode())) {
            // 导入归集的商户
            if (Objects.isNull(ossFileRequest.getAggregationModel())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.AGGREGATION_MODEL_IS_NULL);
            }
            if (Objects.isNull(ossFileRequest.getConcentrateScale())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CONCENTRATE_SCALE_IS_NULL);
            }
            if (Objects.isNull(ossFileRequest.getUseOfFunds())) {
                throw new BrandBusinessException(BrandBusinessExceptionEnum.USE_OF_FUNDS_IS_NULL);
            }
        }
        ThreadPoolHelper.execute(() -> brandFileBusiness.importBrandMerchantsByExcel(ossFileRequest));
        return true;
    }

    @Override
    public ImportMerchantTaskResponseDTO getImportTaskList(PageRequestDTO pageRequest) {
        pageRequest.setTaskType(0);
        pageRequest.setPlatform("SPA");
        return brandFileBusiness.getImportTaskList(pageRequest);
    }

    @Override
    public ImportMerchantTaskResponseDTO getTaskList(PageRequestDTO pageRequest) {
        return brandFileBusiness.getImportTaskList(pageRequest);
    }

    @Override
    public Boolean importBrandMerchantWithdrawStrategy(OssFileRequestDTO ossFileRequest) {
        String[] split = ossFileRequest.getOssKey().split("\\.");
        if (!CSV.equals(split[split.length - 1])) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FILE);
        }
        return brandFileBusiness.importBrandMerchantWithdrawStrategy(ossFileRequest);
    }

    @Override
    public Boolean importBrandMerchantMetTuanIdAndElmId(OssFileRequestDTO ossFileRequest) {
        String[] split = ossFileRequest.getOssKey().split("\\.");
        if (!XLS.equals(split[split.length - 1]) && !XLSX.equals(split[split.length - 1])) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FILE);
        }
        return brandFileBusiness.importBrandMerchantMetTuanIdAndElmId(ossFileRequest);
    }

    @Override
    public Boolean exportBrandMerchant(ExportBrandMerchantRequestDTO exportBrandMerchantRequest) {
        List<BrandMerchantDTO> exportBrandMerchants = brandFileBusiness.getExportBrandMerchants(exportBrandMerchantRequest);
        BrandDetailInfoDTO detailInfoDTO = brandBusiness.getBrandDetailInfoByBrandId(exportBrandMerchantRequest.getBrandId(), false);
        String fileName = "【" + detailInfoDTO.getName() + "】商户列表导出_" + System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(exportBrandMerchants)) {
            exportBrandMerchants.forEach(brandMerchantDTO -> brandMerchantDTO.setMeiTuanStoreStatus(brandBusiness.getMeiTuanStoreStatus(exportBrandMerchantRequest.getBrandId(), brandMerchantDTO.getAssociatedMeituanStoreSn())));
        }
        brandFileBusiness.exportBrandMerchantExcel(fileName, exportBrandMerchantRequest.getBrandId(), "SPA", exportBrandMerchants);
        return true;
    }

    @Override
    public ExportMerchantBankAccountResponse exportBrandMerchantBankAccount(ExportBrandMerchantRequestDTO exportBrandMerchantRequest) {
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(exportBrandMerchantRequest.getBrandId(), false);
        if (Objects.isNull(brandDetailInfo)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        ThreadPoolHelper.execute(() -> {
            List<BankCardDetailDTO> unActivatedBankCardList = brandMerchantBankAccountBusiness.getUnActivatedBankCardList(brandDetailInfo.getBrandId(), null);
            if (CollectionUtils.isEmpty(unActivatedBankCardList)) {
                log.warn(BrandBusinessExceptionEnum.NOT_FIND_UNACTIVATED_BANK_CARD.getMessage());
            }
            unActivatedBankCardList.forEach(bankCardDetailDTO -> bankCardDetailDTO.setActivateFailReason(StringUtils.isBlank(bankCardDetailDTO.getActivateFailReason()) ? "未激活" : bankCardDetailDTO.getActivateFailReason()));
            brandFileBusiness.exportBrandMerchantBankAccount(brandDetailInfo.getBrandId(), brandDetailInfo.getSn(), unActivatedBankCardList);
        });
        return new ExportMerchantBankAccountResponse("", "SUCCESS", "导出成功！");
    }

    @Override
    public Boolean exportNotHaveBankAccountBrandMerchant(ExportBrandMerchantRequestDTO exportBrandMerchantRequest) {
        BrandDetailInfoDTO brandDetailInfo = brandBusiness.getBrandDetailInfoByBrandId(exportBrandMerchantRequest.getBrandId(), false);
        if (Objects.isNull(brandDetailInfo)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        QueryBrandMerchantInfoDTO queryBrandMerchantInfo = new QueryBrandMerchantInfoDTO()
                .setBrandId(brandDetailInfo.getBrandId())
                .setDeleted(0);
        List<BrandMerchantModule> brandMerchantByConditions = brandBusiness.getBrandMerchantByConditions(queryBrandMerchantInfo);
        if (!CollectionUtils.isEmpty(brandMerchantByConditions)) {
            brandMerchantByConditions.removeIf(brandMerchantModule -> {
                BrandMerchantBankCardResponseDTO defaultBankCard = brandMerchantBankAccountBusiness.getDefaultBankCard(brandMerchantModule.getBrandId(), brandMerchantModule.getMerchantId());
                return Objects.nonNull(defaultBankCard);
            });
        }
        String fileName = "【" + brandDetailInfo.getName() + "】未绑卡商户导出_" + System.currentTimeMillis();
        brandFileBusiness.exportNotBindCardMerchantExcel(fileName, exportBrandMerchantRequest.getBrandId(), brandMerchantByConditions);
        return true;
    }

    @Override
    public Boolean batchSpecialTreatmentBrandMerchantOpenAccount(OssFileRequestDTO ossFileRequest) {
        String[] split = ossFileRequest.getOssKey().split("\\.");
        if (!XLS.equals(split[split.length - 1]) && !XLSX.equals(split[split.length - 1])) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FILE);
        }
        return brandFileBusiness.batchSpecialTreatmentBrandMerchantOpenAccount(ossFileRequest);
    }

    @Override
    public Boolean completeBrandMerchantBankCards(OssFileRequestDTO ossFileRequest) {
        String[] split = ossFileRequest.getOssKey().split("\\.");
        if (!XLS.equals(split[split.length - 1]) && !XLSX.equals(split[split.length - 1])) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_SUPPORT_FILE);
        }
        return brandFileBusiness.completeBrandMerchantBankCards(ossFileRequest);
    }
}
