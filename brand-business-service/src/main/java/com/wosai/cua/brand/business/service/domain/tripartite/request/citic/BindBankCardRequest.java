package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class BindBankCardRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {
    /**
     * 绑定
     */
    public static final String OP_TYPE_BIND = "1";
    /**
     * 解绑
     */
    public static final String OP_TYPE_UNBIND = "2";
    /**
     * 账户类型:中信个人账户
     */
    public static final String ACCT_TYPE_CITIC_PERSONAL = "1";
    /**
     * 账户类型:中信企业账户
     */
    public static final String ACCT_TYPE_CITIC_ENTERPRISE = "2";
    /**
     * 账户类型:他行个人账户
     */
    public static final String ACCT_TYPE_OTHER_PERSONAL = "3";
    /**
     * 账户类型:他行企业账户
     */
    public static final String ACCT_TYPE_OTHER_ENTERPRISE = "4";

    /**
     * 用户编号
     * 是否必填：是
     * 银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    @XmlElement(name = "USER_ID")
    private String userId;

    /**
     * 绑卡类型
     * 是否必填：是
     * 1-绑定 2-解绑
     */
    @XmlElement(name = "OP_TYPE")
    private String opType;

    /**
     * 开户银行联行号
     * 是否必填：否
     * 个人用户跨行提现通道为银联代付”开关打开时，个人用户绑卡时 “开户银行联行号”非必输，对公用户绑卡时“开户银行联行号”仍为必输项。“个人用户跨行提现通道为银联代付”开关关闭时，与现有规则保持一致：个人用户和对公用户绑卡时 “开户银行联行号”均为必输项。详见该接口注意事项。
     */
    @XmlElement(name = "PAN_NUM")
    private String panNum;

    /**
     * 账户名称
     * 是否必填：是
     */
    @XmlElement(name = "ACCT_NM")
    private String acctNm;

    /**
     * 银行账号
     * 是否必填：是
     * 绑定银行卡的卡号
     */
    @XmlElement(name = "PAN")
    private String pan;

    /**
     * 用户证件类型
     * 是否必填：是
     * 个人用户： 01-个人身份证 22-户口簿 23-外国护照 25-军人军官证 26-军人士兵证 27-武警军官证 28-港澳居民往来内地通行证（香港） 29-台湾居民往来大陆通行证 30-临时居民身份证 31-外国人永久居留证 32-中国护照 33-武警士兵证 34-港澳居民往来内地通行证（澳门） 35-边民出入境通行证 36-台湾居民旅行证
     * 企业用户： 02-组织机构代码 03-统一社会信用代码 04-民办非企业登记证书 05-社会团体法人登记证书 06-事业单位法人登记证 07-营业执照号码
     */
    @XmlElement(name = "USER_ID_TYPE")
    private String userIdType;

    /**
     * 用户证件号码
     * 是否必填：是
     */
    @XmlElement(name = "BANK_CARD_NO")
    private String bankCardNo;

    /**
     * 账户类型
     * 是否必填：是
     * 若用户类型是个体工商户，需上送以下账户类型且必填 1-中信个人账户 2-中信企业账户 3-他行个人账户 4-他行企业账户 个人用户绑定存折时，账户类型需上送以下账户类型，且必填。 账户类型为存折时，证件类型只支持身份证。 如不上送正确存折类型，可能会导致绑卡或提现失败。 存折提现只能通过智能提现接口，提现通道只支持人行二代。 5-中信个人存折（必填） 6-他行个人存折（必填）
     */
    @XmlElement(name = "ACCT_TYPE")
    private String acctType;

    /**
     * 银行预留手机号
     * 是否必填：否
     * 绑定且用户类型为个人时必填，解绑时非必填，可以不用和注册时使用的手机号一致，银联鉴权以绑卡接口上送的手机号为准
     */
    @XmlElement(name = "BANK_PHONE")
    private String bankPhone;

    /**
     * 用户授权协议版本号
     * 是否必填：是
     * 与个人用户签约的电子协议版本号，通过该版本号能够确定协议的具体内容 该字段在绑定个人账户时必填。【用户授权协议版本号和用户授权协议流水号，管家透传给银联，管家不做验证，商户自己定义即可】
     */
    @XmlElement(name = "AUTH_PROTOCOL_VERSION")
    private String authProtocolVersion;

    /**
     * 用户授权协议流水号
     * 是否必填：是
     * 与个人用户签约的授权交易流水号，通过该流水号应能确定电子协议版本号、签约人、签约时间 该字段在绑定个人账户时必填。【用户授权协议版本号和用户授权协议流水号，管家透传给银联，管家不做验证，商户自己定义即可】
     */
    @XmlElement(name = "AUTH_PROTOCOL_NO")
    private String authProtocolNo;

    /**
     * 交易标识
     * 是否必填：否
     * 验证申请接口或打款验证接口生成的交易标识,开通短信验证或随机打款验证功能时必填
     */
    @XmlElement(name = "TRANS_ID")
    private String transId;

    /**
     * 短信验证码
     * 是否必填：否
     * 用户手机收到的验证码,开通短信验证功能时个人用户必填
     */
    @XmlElement(name = "VERI_CD")
    private String verCd;

    /**
     * 随机打款金额
     * 是否必填：否
     * 待绑定卡收到的随机打款金额（单位：元）,开通随机打款验证功能时对公用户必填
     */
    @XmlElement(name = "VERI_AMT")
    private String verAmt;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_CREATE_BANK_CARD;
    }

    public BindBankCardRequest() {
        super();
        super.setTransCode(BIND_BANK_CARD_REQUEST_TRANS_CODE);
    }

    public static BindBankCardRequest buildUnbindRequest(CiticBankConfigModule configModule, BrandMerchantModule brandMerchantModule, BizBankAccountRes bankAccount) {
        BindBankCardRequest bindBankCardRequest = new BindBankCardRequest();
        bindBankCardRequest.setUserId(brandMerchantModule.getMemberId());
        bindBankCardRequest.setOpType(BindBankCardRequest.OP_TYPE_UNBIND);
        bindBankCardRequest.setPan(bankAccount.getNumber());
        bindBankCardRequest.setAcctNm(bankAccount.getHolder());
        bindBankCardRequest.setMerchantId(configModule.getMerchantId());
        bindBankCardRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + brandMerchantModule.getMerchantSn().substring(brandMerchantModule.getMerchantSn().length() - 8));
        return bindBankCardRequest;
    }

    public static BindBankCardRequest buildUnbindRequest(CiticBankConfigModule configModule, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO) {
        BindBankCardRequest bindBankCardRequest = new BindBankCardRequest();
        bindBankCardRequest.setUserId(separateAccountDO.getSubAccountNo());
        bindBankCardRequest.setOpType(BindBankCardRequest.OP_TYPE_UNBIND);
        bindBankCardRequest.setPan(settlementCardDO.getCardNumber());
        bindBankCardRequest.setAcctNm(settlementCardDO.getHolder());
        bindBankCardRequest.setMerchantId(configModule.getMerchantId());
        bindBankCardRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + separateAccountDO.getAccountNumber().substring(separateAccountDO.getAccountNumber().length() - 8));
        return bindBankCardRequest;
    }

    public static BindBankCardRequest buildBindRequest(CiticBankConfigModule configModule, BrandMerchantModule brandMerchantModule, MerchantInfo merchant, BankCardModule bankCardModule, BizBankAccountRes bankAccount, MerchantBusinessLicenseInfo license) {
        BindBankCardRequest bindBankCardRequest = new BindBankCardRequest();
        bindBankCardRequest.setUserId(brandMerchantModule.getMemberId());
        bindBankCardRequest.setOpType(BindBankCardRequest.OP_TYPE_BIND);
        bindBankCardRequest.setPanNum(bankAccount.getOpeningNumber());
        bindBankCardRequest.setPan(bankAccount.getNumber());
        bindBankCardRequest.setAcctNm(bankAccount.getHolder());
        bindBankCardRequest.setBankPhone(bankCardModule.getReservedMobileNumber());
        bindBankCardRequest.setMerchantId(configModule.getMerchantId());
        bindBankCardRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + merchant.getSn().substring(merchant.getSn().length() - 8));
        switch (license.getType()) {
            case 0:
                personParamsBuild(brandMerchantModule, bankAccount, license, bindBankCardRequest);
                break;
            case 1:
                individualBusinessOperator(brandMerchantModule, license, bindBankCardRequest, bankAccount);
                break;
            case 2:
                companyParamsBuild(bankAccount, license, bindBankCardRequest);
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_CERTIFICATE_TYPE_IS_NOT_ALLOW);
        }
        return bindBankCardRequest;
    }

    public static BindBankCardRequest buildBindRequest(CiticBankConfigModule configModule, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO) {
        BindBankCardRequest bindBankCardRequest = new BindBankCardRequest();
        bindBankCardRequest.setUserId(separateAccountDO.getSubAccountNo());
        bindBankCardRequest.setOpType(BindBankCardRequest.OP_TYPE_BIND);
        bindBankCardRequest.setPanNum(settlementCardDO.getOpeningNumber());
        bindBankCardRequest.setPan(settlementCardDO.getCardNumber());
        bindBankCardRequest.setAcctNm(settlementCardDO.getHolder());
        bindBankCardRequest.setBankPhone(settlementCardDO.getCellphone());
        bindBankCardRequest.setMerchantId(configModule.getMerchantId());
        bindBankCardRequest.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + separateAccountDO.getAccountNumber().substring(separateAccountDO.getAccountNumber().length() - 8));
        switch (separateAccountDO.getType()) {
            case "PERSONAL":
                personParamsBuild(separateAccountDO, settlementCardDO, bindBankCardRequest);
                break;
            case "INDIVIDUAL_BUSINESS":
                individualBusinessOperator(separateAccountDO, settlementCardDO, bindBankCardRequest);
                break;
            case "COMPANY":
                companyParamsBuild(separateAccountDO, settlementCardDO, bindBankCardRequest);
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_CERTIFICATE_TYPE_IS_NOT_ALLOW);
        }
        return bindBankCardRequest;
    }

    private static void individualBusinessOperator(BrandMerchantModule brandMerchantModule, MerchantBusinessLicenseInfo license, BindBankCardRequest bindBankCardRequest, BizBankAccountRes bankAccount) {
        switch (bankAccount.getType()) {
            case 1:
                personParamsBuild(brandMerchantModule, bankAccount, license, bindBankCardRequest);
                break;
            case 2:
                companyParamsBuild(bankAccount, license, bindBankCardRequest);
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_CARD_ACCOUNT_TYPE_IS_NOT_ALLOW);

        }
    }

    private static void individualBusinessOperator(SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO, BindBankCardRequest bindBankCardRequest) {
        switch (settlementCardDO.getType()) {
            case 1:
                personParamsBuild(separateAccountDO, settlementCardDO, bindBankCardRequest);
                break;
            case 2:
                companyParamsBuild(separateAccountDO, settlementCardDO, bindBankCardRequest);
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_CARD_ACCOUNT_TYPE_IS_NOT_ALLOW);

        }
    }

    private static void personParamsBuild(BrandMerchantModule brandMerchantModule, BizBankAccountRes bankAccount, MerchantBusinessLicenseInfo license, BindBankCardRequest bindBankCardRequest) {
        if (bankAccount.getBankName().contains("中信银行")) {
            // 中信个人账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_CITIC_PERSONAL);
        } else {
            // 其他银行个人账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_OTHER_PERSONAL);
        }
        bindBankCardRequest.setAuthProtocolVersion("1.0.0");
        bindBankCardRequest.setAuthProtocolNo(brandMerchantModule.getMerchantSn());
        switch (license.getLegal_person_id_type()) {
            case 1:
                bindBankCardRequest.setUserIdType("01");
                break;
            case 2:
                bindBankCardRequest.setUserIdType("23");
                break;
            case 3:
                bindBankCardRequest.setUserIdType("29");
                break;
            case 4:
                bindBankCardRequest.setUserIdType("28");
                break;
            case 5:
                bindBankCardRequest.setUserIdType("32");
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CITIC_ID_TYPE_IS_NULL);
        }
        bindBankCardRequest.setBankCardNo(license.getLegal_person_id_number());
    }

    private static void personParamsBuild(SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO, BindBankCardRequest bindBankCardRequest) {
        if (settlementCardDO.getBankName().contains("中信银行")) {
            // 中信个人账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_CITIC_PERSONAL);
        } else {
            // 其他银行个人账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_OTHER_PERSONAL);
        }
        bindBankCardRequest.setAuthProtocolVersion("1.0.0");
        bindBankCardRequest.setAuthProtocolNo(separateAccountDO.getAccountNumber());
        switch (separateAccountDO.getLegalPersonIdType()) {
            case "01":
                bindBankCardRequest.setUserIdType("01");
                break;
            case "02":
                bindBankCardRequest.setUserIdType("28");
                break;
            case "03":
                bindBankCardRequest.setUserIdType("34");
                break;
            case "04":
                bindBankCardRequest.setUserIdType("29");
                break;
            case "05":
                bindBankCardRequest.setUserIdType("23");
                break;
            default:
                throw new BrandBusinessException(BrandBusinessExceptionEnum.CITIC_ID_TYPE_IS_NULL);
        }
        bindBankCardRequest.setBankCardNo(separateAccountDO.getLegalPersonId());
    }

    private static void companyParamsBuild(BizBankAccountRes bankAccount, MerchantBusinessLicenseInfo license, BindBankCardRequest bindBankCardRequest) {
        if (bankAccount.getType() != 2) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_ACCOUNT_TYPE_IS_NOT_ALLOW);
        }
        if (bankAccount.getBankName().contains("中信银行")) {
            // 中信企业账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_CITIC_ENTERPRISE);
        } else {
            // 其他银行企业账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_OTHER_ENTERPRISE);
        }
        bindBankCardRequest.setUserIdType("03");
        bindBankCardRequest.setBankCardNo(license.getNumber());
    }

    private static void companyParamsBuild(SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO settlementCardDO, BindBankCardRequest bindBankCardRequest) {
        if (settlementCardDO.getType() != 2) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BANK_ACCOUNT_TYPE_IS_NOT_ALLOW);
        }
        if (settlementCardDO.getBankName().contains("中信银行")) {
            // 中信企业账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_CITIC_ENTERPRISE);
        } else {
            // 其他银行企业账户
            bindBankCardRequest.setAcctType(BindBankCardRequest.ACCT_TYPE_OTHER_ENTERPRISE);
        }
        bindBankCardRequest.setUserIdType("03");
        bindBankCardRequest.setBankCardNo(separateAccountDO.getIdNumber());
    }
}
