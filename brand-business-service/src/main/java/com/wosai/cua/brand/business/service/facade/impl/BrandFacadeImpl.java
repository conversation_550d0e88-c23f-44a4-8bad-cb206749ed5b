package com.wosai.cua.brand.business.service.facade.impl;

import com.alibaba.fastjson2.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.annotations.Validation;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.SubAccountOpenDetailDTO;
import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.BatchCreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CancelMerchantAffiliationForSchoolCanteenRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.ChangeAcquirerCheckRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantChildAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateMerchantAffiliationForSchoolCanteenRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateSecondaryBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.DeleteBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.request.ExistBrandOpenPaymentHubRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.GenerateBrandKeyRequest;
import com.wosai.cua.brand.business.api.dto.request.MemberIdDTO;
import com.wosai.cua.brand.business.api.dto.request.MerchantIdDTO;
import com.wosai.cua.brand.business.api.dto.request.ModifyBrandMerchantRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.OutMerchantNoDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.request.PageQueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.PaymentModeChangeRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.request.RegisterBehaviorDTO;
import com.wosai.cua.brand.business.api.dto.request.RelevanceWithdrawStrategyDTO;
import com.wosai.cua.brand.business.api.dto.request.SignInMemberDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BaseBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandPaymentHubOpenRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandSaveSmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandSmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.brand.CancelEnterRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandForOpenIndirectApplyRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandForReconciliationApplyRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.DeleteBrandDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.ModifyBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CheckAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CreateBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.ImportBrandMerchantForAuditRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.RemoveBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantsQueryResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandPaymentModeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.ChangeAcquirerCheckResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.GenerateKeyResponse;
import com.wosai.cua.brand.business.api.dto.response.MerchantAccountDTO;
import com.wosai.cua.brand.business.api.dto.response.MerchantBusinessLicenseInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.ModifyBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.response.PaymentModeChangeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.WithdrawStrategyInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.common.CommonResult;
import com.wosai.cua.brand.business.api.dto.response.common.MultiCommonResult;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.CheckAccountResultEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantBrandTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandConfigBusiness;
import com.wosai.cua.brand.business.service.business.BrandFileBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.business.BrandWithdrawStrategyBusiness;
import com.wosai.cua.brand.business.service.config.apollo.ApolloConfig;
import com.wosai.cua.brand.business.service.enums.RedisCacheKeyEnum;
import com.wosai.cua.brand.business.service.helper.RedisHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.withdraw.BrandWithdrawStrategyModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌服务实现
 *
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
@Component
@Slf4j
public class BrandFacadeImpl implements BrandFacade {

    private final BrandBusiness brandBusiness;

    private final BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    private final ApolloConfig apolloConfig;

    private final BrandConfigBusiness brandConfigBusiness;

    @Autowired
    private BrandFileBusiness brandFileBusiness;

    private final RedisHelper redisHelper;

    @Autowired
    public BrandFacadeImpl(BrandBusiness brandBusiness, BrandWithdrawStrategyBusiness brandWithdrawStrategyBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness, ApolloConfig apolloConfig, BrandConfigBusiness brandConfigBusiness, RedisHelper redisHelper) {
        this.brandBusiness = brandBusiness;
        this.brandWithdrawStrategyBusiness = brandWithdrawStrategyBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
        this.apolloConfig = apolloConfig;
        this.brandConfigBusiness = brandConfigBusiness;
        this.redisHelper = redisHelper;
    }

    @Override
    public CreateBrandResponseDTO createBrand(CreateBrandRequestDTO createBrandRequest) {
        return brandBusiness.createBrand(createBrandRequest);
    }

    @Override
    public CreateBrandResponseDTO createBrandForReconciliationApply(CreateBrandForReconciliationApplyRequestDTO createBrandForReconciliationApplyRequestDTO) {
        return brandBusiness.createBrandForReconciliationApply(createBrandForReconciliationApplyRequestDTO);
    }

    @Override
    public CreateBrandResponseDTO createBrandForOpenIndirectApply(CreateBrandForOpenIndirectApplyRequestDTO createBrandForBusinessOpenApplyRequest) {
        return brandBusiness.createBrandForOpenIndirectApply(createBrandForBusinessOpenApplyRequest);
    }

    @Override
    public CreateBrandResponseDTO openBrandPaymentHubForAudit(BrandPaymentHubOpenRequestDTO brandPaymentHubOpenRequest) {
        return brandBusiness.openBrandPaymentHubForAudit(brandPaymentHubOpenRequest);
    }

    @Override
    public void existBrandOpenPaymentHub(ExistBrandOpenPaymentHubRequestDTO existBrandOpenPaymentHubRequest) {
        brandBusiness.existBrandOpenPaymentHub(existBrandOpenPaymentHubRequest);
    }

    @Override
    public void importBrandMerchantForAudit(ImportBrandMerchantForAuditRequestDTO createBrandMerchantForAuditRequest) {
        brandFileBusiness.importBrandMerchantsForAudit(createBrandMerchantForAuditRequest);
    }

    @Override
    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantId(QueryBrandsDTO queryBrandsDto) {
        if (StringUtils.isBlank(queryBrandsDto.getMerchantId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        return brandBusiness.getBrandInfoListByMerchantId(queryBrandsDto.getMerchantId(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public List<BrandSimpleInfoDTO> getBrandInfoListByMerchantIds(QueryBrandsDTO queryBrandsDto) {
        if (CollectionUtils.isEmpty(queryBrandsDto.getMerchantIds())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.MERCHANT_ID_NOT_BE_NULL);
        }
        return brandBusiness.getBrandInfoListByMerchantIds(queryBrandsDto.getMerchantIds(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public BrandDetailInfoDTO getBrandDetailInfoByBrandId(QueryBrandsDTO queryBrandsDto) {
        if (StringUtils.isBlank(queryBrandsDto.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        return brandBusiness.getBrandDetailInfoByBrandId(queryBrandsDto.getBrandId(), queryBrandsDto.getNeedGetConfig());
    }

    @Override
    public BrandDetailInfoDTO getBrandDetailInfoByBrandSn(QueryBrandsDTO queryBrandsDto) {
        if (StringUtils.isBlank(queryBrandsDto.getBrandSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_SN_NOT_BE_NULL);
        }
        return brandBusiness.getBrandDetailInfoByBrandSn(queryBrandsDto.getBrandSn(), queryBrandsDto.getNeedGetConfig());
    }

    /**
     * 根据外部品牌号和服务商，查询品牌详细信息
     *
     * @param clientSn 外部品牌号
     * @param vendorId 服务商id
     * @return 品牌详细信息
     */
    @Override
    public BrandDetailInfoDTO getBrandDetailInfoByClientSnAndVendorId(String clientSn, String vendorId) {
        return brandBusiness.getBrandDetailInfoByClientSnAndVendorId(clientSn, vendorId);
    }

    @Override
    public PageBrandInfoDTO pageBrandInfoList(PageQueryBrandsDTO pageQueryBrandsDto) {
        if (Objects.isNull(pageQueryBrandsDto.getPage())) {
            pageQueryBrandsDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageQueryBrandsDto.getPageSize())) {
            pageQueryBrandsDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        return brandBusiness.pageBrandInfoListByBrandCondition(pageQueryBrandsDto);
    }

    @Override
    public Boolean deleteBrand(DeleteBrandDTO deleteBrandDto) {
        brandBusiness.deleteBrand(deleteBrandDto.getBrandId());
        return true;
    }

    @Override
    public int deleteBrandMerchant(DeleteBrandMerchantDTO deleteBrandMerchantDto) {
        return brandBusiness.deleteBrandMerchant(deleteBrandMerchantDto.getBrandId(), deleteBrandMerchantDto.getMerchantIdList(), deleteBrandMerchantDto.getNeedDeleteMerchant());
    }

    @Override
    public int batchCreateBrandMerchant(BatchCreateBrandMerchantRequestDTO batchCreateBrandMerchantRequest) {
        return brandBusiness.batchCreateBrandMerchants(batchCreateBrandMerchantRequest);
    }

    @Override
    public CreateBrandMerchantResponseDTO createBrandMerchant(CreateBrandMerchantRequestDTO createBrandMerchant) {
        return brandBusiness.createBrandMerchant(createBrandMerchant);
    }

    @Override
    public CreateBrandMerchantResponseDTO createSecondaryBrandMerchant(CreateSecondaryBrandMerchantRequestDTO createSecondaryBrandMerchant) {
        return brandBusiness.createSecondaryBrandMerchant(createSecondaryBrandMerchant);
    }

    @Override
    public PageBrandMerchantsDTO pageQueryBrandMerchants(PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto) {
        if (CollectionUtils.isEmpty(pageQueryBrandMerchantsDto.getBrandIds()) && CollectionUtils.isEmpty(pageQueryBrandMerchantsDto.getBrandSnList())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_OR_BRAND_SN_NOT_BE_NULL);
        }
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPage())) {
            pageQueryBrandMerchantsDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPageSize())) {
            pageQueryBrandMerchantsDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        if (Objects.nonNull(pageQueryBrandMerchantsDto.getNeedPaging()) && Boolean.TRUE.equals(!pageQueryBrandMerchantsDto.getNeedPaging())) {
            pageQueryBrandMerchantsDto.setPage(null);
            pageQueryBrandMerchantsDto.setPageSize(null);
        }
        return brandBusiness.pageQueryBrandMerchants(pageQueryBrandMerchantsDto);
    }

    @Override
    public PageBrandMerchantsDTO pageQuerySimpleBrandMerchants(PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto) {
        if (CollectionUtils.isEmpty(pageQueryBrandMerchantsDto.getBrandIds()) && CollectionUtils.isEmpty(pageQueryBrandMerchantsDto.getBrandSnList())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_OR_BRAND_SN_NOT_BE_NULL);
        }
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPage())) {
            pageQueryBrandMerchantsDto.setPage(apolloConfig.getDefaultPage());
        }
        if (Objects.isNull(pageQueryBrandMerchantsDto.getPageSize())) {
            pageQueryBrandMerchantsDto.setPageSize(apolloConfig.getDefaultPageSize());
        }
        if (Objects.nonNull(pageQueryBrandMerchantsDto.getNeedPaging()) && Boolean.TRUE.equals(!pageQueryBrandMerchantsDto.getNeedPaging())) {
            pageQueryBrandMerchantsDto.setPage(null);
            pageQueryBrandMerchantsDto.setPageSize(null);
        }
        return brandBusiness.pageQuerySimpleBrandMerchants(pageQueryBrandMerchantsDto);
    }

    @Override
    public List<BrandMerchantDTO> getAllBrandMerchantsByBrandId(QueryBrandsDTO queryBrandsDTO) {
        if (WosaiStringUtils.isEmpty(queryBrandsDTO.getBrandId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_ID_NOT_BE_NULL);
        }
        return brandBusiness.getAllBrandMerchantsByBrandId(queryBrandsDTO.getBrandId(),queryBrandsDTO.getSelectChildBrand());
    }

    @Override
    public List<BrandMerchantDTO> getAllBrandMerchantsByBrandSn(QueryBrandsDTO queryBrandsDTO) {
        if (WosaiStringUtils.isEmpty(queryBrandsDTO.getBrandSn())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.BRAND_SN_NOT_BE_NULL);
        }
        return brandBusiness.getAllBrandMerchantsByBrandSn(queryBrandsDTO.getBrandSn(),queryBrandsDTO.getSelectChildBrand());
    }

    @Override
    public ModifyBrandResponseDTO modifyBrand(ModifyBrandRequestDTO modifyBrandRequestDto) {
        return brandBusiness.modifyBrand(modifyBrandRequestDto);
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByStoreIdOrMerchantSn(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        if (this.checkIsNullDataByCache(queryBrandMerchantInfo)) return null;
        BrandMerchantInfoDTO brandMerchantInfoDTO = this.getBrandMerchantInfoByCache(queryBrandMerchantInfo);
        if (brandMerchantInfoDTO != null) return brandMerchantInfoDTO;
        List<BrandMerchantModule> brandMerchantByConditions = brandBusiness.getBrandMerchantByConditions(queryBrandMerchantInfo);
        if (CollectionUtils.isEmpty(brandMerchantByConditions)) {
            this.saveNullDataIntoCache(queryBrandMerchantInfo);
            return null;
        }
        List<Long> strategyIdList = brandMerchantByConditions.stream().map(BrandMerchantModule::getStrategyId).distinct().collect(Collectors.toList());
        List<String> brandIdList = brandMerchantByConditions.stream().map(BrandMerchantModule::getBrandId).distinct().collect(Collectors.toList());
        List<BrandModule> brandByBrandIds = brandBusiness.getBrandByBrandIds(brandIdList);
        List<BrandWithdrawStrategyModule> withdrawStrategyList = brandWithdrawStrategyBusiness.getWithdrawStrategyListByStrategyIdList(strategyIdList);
        Map<Long, BrandWithdrawStrategyModule> brandWithdrawStrategyModuleMap = withdrawStrategyList.stream().collect(Collectors.toMap(BrandWithdrawStrategyModule::getStrategyId, Function.identity()));
        Map<String, BrandModule> brandModuleMap = brandByBrandIds.stream().collect(Collectors.toMap(BrandModule::getBrandId, Function.identity()));
        BrandMerchantModule brandMerchantModule = brandMerchantByConditions.get(0);
        BrandModule brandModule = brandModuleMap.get(brandMerchantModule.getBrandId());
        BrandWithdrawStrategyModule brandWithdrawStrategyModule = brandWithdrawStrategyModuleMap.get(brandMerchantModule.getStrategyId());
        if (Objects.isNull(brandModule)) {
            log.warn("未查到品牌信息。");
            return null;
        }
        // 查询子账号以及充值账户信息
        brandBusiness.getSubAccountInfoAndTopUpAccountInfo(brandMerchantModule, brandModule.getFundManagementCompanyCode());
        // 补充参数
        brandBusiness.supplementaryBrandMerchantParameter(brandMerchantModule);
        // 查默认提现银行卡
        BrandMerchantInfoDTO brandMerchantInfo = JSON.parseObject(JSON.toJSONString(brandMerchantModule), BrandMerchantInfoDTO.class);
        brandMerchantInfo.setBrandSn(brandModule.getSn());
        brandMerchantInfo.setBrandName(brandModule.getName());
        brandMerchantInfo.setMerchantType(brandMerchantModule.getMerchantType());
        brandMerchantInfo.setElmStoreSn(brandMerchantModule.getAssociatedElmStoreSn());
        brandMerchantInfo.setSqbStoreId(brandMerchantModule.getAssociatedSqbStoreId());
        brandMerchantInfo.setSqbStoreSn(brandMerchantModule.getSqbStoreSn());
        brandMerchantInfo.setDyStoreSn(brandMerchantModule.getDyStoreSn());
        brandMerchantInfo.setMeiTuanStoreSn(brandMerchantModule.getAssociatedMeituanStoreSn());
        brandMerchantInfo.setMeiTuanStoreStatus(brandBusiness.getMeiTuanStoreStatus(brandModule.getBrandId(), brandMerchantModule.getAssociatedMeituanStoreSn()));
        brandMerchantInfo.setConfig(brandBusiness.getBrandConfig(brandModule, queryBrandMerchantInfo.getNeedGetConfig()));
        MerchantAccountDTO merchantAccount = new MerchantAccountDTO();
        merchantAccount.setMemberId(brandMerchantModule.getMemberId());
        merchantAccount.setSubAccountName(brandMerchantModule.getSubAccountName());
        merchantAccount.setTopUpAccountName(brandMerchantModule.getTopUpAccountName());
        merchantAccount.setSubAccount(brandMerchantModule.getSubAccountNo());
        merchantAccount.setTopUpAccountNo(brandMerchantModule.getTopUpAccountNo());
        brandMerchantInfo.setMerchantAccount(merchantAccount);
        if (Objects.nonNull(brandWithdrawStrategyModule)) {
            WithdrawStrategyInfoDTO withdrawStrategyInfo = JSON.parseObject(JSON.toJSONString(brandWithdrawStrategyModule), WithdrawStrategyInfoDTO.class);
            if (Objects.nonNull(brandWithdrawStrategyModule.getReservedAmount()) && brandWithdrawStrategyModule.getReservedAmount() > 0) {
                withdrawStrategyInfo.setReservedAmount(new BigDecimal(brandWithdrawStrategyModule.getReservedAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(brandWithdrawStrategyModule.getMinWithdrawalAmount()) && brandWithdrawStrategyModule.getMinWithdrawalAmount() > 0) {
                withdrawStrategyInfo.setMinWithdrawalAmount(new BigDecimal(brandWithdrawStrategyModule.getMinWithdrawalAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
            }
            brandMerchantInfo.setWithdrawStrategyInfo(withdrawStrategyInfo);
        }
        MerchantBusinessLicenseInfo merchantBusinessLicense = brandMerchantModule.getMerchantBusinessLicense();
        if (Objects.nonNull(merchantBusinessLicense) && Objects.nonNull(merchantBusinessLicense.getType())) {
            brandMerchantInfo.setMerchantBusinessLicenseInfo(
                    new MerchantBusinessLicenseInfoDTO(
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_name() : merchantBusinessLicense.getName(),
                            merchantBusinessLicense.getLegal_person_name(),
                            merchantBusinessLicense.getType(),
                            merchantBusinessLicense.getType() == 0 ? merchantBusinessLicense.getLegal_person_id_number() : merchantBusinessLicense.getNumber(),
                            merchantBusinessLicense.getLegal_person_id_number(),
                            merchantBusinessLicense.getLegal_person_id_type()
                    )
            );
        }
        // 获取提现银行卡
        brandMerchantInfo.setBrandMerchantBankCard(brandMerchantBankAccountBusiness.getDefaultBankCard(brandModule.getBrandId(), brandMerchantModule.getMerchantId()));
        brandMerchantInfo.setAccountOpenStatusDesc(BrandMerchantAccountOpenStatusEnum.getStatusDescription(brandMerchantInfo.getAccountOpenStatus()));
        this.saveBrandInfoIntoCacheByMerchantSnOrStoreId(queryBrandMerchantInfo, brandMerchantInfo);
        return brandMerchantInfo;
    }

    private @Nullable BrandMerchantInfoDTO getBrandMerchantInfoByCache(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getMerchantSn())) {
            BrandMerchantInfoDTO brandMerchantInfoDTO = redisHelper.getObjByKey(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN, queryBrandMerchantInfo.getMerchantSn()), BrandMerchantInfoDTO.class);
            if (Objects.nonNull(brandMerchantInfoDTO)) {
                return brandMerchantInfoDTO;
            }
        }
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getSqbStoreId())) {
            BrandMerchantInfoDTO brandMerchantInfoDTO = redisHelper.getObjByKey(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_SEARCH_MERCHANT_INFO_BY_STORE_ID, queryBrandMerchantInfo.getSqbStoreId()), BrandMerchantInfoDTO.class);
            if (Objects.nonNull(brandMerchantInfoDTO)) {
                return brandMerchantInfoDTO;
            }
        }
        return null;
    }

    private void saveBrandInfoIntoCacheByMerchantSnOrStoreId(QueryBrandMerchantInfoDTO queryBrandMerchantInfo, BrandMerchantInfoDTO brandMerchantInfo) {
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getMerchantSn())) {
            redisHelper.setCache(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN, queryBrandMerchantInfo.getMerchantSn()), brandMerchantInfo, 15 * 60);
        }
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getSqbStoreId())) {
            redisHelper.setCache(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_SEARCH_MERCHANT_INFO_BY_STORE_ID, queryBrandMerchantInfo.getSqbStoreId()), brandMerchantInfo, 15 * 60);
        }
    }

    private void saveNullDataIntoCache(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getSqbStoreId())) {
            redisHelper.setCache(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_STORE_ID, queryBrandMerchantInfo.getSqbStoreId()), "1", 60);
        }
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getMerchantSn())) {
            redisHelper.setCache(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN, queryBrandMerchantInfo.getMerchantSn()), "1", 60);
        }
    }

    private boolean checkIsNullDataByCache(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getSqbStoreId())) {
            String result = redisHelper.getObjByKey(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_STORE_ID, queryBrandMerchantInfo.getSqbStoreId()), String.class);
            return StringUtils.isNotBlank(result);
        }
        if (StringUtils.isNotBlank(queryBrandMerchantInfo.getMerchantSn())) {
            String result = redisHelper.getObjByKey(RedisCacheKeyEnum.getKey(RedisCacheKeyEnum.CACHE_KEY_NO_SEARCH_MERCHANT_INFO_BY_MERCHANT_SN, queryBrandMerchantInfo.getMerchantSn()), String.class);
            return StringUtils.isNotBlank(result);
        }
        return false;
    }

    @Override
    public Boolean createBrandMerchantChildAccount(CreateBrandMerchantChildAccountDTO createBrandMerchantChildAccount) {
        return brandBusiness.createBrandMerchantChildAccount(createBrandMerchantChildAccount.getMerchantSn(), createBrandMerchantChildAccount.getBrandId());
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByMerchantId(MerchantIdDTO merchantIdDTO) {
        return brandBusiness.getBrandMerchantInfoByMerchantId(merchantIdDTO.getMerchantId());
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByMemberId(MemberIdDTO memberIdDto) {
        return brandBusiness.getBrandMerchantInfoByMemberId(memberIdDto.getMemberId());
    }

    @Override
    public BrandMerchantInfoDTO getBrandMerchantInfoByOutMerchantNo(OutMerchantNoDTO outMerchantNoDto) {
        return brandBusiness.getBrandMerchantInfoByOutMerchantNo(outMerchantNoDto.getBrandId(), outMerchantNoDto.getOutMerchantNo());
    }

    @Override
    public int relevanceBrandWithdrawStrategy(RelevanceWithdrawStrategyDTO relevanceWithdrawStrategy) {
        return brandBusiness.relevanceBrandWithdrawStrategy(relevanceWithdrawStrategy.getStrategyId(), relevanceWithdrawStrategy.getBrandId(), relevanceWithdrawStrategy.getMerchantSnList());
    }

    @Override
    @Validation
    public CreateMerchantResponseDTO createMerchant(CreateMerchantDTO createMerchant) {
        return brandBusiness.createMerchant(createMerchant);
    }

    @Override
    public void signInMember(SignInMemberDTO signInMember) {
        brandBusiness.signInMember(signInMember.getMerchantId(), signInMember.getBrandId());
    }

    @Override
    public CreateMerchantResponseDTO reRegisterMemberInfo(ModifyBrandMerchantRequestDTO brandMerchantRequest) {
        return brandBusiness.registerMemberInfo(brandMerchantRequest.getBrandId(), brandMerchantRequest.getMerchantId());
    }

    @Override
    public void reGetMemberId(SignInMemberDTO signInMember) {
        brandBusiness.reGetMemberId(signInMember.getBrandId(), signInMember.getMerchantId());
    }

    @Override
    public Boolean registerBehaviorRecord(RegisterBehaviorDTO registerBehavior) {
        return brandBusiness.registerBehaviorRecord(registerBehavior.getBrandId(), registerBehavior.getMerchantId());
    }

    @Override
    public BrandConfigDTO getBrandConfigByChannelId(String channelId, FundManagementCompanyEnum channelType) {
        return brandBusiness.getBrandConfigByChannelId(channelId, channelType);
    }

    @Override
    public void dealPabConfig() {
        brandBusiness.dealPabConfig();
    }

    @Override
    public Boolean createBrandConfig(ConfigDTO config) {
        return brandConfigBusiness.createBrandConfigWithNotify(config);
    }

    @Override
    public Boolean modifyBrandMerchant(ModifyBrandMerchantDTO modifyBrandMerchant) {
        brandBusiness.modifyBrandMerchant(modifyBrandMerchant);
        return true;
    }

    @Override
    public MerchantBrandTypeEnum queryMerchantBrandType(MerchantIdDTO merchantIdDTO) {
        return brandBusiness.queryMerchantBrandType(merchantIdDTO.getMerchantId());
    }

    @Override
    public ChangeAcquirerCheckResponseDTO checkChangeAcquirer(ChangeAcquirerCheckRequestDTO changeAcquirerCheckRequestDTO) {
        return brandBusiness.checkChangeAcquirer(changeAcquirerCheckRequestDTO);
    }

    @Override
    public PaymentModeChangeResponseDTO changePaymentMode(PaymentModeChangeRequestDTO paymentModeChangeRequestDTO) {
        return brandBusiness.changePaymentMode(paymentModeChangeRequestDTO);
    }

    @Override
    public BrandPaymentModeResponseDTO queryBrandPaymentModeForContract(String merchantId) {
        return brandBusiness.queryBrandPaymentModeForContract(merchantId);
    }

    @Override
    public Boolean cancelEnter(CancelEnterRequestDTO cancelEnterRequest) {
        return brandBusiness.cancelEnter(cancelEnterRequest.getBrandId(), cancelEnterRequest.getMerchantId(), cancelEnterRequest.getCancelType());
    }

    @Override
    public SubAccountOpenDetailDTO querySubAccountOpenStatus(QueryBrandMerchantInfoDTO queryBrandMerchantInfo) {
        return brandBusiness.querySubAccountOpenStatus(queryBrandMerchantInfo.getBrandId(), queryBrandMerchantInfo.getMerchantSn());
    }

    @Override
    public CommonResult singleRemoveBrandMerchantAssociation(RemoveBrandMerchantAssociationRequestDTO requestDTO) {
        return brandBusiness.singleRemoveBrandMerchantAssociation(requestDTO);
    }

    @Override
    public CommonResult singleCreateBrandMerchantAssociation(CreateBrandMerchantAssociationRequestDTO requestDTO) {
        return brandBusiness.singleCreateBrandMerchantAssociation(requestDTO);
    }

    @Override
    public CommonResult specialTreatmentBrandMerchantOpenAccount(BaseBrandRequestDTO request) {
        return brandBusiness.specialTreatmentBrandMerchantOpenAccount(request.getBrandId());
    }

    @Override
    public Boolean saveBrandSmsTemplateConfig(BrandSaveSmsTemplateConfigRequest request) {
        BrandDetailInfoDTO brand = brandBusiness.getBrandDetailInfoByBrandId(request.getBrandId(), false);
        if (Objects.isNull(brand)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        brandConfigBusiness.saveBrandSmsTemplateConfig(request.getBrandId(), request.getMethod(), request.getTemplateConfigs());
        return true;
    }

    @Override
    public List<TemplateConfigDTO> getBrandSmsTemplateConfig(BrandSmsTemplateConfigRequest request) {
        BrandDetailInfoDTO brand = brandBusiness.getBrandDetailInfoByBrandId(request.getBrandId(), false);
        if (Objects.isNull(brand)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        return brandConfigBusiness.getBrandSmsTemplateConfig(request.getBrandId(), request.getMethod(), request.getTemplateCodes());
    }

    @Override
    public CommonResult handleBrandMerchantBindCard(BaseBrandRequestDTO request) {
        return brandBusiness.handleBrandMerchantBindCard(request.getBrandId());
    }

    @Override
    public CommonResult batchOpenSubAccounts(BaseBrandRequestDTO request) {
        return brandBusiness.batchOpenSubAccounts(request.getBrandId());
    }

    @Override
    public CheckAccountResultEnum checkMerchantAccount(CheckAccountDTO checkAccountDto) {
        return brandBusiness.checkMerchantAccount(checkAccountDto.getBrandId(), checkAccountDto.getMerchantSn(), checkAccountDto.getOutMerchantNo(), checkAccountDto.getIdentifyNo());
    }

    @Override
    public List<BrandMerchantDTO> getMerchantSimpleListByBrandId(BaseBrandRequestDTO request) {
        return brandBusiness.getMerchantSimpleListByBrandId(request.getBrandId());
    }

    @Override
    public GenerateKeyResponse generateBrandKey(GenerateBrandKeyRequest request) {
        return GenerateKeyResponse.builder().publicKey(brandBusiness.generateBrandKey(request.getBrandId(), request.getManagementPublicKey())).build();
    }

    @Override
    public GenerateKeyResponse getBrandKey(BaseBrandRequestDTO request) {
        return GenerateKeyResponse.builder().publicKey(brandBusiness.getBrandKey(request.getBrandId())).build();
    }

    @Override
    public void enableSft(BaseBrandRequestDTO request) {
        brandBusiness.enableSft(request.getBrandId());
    }

    @Override
    public CommonResult createMerchantAffiliationForSchoolCanteen(CreateMerchantAffiliationForSchoolCanteenRequestDTO request) {
        try {
            brandBusiness.createMerchantAffiliationForSchoolCanteen(request);
            return CommonResult.success();
        } catch (BrandBusinessException e) {
            log.warn("高校食堂挂靠失败 {}", JSON.toJSONString(request), e);
            return CommonResult.fail(e.getMessage());
        } catch (Exception e) {
            log.error("高校食堂挂靠失败 {}", JSON.toJSONString(request), e);
            return CommonResult.fail(BrandBusinessExceptionEnum.SYSTEM_ERROR.getMessage());
        }
    }

    @Override
    public MultiCommonResult cancelMerchantAffiliationForSchoolCanteen(CancelMerchantAffiliationForSchoolCanteenRequestDTO request) {
        try {
            Map<String, CommonResult> stringCommonResultMap = brandBusiness.cancelMerchantAffiliationForSchoolCanteen(request);
            return MultiCommonResult.success(stringCommonResultMap);
        } catch (BrandBusinessException e) {
            log.warn("高校食堂解除挂靠失败 {}", JSON.toJSONString(request), e);
            return MultiCommonResult.fail(e.getMessage());
        } catch (Exception e) {
            log.error("高校食堂解除挂靠系统异常 {}", JSON.toJSONString(request), e);
            return MultiCommonResult.fail(BrandBusinessExceptionEnum.SYSTEM_ERROR.getMessage());
        }
    }

    @Override
    public BrandMerchantsQueryResponseDTO queryBrandMerchants(String merchantId) {
        return brandBusiness.queryBrandMerchants(merchantId);
    }
}
