package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import lombok.Data;

import java.util.Date;

/**
 * 分账账户信息表
 *
 * @TableName separate_account
 */
@TableName(value = "separate_account")
@Data
@SensitiveClass
public class SeparateAccountDO {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 子账号类型：FRANCHISE-加盟、BRAND_OWNED-品牌自有、BRAND_SELF_OPERATED-品牌自营、SUPPLIER-供应
     */
    private String accountType;

    /**
     * 类型：PERSONAL-个人/小微、INDIVIDUAL_BUSINESS-个体工商户、COMPANY-企业
     */
    private String type;

    /**
     * 证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照，99-统一社会信用代码
     */
    private String idType;

    /**
     * 证件编号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String idNumber;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照
     */
    private String legalPersonIdType;

    /**
     * 法人证件编号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String legalPersonId;

    /**
     * 法人电话
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String legalPersonPhone;
    /**
     * 联系人姓名
     */
    private String contractName;

    /**
     * 联系人手机号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String contractPhone;

    /**
     * 联系人证件类型
     */
    private String contractIdType;

    /**
     * 联系人证件号码
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String contractId;

    /**
     * 资管子账号
     */
    private String subAccountNo;

    /**
     * 第三方会员ID
     */
    private String memberId;

    /**
     * 子账号开通状态：HAVE_NOT_OPENED-未开通，IN_OPENNING-开通中，OPENED-已开通，OPENING_FAILURE-开通失败，UNDER_REVIEW-审核中，TO_BE_ACTIVATED-待激活
     */
    private String accountOpenStatus;

    /**
     * 子账号开通失败原因
     */
    private String accountOpenFailureReason;

    /**
     * 子账号开通时间
     */
    private Date accountOpenedTime;

    /**
     * 结算卡绑定状态：NOT_BOUND-未绑卡，UNACTIVATED-未激活，ACTIVATED-已激活
     */
    private String settleCardStatus;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 结算策略ID
     */
    private Long strategyId;

    /**
     * 删除状态(1-已删除，0-未删除)
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}