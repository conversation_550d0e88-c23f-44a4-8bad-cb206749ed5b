package com.wosai.cua.brand.business.service.domain.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.CountBrandMerchantNumberDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandMerchantMapper extends BaseMapper<BrandMerchantDO> {
    /**
     * 根据merchantId查询品牌关系
     *
     * @param merchantId 商户id
     * @return 品牌商户关系集合
     */
    List<BrandMerchantDO> selectBrandMerchantByMerchantId(@Param("merchantId") String merchantId);

    /**
     * 根据商户查询条件查询品牌商户关系
     *
     * @param merchantConditions 商户关系查询条件
     * @return 品牌商户关系集合
     */
    List<BrandMerchantDO> selectBrandMerchantByConditions(QueryMerchantConditionsDO merchantConditions);

    /**
     * 根据品牌id和商户id集合查询品牌商户关系
     *
     * @param brandId     品牌id
     * @param merchantIds 商户id集合
     * @return 品牌商户关系集合
     */
    List<BrandMerchantDO> selectBrandMerchantByBrandIdAndMerchantIds(@Param("brandId") String brandId, @Param("merchantIds") List<String> merchantIds);

    /**
     * 根据品牌id和商户id查询品牌商户关系
     *
     * @param brandId    品牌id
     * @param merchantId 商户id
     * @return 品牌商户关系集合
     */
    BrandMerchantDO selectBrandMerchantByBrandIdAndMerchantId(@Param("brandId") String brandId, @Param("merchantId") String merchantId, @Param("deleted") Integer deleted);

    /**
     * 删除品牌和商户的关联关系（逻辑删除）
     *
     * @param brandId     品牌
     * @param merchantIds 商户id集合
     * @return 删除条数
     */
    int deleteBrandMerchantByBrandIdAndMerchantId(@Param("brandId") String brandId, @Param("merchantIds") List<String> merchantIds);

    /**
     * 批量插入品牌商户关系
     *
     * @param list 插入对象集合
     * @return 插入条数
     */
    int batchInsertBrandMerchant(@Param("list") List<BrandMerchantDO> list);

    /**
     * 根据条件统计品牌商户数量
     *
     * @param merchantConditions 查询条件
     * @return 数量
     */
    int countBrandMerchantsByConditions(QueryMerchantConditionsDO merchantConditions);

    /**
     * 分页查询品牌下的商户信息
     *
     * @param conditions 查询条件
     * @param offset     偏移量
     * @param pageSize   查询数量
     * @return 品牌下商户信息集合
     */
    List<BrandMerchantDO> pageBrandMerchantsByConditions(@Param("conditions") QueryMerchantConditionsDO conditions, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 删除商户提现策略
     *
     * @param strategyIds 提现策略id集合
     * @return 删除条数
     */
    int deleteBrandMerchantWithdrawStrategy(@Param("strategyIds") List<Long> strategyIds);

    /**
     * 查询品牌下商户数量
     *
     * @param brandIds 品牌id集合
     * @return 品牌商户数量
     */
    List<CountBrandMerchantNumberDO> countMerchantNumber(@Param("brandIds") List<String> brandIds);

    /**
     * 根据merchantId获取品牌商户信息
     * @param merchantId memberId
     * @return 品牌商户信息
     */
    BrandMerchantDO getBrandMerchantInfoByMerchantId(@Param("merchantId") String merchantId);

    /**
     * 根据memberId获取品牌商户信息
     *
     * @param memberId memberId
     * @return 品牌商户信息
     */
    List<BrandMerchantDO> getBrandMerchantInfoByMemberId(@Param("memberId") String memberId);

    /**
     * 根据子账号获取品牌商户信息
     *
     * @param subAccountNo 子账号
     * @return 品牌商户信息
     */
    List<BrandMerchantDO> getBrandMerchantInfoByBrandIdAndSubAccountNo(@Param("brandId") String brandId,@Param("subAccountNo")String subAccountNo);

    /**
     * 关联提现策略
     *
     * @param brandId        品牌id
     * @param strategyId     策略id
     * @param merchantSnList 商户编号集合
     * @return 关联成功数量
     */
    int relevanceBrandWithdrawStrategy(@Param("strategyId") Long strategyId, @Param("brandId") String brandId, @Param("merchantSnList") List<String> merchantSnList);

    /**
     * 根据品牌id和id分页查询品牌商户信息
     *
     * @param brandId  品牌id
     * @param id       主键id
     * @param pageSize 每次查询条数
     * @return 品牌商户信息
     */
    List<BrandMerchantDO> pageGetBrandMerchantByBrandIdAndId(@Param("brandId") String brandId, @Param("id") Long id, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询品牌商户信息
     *
     * @param id       主键id
     * @param pageSize 每次查询条数
     * @return 品牌商户信息
     */
    List<BrandMerchantDO> pageBrandMerchantById(@Param("id") Long id, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询品牌商户信息
     *
     * @param id       主键id
     * @param pageSize 每次查询条数
     * @return 品牌商户信息
     */
    List<BrandMerchantDO> pageBrandMerchantByIdAndConditions(@Param("id") Long id, @Param("pageSize") Integer pageSize,@Param("conditions") QueryMerchantConditionsDO conditions);
}