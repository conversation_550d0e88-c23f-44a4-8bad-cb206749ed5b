package com.wosai.cua.brand.business.service.excel.handler;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.BrandTaskStatusEnum;
import com.wosai.cua.brand.business.api.enums.ExcelImportTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.excel.AbstractImportExcelHandler;
import com.wosai.cua.brand.business.service.excel.context.ImportExcelAuditContext;
import com.wosai.cua.brand.business.service.excel.data.SimpleBrandMerchantOpData;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import com.wosai.cua.brand.business.service.module.log.BrandSubTaskModule;
import com.wosai.cua.brand.business.service.module.log.BrandTaskLogModule;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Slf4j
@Component
public class SimpleBrandMerchantDeleteHandler extends AbstractImportExcelHandler<ImportExcelAuditContext, SimpleBrandMerchantOpData> {

    @Autowired
    private BrandDomainService brandDomainService;
    @Autowired
    private MerchantService coreMerchantService;

    @Override
    public ImportExcelAuditContext initContext(BrandTaskLogModule brandTaskLogModule) {
        return ImportExcelAuditContext.newInstance(brandTaskLogModule);
    }

    @Override
    public ExcelImportTypeEnum getAuditImportType() {
        return ExcelImportTypeEnum.RECONCILIATION_DELETE_MERCHANT;
    }

    @Override
    public void preCheck(ImportExcelAuditContext context) {
        String brandId = context.getBrandId();
        BrandModule brandModule = brandDomainService.getBrandModuleByBrandId(brandId);
        if (Objects.isNull(brandModule)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        context.bindBrandModule(brandModule);
    }


    @Override
    protected void doHandleData(ImportExcelAuditContext context, BrandSubTaskModule brandSubTaskModule) {
        SimpleBrandMerchantOpData data = JSON.parseObject(brandSubTaskModule.getSubTaskContext(), SimpleBrandMerchantOpData.class);
        try {
            Map merchant = coreMerchantService.getMerchantBySn(data.getMerchantSn());
            if (WosaiMapUtils.isNotEmpty(merchant)) {
                brandDomainService.deleteBrandMerchantFromAudit(context.getBrandId(), Collections.singletonList(WosaiMapUtils.getString(merchant, DaoConstants.ID)), context.getAuditSn());
            }
            data.setResult("成功");
            brandSubTaskModule.setSubTaskContext(JSON.toJSONString(data));
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.SUCCESS.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "成功")));
        } catch (Exception e) {
            log.error("处理数据异常 brandSubTaskId:{}", brandSubTaskModule.getId(), e);
            data.setResult("失败：" + e.getMessage());
            brandSubTaskModule.setTaskStatus(BrandTaskStatusEnum.FAIL.getTaskStatus());
            brandSubTaskModule.setTaskResult(JSON.toJSONString(CollectionUtil.hashMap("result", "失败:" + e.getMessage())));
        }
    }
}
