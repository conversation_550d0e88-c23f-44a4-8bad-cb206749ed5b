package com.wosai.cua.brand.business.service.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.enums.SeparateAccountRelatedTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【separate_account_related(分账账户关联表)】的数据库操作Service
 * @createDate 2025-06-05 10:17:57
 */
public interface SeparateAccountRelatedDOService extends IService<SeparateAccountRelatedDO> {
    /**
     * 添加分账账户关联
     *
     * @param accountNumber 分账账户编号
     * @param type          关联类型
     * @param relatedSn     关联编号
     * @return 分账账户关联关系
     */
    SeparateAccountRelatedDO addSeparateAccountRelated(String accountNumber, SeparateAccountRelatedTypeEnum type, String relatedSn);

    /**
     * 删除分账账户关联
     *
     * @param accountNumber 分账账户编号
     * @param type          关联类型
     * @param relatedSn     关联编号
     * @return 是否删除成功
     */
    Boolean deleteSeparateAccountRelated(String accountNumber, SeparateAccountRelatedTypeEnum type, String relatedSn);

    /**
     * 获取分账账户关联
     *
     * @param accountNumber 分账账户编号
     * @return 分账账户关联
     */
    List<SeparateAccountRelatedDO> getSeparateAccountRelatedList(String accountNumber);

    /**
     * 获取分账账户关联
     *
     * @param accountNumbers 分账账户编号
     * @return 分账账户关联
     */
    Map<String, List<SeparateAccountRelatedDO>> getSeparateAccountRelatedMap(List<String> accountNumbers);

    /**
     * 检查关联编号是否存在
     *
     * @param type      关联类型
     * @param relatedSn 关联编号
     * @return 是否存在
     */
    boolean checkRelatedSnExist(String accountNumber, SeparateAccountRelateTypeEnum type, String relatedSn);

    /**
     * 通过关联编号获取关联账号
     *
     * @param brandId   品牌id
     * @param relatedSn 关联编号
     * @param type      关联类型
     * @return 关联账号
     */
    List<String> getAccountNumberByRelatedSn(String brandId, String relatedSn, SeparateAccountRelateTypeEnum type);
}
