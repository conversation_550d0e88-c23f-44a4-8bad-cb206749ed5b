package com.wosai.cua.brand.business.service.job;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.service.BankCardDomainService;
import com.wosai.cua.brand.business.service.domain.service.BrandDomainService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountRelatedDOService;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountSettlementCardDOService;
import com.wosai.cua.brand.business.service.module.bank.BankCardModule;
import com.wosai.cua.brand.business.service.module.brand.BrandConditionModule;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.PageBrandModule;
import com.wosai.cua.brand.business.service.module.merchant.MerchantConditionModule;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SeparateAccountInfoInitJobHandler {

    private final BrandDomainService brandDomainService;

    private final BankCardDomainService bankCardDomainService;

    private final SeparateAccountDOService separateAccountDOService;

    private final SeparateAccountRelatedDOService separateAccountRelatedDOService;

    private final SeparateAccountSettlementCardDOService separateAccountSettlementCardDOService;

    private final MerchantService merchantService;

    private final MerchantBusinessLicenseService merchantBusinessLicenseService;

    private final MerchantBizBankAccountService merchantBizBankAccountService;

    public SeparateAccountInfoInitJobHandler(BrandDomainService brandDomainService, BankCardDomainService bankCardDomainService, SeparateAccountDOService separateAccountDOService, SeparateAccountRelatedDOService separateAccountRelatedDOService, SeparateAccountSettlementCardDOService separateAccountSettlementCardDOService, MerchantService merchantService, MerchantBusinessLicenseService merchantBusinessLicenseService, MerchantBizBankAccountService merchantBizBankAccountService) {
        this.brandDomainService = brandDomainService;
        this.bankCardDomainService = bankCardDomainService;
        this.separateAccountDOService = separateAccountDOService;
        this.separateAccountRelatedDOService = separateAccountRelatedDOService;
        this.separateAccountSettlementCardDOService = separateAccountSettlementCardDOService;
        this.merchantService = merchantService;
        this.merchantBusinessLicenseService = merchantBusinessLicenseService;
        this.merchantBizBankAccountService = merchantBizBankAccountService;
    }

    /**
     * 查询品牌下商户数据并转换为Map结构
     *
     * @param brandId 品牌ID
     * @return 商户Map，key为merchantId
     */
    private void processMerchantData(String brandId) {
        int pageSize = 100;
        int currentPage = 1;

        while (true) {
            // 构建分页查询条件
            MerchantConditionModule conditionModule = new MerchantConditionModule();
            conditionModule.setBrandId(brandId);
            conditionModule.setPage(currentPage);
            conditionModule.setPageSize(pageSize);

            // 执行分页查询
            PageBrandMerchantModule pageBrandMerchantModule = brandDomainService.pageQueryBrandMerchantModule(conditionModule);

            // 如果没有数据了,退出循环
            if (pageBrandMerchantModule == null || pageBrandMerchantModule.getBrandMerchantModuleList() == null
                    || pageBrandMerchantModule.getBrandMerchantModuleList().isEmpty()) {
                break;
            }

            Map<String, BrandMerchantModule> brandMerchantModuleMap = pageBrandMerchantModule.getBrandMerchantModuleList().stream()
                    .filter(brandMerchantModule -> !MerchantTypeEnum.BRAND_ADMIN.getMerchantType().equals(brandMerchantModule.getMerchantType()))
                    .collect(Collectors.toMap(
                            BrandMerchantModule::getMerchantId,
                            merchant -> merchant
                    ));
            Set<String> merchantIdSet = brandMerchantModuleMap.keySet();
            if (CollectionUtils.isEmpty(merchantIdSet)){
                break;
            }
            List<MerchantInfo> merchantListByMerchantIds = merchantService.getMerchantListByMerchantIds(Lists.newArrayList(merchantIdSet));
            if (CollectionUtils.isEmpty(merchantListByMerchantIds)) {
                break;
            }
            Map<String, MerchantInfo> merchantInfoMap = merchantListByMerchantIds.stream().collect(Collectors.toMap(MerchantInfo::getId, merchant -> merchant));
            List<BankCardModule> defaultBankCardModules = bankCardDomainService.getDefaultBankCardModules(brandId, Lists.newArrayList(brandMerchantModuleMap.keySet()));
            Map<String, BankCardModule> bankCardModuleMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(defaultBankCardModules)) {
                bankCardModuleMap.putAll(defaultBankCardModules.stream().collect(Collectors.toMap(BankCardModule::getMerchantId, bankCard -> bankCard, (existing, replacement) -> replacement)));
            }
            initSeparateAccountInfo(brandId, brandMerchantModuleMap, merchantInfoMap, bankCardModuleMap);
            // 下一页
            currentPage++;
        }
    }

    /**
     * 初始化分离账户信息
     *
     * @param brandId                品牌ID
     * @param brandMerchantModuleMap 品牌商户Map
     * @param merchantInfoMap        商户信息Map
     */
    private void initSeparateAccountInfo(String brandId, Map<String, BrandMerchantModule> brandMerchantModuleMap,
                                        Map<String, MerchantInfo> merchantInfoMap, Map<String, BankCardModule> bankCardModuleMap) {
        brandMerchantModuleMap.forEach((merchantId, brandMerchantModule) -> {
            // 获取商户信息
            MerchantInfo merchantInfo = merchantInfoMap.get(merchantId);
            if (Objects.isNull(merchantInfo)) {
                return;
            }
            SeparateAccountDO separateAccountDO = new SeparateAccountDO();
            separateAccountDO.setBrandId(brandId);
            separateAccountDO.setAccountName(merchantInfo.getName());
            separateAccountDO.setAccountNumber(brandMerchantModule.getMerchantSn());
            separateAccountDO.setAccountType(SeparateAccountTypeEnum.getSeparateAccountTypeEnumByMerchantTypeEnum(MerchantTypeEnum.getEnumByMerchantType(brandMerchantModule.getMerchantType())).getCode());
            separateAccountDO.setType(brandMerchantModule.getType());
            separateAccountDO.setContractName(merchantInfo.getContact_name());
            separateAccountDO.setContractPhone(merchantInfo.getContact_cellphone());
            separateAccountDO.setSubAccountNo(brandMerchantModule.getSubAccountNo());
            separateAccountDO.setMemberId(brandMerchantModule.getMemberId());
            separateAccountDO.setAccountOpenStatus(brandMerchantModule.getAccountOpenStatus());
            separateAccountDO.setAccountOpenedTime(brandMerchantModule.getAccountOpenedTime());
            separateAccountDO.setAccountOpenFailureReason(brandMerchantModule.getAccountOpenFailureReason());
            separateAccountDO.setStrategyId(brandMerchantModule.getStrategyId());
            // 获取营业执照信息
            try {
                MerchantBusinessLicenseInfo merchantBusinessLicense =
                        merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
                if (Objects.nonNull(merchantBusinessLicense)) {
                    switch (merchantBusinessLicense.getType()) {
                        //个人
                        case 0:
                            separateAccountDO.setIdType("01");
                            separateAccountDO.setIdNumber(merchantBusinessLicense.getLegal_person_id_number());
                            break;
                        case 1:
                        case 2:
                            separateAccountDO.setIdType("99");
                            separateAccountDO.setIdNumber(merchantBusinessLicense.getNumber());
                            separateAccountDO.setLegalPersonName(merchantBusinessLicense.getLegal_person_name());
                            separateAccountDO.setLegalPersonIdType("01");
                            separateAccountDO.setLegalPersonId(merchantBusinessLicense.getLegal_person_id_number());
                            separateAccountDO.setLegalPersonPhone(merchantInfo.getContact_phone());
                            break;
                        default:
                    }
                }
                separateAccountDOService.addSeparateAccount(separateAccountDO);
                BankCardModule bankCardModule = bankCardModuleMap.get(merchantId);
                if (Objects.nonNull(bankCardModule)) {
                    BizBankAccountRes bankAccount = merchantBizBankAccountService.getBankAccountById(bankCardModule.getBankCardId());
                    if (Objects.nonNull(bankAccount)){
                        SeparateAccountSettlementCardDO separateAccountSettlementCardDO = new SeparateAccountSettlementCardDO();
                        separateAccountSettlementCardDO.setAccountNumber(separateAccountDO.getAccountNumber());
                        separateAccountSettlementCardDO.setHolder(bankAccount.getHolder());
                        separateAccountSettlementCardDO.setType(bankAccount.getType());
                        separateAccountSettlementCardDO.setCardNumber(bankAccount.getNumber());
                        separateAccountSettlementCardDO.setBankName(bankAccount.getBankName());
                        separateAccountSettlementCardDO.setBranchName(bankAccount.getBranchName());
                        separateAccountSettlementCardDO.setClearingNumber(bankAccount.getClearingNumber());
                        separateAccountSettlementCardDO.setOpeningNumber(bankAccount.getOpeningNumber());
                        separateAccountSettlementCardDO.setCellphone(bankCardModule.getMobile());
                        separateAccountSettlementCardDO.setDefaultStatus(Boolean.TRUE.equals(bankCardModule.getIsDefault()) ? 1 : 0);
                        separateAccountSettlementCardDO.setActiveStatus(bankCardModule.getStatus());
                        separateAccountSettlementCardDO.setActiveFailReason(bankCardModule.getActivateFailReason());
                        separateAccountSettlementCardDO.setCtime(bankCardModule.getCreatedTime());
                        separateAccountSettlementCardDO.setMtime(bankCardModule.getUpdatedTime());
                        separateAccountSettlementCardDO.setThirdBankCardId(bankCardModule.getThirdBankCardId());
                        separateAccountSettlementCardDOService.add(separateAccountSettlementCardDO);
                    }
                }
                List<SeparateAccountRelatedDO> separateAccountRelatedDOList = Lists.newArrayList();
                separateAccountRelatedDOList.add(
                        SeparateAccountRelatedDO.builder()
                                .brandId(brandId)
                                .accountNumber(separateAccountDO.getAccountNumber())
                                .type(SeparateAccountRelateTypeEnum.MERCHANT_SN.getType())
                                .relatedSn(merchantInfo.getSn())
                                .build()
                );
                if (StringUtils.isNotBlank(brandMerchantModule.getOutMerchantNo())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.OUT_MERCHANT.getType())
                            .relatedSn(brandMerchantModule.getOutMerchantNo())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getSqbStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.STORE_SN.getType())
                            .relatedSn(brandMerchantModule.getSqbStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedSqbStoreId())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.STORE_ID.getType())
                            .relatedSn(brandMerchantModule.getAssociatedSqbStoreId())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedElmStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.ELM_STORE.getType())
                            .relatedSn(brandMerchantModule.getAssociatedElmStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getAssociatedMeituanStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.MT_STORE.getType())
                            .relatedSn(brandMerchantModule.getAssociatedMeituanStoreSn())
                            .build()
                    );
                }
                if (StringUtils.isNotBlank(brandMerchantModule.getDyStoreSn())) {
                    separateAccountRelatedDOList.add(SeparateAccountRelatedDO.builder()
                            .brandId(brandId)
                            .accountNumber(separateAccountDO.getAccountNumber())
                            .type(SeparateAccountRelateTypeEnum.DY_STORE.getType())
                            .relatedSn(brandMerchantModule.getDyStoreSn())
                            .build()
                    );
                }
                separateAccountRelatedDOService.saveBatch(separateAccountRelatedDOList);
            } catch (Exception e) {
                log.error("initSeparateAccountInfo error {}", JSON.toJSONString(brandMerchantModule), e);
            }
        });
    }

    @XxlJob("initSeparateAccountInfo")
    @Transactional(rollbackFor = Exception.class)
    public void init() {
        int pageSize = 100;
        int currentPage = 1;

        while (true) {
            // 构建分页查询条件
            BrandConditionModule conditionModule = new BrandConditionModule();
            conditionModule.setPage(currentPage);
            conditionModule.setPageSize(pageSize);

            // 执行分页查询
            PageBrandModule pageBrandModule = brandDomainService.pageBrandModuleByBrandConditions(conditionModule);

            // 如果没有数据了,退出循环
            if (pageBrandModule == null || pageBrandModule.getBrandList() == null || pageBrandModule.getBrandList().isEmpty()) {
                break;
            }

            // 处理每个品牌
            pageBrandModule.getBrandList().stream()
                    .filter(brand -> brand.getFundManagementCompanyCode() != null)
                    .forEach(brand -> processMerchantData(brand.getBrandId()));

            // 下一页
            currentPage++;
        }
    }

}
