package com.wosai.cua.brand.business.service.domain.entity;

import com.wosai.cua.brand.business.service.annotations.SensitiveClass;
import com.wosai.cua.brand.business.service.annotations.SensitiveField;
import com.wosai.cua.brand.business.service.enums.SensitiveFieldEnum;
import lombok.Data;

@Data
@SensitiveClass
public class SeparateAccountCardQueryDO {
    /**
     * 账户编号
     */
    private String accountNumber;
    /**
     * 银行卡号
     */
    @SensitiveField(fieldType = SensitiveFieldEnum.DEFAULT)
    private String cardNumber;
}
