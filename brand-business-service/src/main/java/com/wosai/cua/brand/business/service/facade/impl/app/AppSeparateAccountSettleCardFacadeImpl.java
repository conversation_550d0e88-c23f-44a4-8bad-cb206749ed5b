package com.wosai.cua.brand.business.service.facade.impl.app;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.annotations.MaskClass;
import com.wosai.cua.brand.business.api.annotations.MaskMethod;
import com.wosai.cua.brand.business.api.dto.request.app.AppActiveSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppBaseSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppCreateSeparateAccountSettleCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteOrSetDefaultSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.response.BankInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppSeparateAccountSettleCardResponseDTO;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.api.facade.app.AppSeparateAccountSettleCardFacade;
import com.wosai.cua.brand.business.service.business.BrandBusiness;
import com.wosai.cua.brand.business.service.business.BrandMerchantBankAccountBusiness;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountBusinessV2;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountCardBusinessV2;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountModule;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountSettlementCardModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
@MaskClass
public class AppSeparateAccountSettleCardFacadeImpl implements AppSeparateAccountSettleCardFacade {

    private final SeparateAccountCardBusinessV2 separateAccountCardBusiness;

    private final SeparateAccountBusinessV2 separateAccountBusiness;

    private final BrandBusiness brandBusiness;

    private final BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness;

    @Autowired
    public AppSeparateAccountSettleCardFacadeImpl(SeparateAccountCardBusinessV2 separateAccountCardBusiness, SeparateAccountBusinessV2 separateAccountBusiness, BrandBusiness brandBusiness, BrandMerchantBankAccountBusiness brandMerchantBankAccountBusiness) {
        this.separateAccountCardBusiness = separateAccountCardBusiness;
        this.separateAccountBusiness = separateAccountBusiness;
        this.brandBusiness = brandBusiness;
        this.brandMerchantBankAccountBusiness = brandMerchantBankAccountBusiness;
    }


    @Override
    @MaskMethod
    public List<AppSeparateAccountSettleCardResponseDTO> getSeparateAccountSettleCardList(AppBaseSeparateAccountDTO request) {
        checkPermission(request.getTokenMerchantId(), request.getAccountNumber());
        List<SeparateAccountSettlementCardModule> separateAccountSettlementCardModuleList = separateAccountCardBusiness.getCardListModule(request.getAccountNumber());
        return SeparateAccountSettlementCardModule.convertToAppSeparateAccountSettleCardResponseDTOList(separateAccountSettlementCardModuleList);
    }

    @Override
    public boolean createSeparateAccountSettleCard(AppCreateSeparateAccountSettleCardDTO request) {
        String brandId = checkPermission(request.getTokenMerchantId(), request.getAccountNumber());
        if (Objects.nonNull(request.getType()) && request.getType() == 2 && StringUtils.isBlank(request.getOpeningNumber())){
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "对公卡开户行行号不能为空！");
        }
        if (Objects.nonNull(request.getType()) && request.getType() == 1){
            BankInfoResponseDTO bankInfoByCardNumber = brandMerchantBankAccountBusiness.getBankInfoByCardNumber(request.getBankCardNo());
            if (Objects.nonNull(bankInfoByCardNumber)) {
                request.setOpeningNumber(bankInfoByCardNumber.getCnaps());
                request.setBankName(bankInfoByCardNumber.getBank());
                request.setBranchName(bankInfoByCardNumber.getName());
            }
        }
        return separateAccountCardBusiness.createSettlementCard(SeparateAccountSettlementCardModule.convert(request), request.getAccountNumber(), brandId);
    }

    private String checkPermission(String tokenMerchantId, String accountNumber) {
        List<BrandSimpleInfoDTO> brandSimpleInfoList = brandBusiness.getBrandInfoListByMerchantId(tokenMerchantId, false);
        if (CollectionUtils.isEmpty(brandSimpleInfoList)) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_FIND_BRAND);
        }
        SeparateAccountModule accountInfoByAccountNumber = separateAccountBusiness.getSeparateAccountInfoByAccountNumber(brandSimpleInfoList.get(0).getBrandId(), accountNumber);
        if (accountInfoByAccountNumber == null) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.NOT_PERMISSION);
        }
        return brandSimpleInfoList.get(0).getBrandId();
    }

    @Override
    public boolean deleteSeparateAccountSettleCard(AppDeleteOrSetDefaultSeparateAccountCardDTO request) {
        if (!NumberUtils.isDigits(request.getId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "id不合法");
        }
        String brandId = checkPermission(request.getTokenMerchantId(), request.getAccountNumber());
        return separateAccountCardBusiness.deleteSettlementCard(brandId, Long.valueOf(request.getId()), request.getAccountNumber());
    }

    @Override
    public boolean setDefaultSeparateAccountSettleCard(AppDeleteOrSetDefaultSeparateAccountCardDTO request) {
        if (!NumberUtils.isDigits(request.getId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "id不合法");
        }
        String brandId = checkPermission(request.getTokenMerchantId(), request.getAccountNumber());
        return separateAccountCardBusiness.setDefaultSettlementCard(brandId, Long.valueOf(request.getId()), request.getAccountNumber());
    }

    @Override
    public boolean activateSeparateAccountSettleCard(AppActiveSeparateAccountCardDTO request) {
        if (!NumberUtils.isDigits(request.getId())) {
            throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "id不合法");
        }
        String brandId = checkPermission(request.getTokenMerchantId(), request.getAccountNumber());
        separateAccountCardBusiness.activateSettlementCard(brandId, Long.valueOf(request.getId()));
        return true;
    }
}
