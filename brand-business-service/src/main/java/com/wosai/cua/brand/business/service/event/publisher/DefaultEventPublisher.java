package com.wosai.cua.brand.business.service.event.publisher;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventPublisher;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.Listener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 主要用于注册与事件发表的监听调用处理
 */
@Component
@Slf4j
public class DefaultEventPublisher implements EventPublisher, InitializingBean {

    @Autowired
    private List<Listener> listeners;

    private final Map<EventType, Listener<Event>> listenerMap = new ConcurrentHashMap<>();

    private Executor taskExecutor;

    @Override
    public void publish(Event event) {
        if (event != null && listenerMap.containsKey(event.getEventType())) {
            final Listener<Event> listener = listenerMap.get(event.getEventType());
            if (Objects.nonNull(listener)) {
                log.info("start handle event:{}", JSON.toJSONString(event));
                taskExecutor = getTaskExecutor();
                if (Objects.nonNull(taskExecutor)) {
                    taskExecutor.execute(() -> listener.handle(event));
                } else {
                    listener.handle(event);
                }
            }
        }
    }

    @Override
    public void register(Listener listener) {
        if (listener != null && listener.getEventType() != null) {
            listenerMap.put(listener.getEventType(), listener);
        }
    }

    @Override
    public void register(List<Listener> listeners) {
        if (WosaiCollectionUtils.isNotEmpty(listeners)) {
            for (Listener listener : listeners) {
                register(listener);
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        register(listeners);
        taskExecutor = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.DiscardPolicy());
    }

    protected Executor getTaskExecutor() {
        if (Objects.nonNull(taskExecutor)) {
            return taskExecutor;
        } else {
            synchronized (DefaultEventPublisher.class) {
                if (Objects.nonNull(taskExecutor)) {
                    return taskExecutor;
                }
                taskExecutor = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.DiscardPolicy());
                return this.taskExecutor;
            }
        }
    }
}
