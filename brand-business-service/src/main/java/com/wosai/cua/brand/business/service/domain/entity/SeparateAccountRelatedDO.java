package com.wosai.cua.brand.business.service.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分账账户关联表
 * @TableName separate_account_related
 */
@TableName(value ="separate_account_related")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeparateAccountRelatedDO {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 分账账户
     */
    private String accountNumber;

    /**
     * 类型(MERCHANT-商户、STORE-门店、OUT_MERCHANT-外部商户)
     * @see com.wosai.cua.brand.business.api.enums.SeparateAccountRelateTypeEnum
     */
    private String type;

    /**
     * 关联编号(MERCHANT对应收钱吧商户号，STORE对应收钱吧门店编号，OUT_MERCHANT对应外部商户号)
     */
    private String relatedSn;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 删除状态(1-已删除，0-未删除)
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;
}