package com.wosai.cua.brand.business.service.event.model;

import com.wosai.cua.brand.business.service.event.Event;
import com.wosai.cua.brand.business.service.event.EventType;
import com.wosai.cua.brand.business.service.event.type.BrandMerchantStatusChangeEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMerchantStatusChangeEvent implements Event {

    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 主商户 ID
     */
    private String mainMerchantId;
    /**
     * 商户 ID
     */
    private List<String> merchantIds;
    /**
     * 状态 1绑定 0解绑
     */
    private Integer status;
    /**
     * 审批编号，如果从审批过来做的绑定或者解绑会有这个参数
     */
    private String auditSn;
    /**
     * 是否删除品牌导致的解绑 true是  false不是
     */
    private boolean deleteBrand;
    @Override
    public EventType getEventType() {
        return BrandMerchantStatusChangeEventType.STATUS_CHANGE;
    }
}
