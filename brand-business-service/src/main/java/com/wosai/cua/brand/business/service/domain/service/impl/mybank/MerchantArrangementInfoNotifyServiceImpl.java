package com.wosai.cua.brand.business.service.domain.service.impl.mybank;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.service.MyBankNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml.XmlConverter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantArrangementInfoNotifyRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.MerchantArrangementInfoNotifyModel;
import com.wosai.cua.brand.business.service.helper.RedisLockHelper;
import com.wosai.cua.brand.business.service.module.brand.extra.BrandMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.brand.extra.mybank.MyBankMerchantExtraModule;
import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 处理
 */
@Service(value = "merchantprodMerchantArrangementInfoNotifyService")
@Slf4j
public class MerchantArrangementInfoNotifyServiceImpl implements MyBankNotifyService {


    private final CommonRequestHandle commonRequestHandle;

    private final BrandMerchantMapper brandMerchantMapper;

    private final RedisLockHelper redisLockHelper;

    private final SeparateAccountDOMapper separateAccountMapper;

    @Autowired
    public MerchantArrangementInfoNotifyServiceImpl(CommonRequestHandle commonRequestHandle, BrandMerchantMapper brandMerchantMapper, RedisLockHelper redisLockHelper, SeparateAccountDOMapper separateAccountMapper) {
        this.commonRequestHandle = commonRequestHandle;
        this.brandMerchantMapper = brandMerchantMapper;
        this.redisLockHelper = redisLockHelper;
        this.separateAccountMapper = separateAccountMapper;
    }


    @Override
    public String notifyHandle(String context, String function) throws Exception {

        //通知内容转换
        MerchantArrangementInfoNotifyRequest arrangeMentInfoNotify =
                XmlConverter.getInstance().toResponse(context, MerchantArrangementInfoNotifyRequest.class);
        RequestHead head = arrangeMentInfoNotify.getMerchantprodMerchantArrangementInfoNotify().getRequestHead();
        //通知内容保存
        boolean flag = saveNotify(arrangeMentInfoNotify.getMerchantprodMerchantArrangementInfoNotify().getMerchantprodMerchantArrangementInfoNotifyModel());
        //响应结果根据执行结果统一处理
        return commonRequestHandle.getSignResult(flag, head);


    }

    @Override
    public void notifyHandle(BrandCallbackRecordModule baseNotifyModule) {

    }

    private boolean saveNotify(MerchantArrangementInfoNotifyModel merchantArrangementInfoNotifyModel) {
        String lockKey = String.format(RedisLockHelper.MY_BANK_NOTIFY_MERCHANT_LOCK_KEY, merchantArrangementInfoNotifyModel.getMerchantId());
        String lockValue = merchantArrangementInfoNotifyModel.getMerchantId();

        // 尝试获取锁，增加超时时间以避免死锁
        if (!redisLockHelper.tryLock(lockKey, lockValue, 10)) { // 超时时间为10秒
            log.error("Failed to acquire lock for MerchantArrangementInfoNotifyModel: {}", JSON.toJSONString(merchantArrangementInfoNotifyModel));
            return true; // 无法获取锁则直接返回失败
        }

        try {
            this.separateAccount(merchantArrangementInfoNotifyModel);
            return brandMerchant(merchantArrangementInfoNotifyModel);
        } finally {
            redisLockHelper.unlock(lockKey, lockValue); // 确保锁被释放
        }
    }

    private boolean brandMerchant(MerchantArrangementInfoNotifyModel merchantArrangementInfoNotifyModel) {
        LambdaQueryWrapper<BrandMerchantDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BrandMerchantDO::getMemberId, merchantArrangementInfoNotifyModel.getMerchantId());
        BrandMerchantDO brandMerchant = brandMerchantMapper.selectOne(queryWrapper);

        if (brandMerchant == null) {
            log.error("Failed to update MerchantArrangementInfoNotifyModel: {}", JSON.toJSONString(merchantArrangementInfoNotifyModel));
            return true;
        }

        // 防止空指针异常
        brandMerchant.setVersion(brandMerchant.getVersion() + 1);

        // 更新额外信息
        if (StringUtils.isBlank(brandMerchant.getExtra())) {
            brandMerchant.setExtra(
                    JSON.toJSONString(
                            BrandMerchantExtraModule.builder()
                                    .myBankMerchantExtraModule(
                                            MyBankMerchantExtraModule.builder()
                                                    .arrangementNo(merchantArrangementInfoNotifyModel.getArrangementNo())
                                                    .arrangementStatus(merchantArrangementInfoNotifyModel.getArrangementStatus())
                                                    .arrangementType(merchantArrangementInfoNotifyModel.getArrangementType())
                                                    .build()
                                    )
                                    .build()
                    )
            );
        } else {
            BrandMerchantExtraModule brandMerchantExtraModule = JSON.parseObject(brandMerchant.getExtra(), BrandMerchantExtraModule.class);
            brandMerchantExtraModule.setMyBankMerchantExtraModule(
                    MyBankMerchantExtraModule.builder()
                            .arrangementNo(merchantArrangementInfoNotifyModel.getArrangementNo())
                            .arrangementStatus(merchantArrangementInfoNotifyModel.getArrangementStatus())
                            .arrangementType(merchantArrangementInfoNotifyModel.getArrangementType())
                            .build()
            );
            brandMerchant.setExtra(JSON.toJSONString(brandMerchantExtraModule));
        }

        // 使用乐观锁或其他并发控制手段
        brandMerchantMapper.updateById(brandMerchant);
        return true;
    }

    private void separateAccount(MerchantArrangementInfoNotifyModel merchantArrangementInfoNotifyModel) {
        LambdaQueryWrapper<SeparateAccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SeparateAccountDO::getSubAccountNo, merchantArrangementInfoNotifyModel.getMerchantId());
        queryWrapper.eq(SeparateAccountDO::getDeleted, 0);
        List<SeparateAccountDO> separateAccountList = separateAccountMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(separateAccountList)) {
            log.error("Failed to update MerchantArrangementInfoNotifyModel: {}", JSON.toJSONString(merchantArrangementInfoNotifyModel));
            return;
        }
        separateAccountList.forEach(separateAccountDO -> {
            // 更新额外信息
            if (StringUtils.isBlank(separateAccountDO.getExt())) {
                separateAccountDO.setExt(
                        JSON.toJSONString(
                                BrandMerchantExtraModule.builder()
                                        .myBankMerchantExtraModule(
                                                MyBankMerchantExtraModule.builder()
                                                        .arrangementNo(merchantArrangementInfoNotifyModel.getArrangementNo())
                                                        .arrangementStatus(merchantArrangementInfoNotifyModel.getArrangementStatus())
                                                        .arrangementType(merchantArrangementInfoNotifyModel.getArrangementType())
                                                        .build()
                                        )
                                        .build()
                        )
                );
            } else {
                BrandMerchantExtraModule brandMerchantExtraModule = JSON.parseObject(separateAccountDO.getExt(), BrandMerchantExtraModule.class);
                brandMerchantExtraModule.setMyBankMerchantExtraModule(
                        MyBankMerchantExtraModule.builder()
                                .arrangementNo(merchantArrangementInfoNotifyModel.getArrangementNo())
                                .arrangementStatus(merchantArrangementInfoNotifyModel.getArrangementStatus())
                                .arrangementType(merchantArrangementInfoNotifyModel.getArrangementType())
                                .build()
                );
                separateAccountDO.setExt(JSON.toJSONString(brandMerchantExtraModule));
            }

            // 使用乐观锁或其他并发控制手段
            separateAccountMapper.updateById(separateAccountDO);
        });
    }

}
