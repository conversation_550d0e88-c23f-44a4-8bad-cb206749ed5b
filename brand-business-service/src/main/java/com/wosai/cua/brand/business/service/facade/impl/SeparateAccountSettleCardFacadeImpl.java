package com.wosai.cua.brand.business.service.facade.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.cua.brand.business.api.dto.request.BaseSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.response.SeparateAccountSettleCardResponseDTO;
import com.wosai.cua.brand.business.api.facade.SeparateAccountSettleCardFacade;
import com.wosai.cua.brand.business.service.business.v2.SeparateAccountCardBusinessV2;
import com.wosai.cua.brand.business.service.module.separate.account.SeparateAccountSettlementCardModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class SeparateAccountSettleCardFacadeImpl implements SeparateAccountSettleCardFacade {

    private final SeparateAccountCardBusinessV2 separateAccountCardBusinessV2;

    public SeparateAccountSettleCardFacadeImpl(SeparateAccountCardBusinessV2 separateAccountCardBusinessV2) {
        this.separateAccountCardBusinessV2 = separateAccountCardBusinessV2;
    }

    @Override
    public SeparateAccountSettleCardResponseDTO getDefaultSeparateAccountSettleCard(BaseSeparateAccountDTO request) {
        SeparateAccountSettlementCardModule defaultCardModule = separateAccountCardBusinessV2.getDefaultCardModule(request.getAccountNumber());
        return SeparateAccountSettlementCardModule.convertToSeparateAccountSettleCardResponseDTO(defaultCardModule);
    }
}
