package com.wosai.cua.brand.business.service.domain.service.impl.mybank;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.service.domain.dao.BrandConfigDOMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantControlRecordsMapper;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandConfigDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantControlRecordsDO;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.https.enums.ControlNotifyTypeEnum;
import com.wosai.cua.brand.business.service.domain.service.MyBankNotifyService;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.exception.MybankApiException;
import com.wosai.cua.brand.business.service.domain.tripartite.mybank.parse.xml.XmlConverter;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.RequestHead;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.merchant.MerchantControlNotifyRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.mybank.model.notify.MerchantControlNotifyModel;
import com.wosai.cua.brand.business.service.event.model.MerchantOpenStatusEvent;
import com.wosai.cua.brand.business.service.event.publisher.DefaultEventPublisher;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.merchant.BrandCallbackRecordModule;
import com.wosai.cua.brand.business.service.mybank.manage.CommonRequestHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service(value = "merchantControlNotifyService")
@Slf4j
public class MerchantControlNotifyServiceImpl extends BaseNotifyServiceImpl implements MyBankNotifyService {

    @Autowired
    public MerchantControlNotifyServiceImpl(CommonRequestHandle commonRequestHandle, BrandMerchantMapper brandMerchantMapper, BrandMerchantControlRecordsMapper brandMerchantControlRecordsMapper, BrandConfigDOMapper brandConfigMapper, DefaultEventPublisher defaultEventPublisher, BrandMapper brandMapper, SeparateAccountDOMapper separateAccountDOMapper) {
        this.commonRequestHandle = commonRequestHandle;
        this.brandMerchantMapper = brandMerchantMapper;
        this.brandMerchantControlRecordsMapper = brandMerchantControlRecordsMapper;
        this.brandConfigMapper = brandConfigMapper;
        this.defaultEventPublisher = defaultEventPublisher;
        this.brandMapper = brandMapper;
        this.separateAccountDOMapper = separateAccountDOMapper;
    }

    @Override
    public String notifyHandle(String context, String function) throws Exception {
        //通知内容转换
        MerchantControlNotifyRequest merchantControlNotifyRequest =
                XmlConverter.getInstance().toResponse(context, MerchantControlNotifyRequest.class);
        RequestHead head = merchantControlNotifyRequest.getMerchantControlNotify().getRequestHead();
        // 查询品牌根据appid
        String appId = commonRequestHandle.getNotifyAppId(context);
        LambdaQueryWrapper<BrandConfigDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BrandConfigDO::getChannelId, appId);
        wrapper.eq(BrandConfigDO::getChannelType, FundManagementCompanyEnum.MY_BANK.getFundManagementCompanyCode());
        BrandConfigDO brandConfigDO = brandConfigMapper.selectOne(wrapper);
        if (brandConfigDO == null) {
            throw new MybankApiException("未找到品牌信息");
        }
        boolean flag = saveNotify(brandConfigDO.getBrandId(), merchantControlNotifyRequest.getMerchantControlNotify().getMerchantControlNotifyModel());
        //响应结果根据执行结果统一处理
        return commonRequestHandle.getSignResult(flag, head);
    }

    @Override
    public void notifyHandle(BrandCallbackRecordModule baseNotifyModule) {

    }

    private boolean saveNotify(String brandId, MerchantControlNotifyModel merchantControlNotifyModel) {
        List<BrandMerchantControlRecordsDO> brandMerchantControlRecords = getBrandMerchantControlRecordsByOutTradeNo(merchantControlNotifyModel);
        if (CollectionUtils.isNotEmpty(brandMerchantControlRecords)) {
            return true;
        }
        ControlNotifyTypeEnum controlNotifyTypeEnum = ControlNotifyTypeEnum.getControlNotifyTypeEnum(merchantControlNotifyModel.getControlNotifyType());
        if (Objects.isNull(controlNotifyTypeEnum)) {
            log.error("管控通知类型错误。controlNotifyType = {}", merchantControlNotifyModel.getControlNotifyType());
            return false;
        }
        if (controlNotifyTypeEnum == ControlNotifyTypeEnum.RELEASE_CONTROL) {
            this.releaseControl(merchantControlNotifyModel, brandId);
            this.separateReleaseControl(merchantControlNotifyModel, brandId);
        } else {
            this.control(merchantControlNotifyModel, brandId);
            this.separateControl(merchantControlNotifyModel, brandId);
        }
        return true;
    }

    private List<BrandMerchantControlRecordsDO> getBrandMerchantControlRecordsByOutTradeNo(MerchantControlNotifyModel merchantControlNotifyModel) {
        LambdaQueryWrapper<BrandMerchantControlRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BrandMerchantControlRecordsDO::getOutTradeNo, merchantControlNotifyModel.getOutTradeNo());
        wrapper.eq(BrandMerchantControlRecordsDO::getDeleted, 0);
        return brandMerchantControlRecordsMapper.selectList(wrapper);
    }

    private void releaseControl(MerchantControlNotifyModel merchantControlNotifyModel, String brandId) {
        List<String> merchantIds = Lists.newArrayList(merchantControlNotifyModel.getMerchantIds().split(","));
        List<String> merchantSnList = Lists.newArrayList(merchantControlNotifyModel.getOutMerchantIds().split(","));
        LambdaQueryWrapper<BrandMerchantControlRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BrandMerchantControlRecordsDO::getBrandId, brandId);
        wrapper.in(BrandMerchantControlRecordsDO::getMerchantSn, merchantSnList);
        wrapper.in(BrandMerchantControlRecordsDO::getSubAccountNo, merchantIds);
        wrapper.eq(BrandMerchantControlRecordsDO::getDeleted, 0);
        List<BrandMerchantControlRecordsDO> brandMerchantControlRecords = brandMerchantControlRecordsMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(brandMerchantControlRecords)) {
            brandMerchantControlRecords.forEach(brandMerchantControlRecordsDO -> {
                LambdaQueryWrapper<BrandMerchantDO> brandMerchantLambdaQueryWrapper = new LambdaQueryWrapper<>();
                brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getBrandId, brandId);
                brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getMerchantSn, brandMerchantControlRecordsDO.getMerchantSn());
                brandMerchantLambdaQueryWrapper.eq(BrandMerchantDO::getSubAccountNo, brandMerchantControlRecordsDO.getSubAccountNo());
                BrandMerchantDO brandMerchantDO = brandMerchantMapper.selectOne(brandMerchantLambdaQueryWrapper);
                if (Objects.nonNull(brandMerchantDO)) {
                    brandMerchantDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                    brandMerchantDO.setAccountOpenFailureReason("");
                    brandMerchantDO.setUpdatedTime(new Date());
                    brandMerchantMapper.updateById(brandMerchantDO);
                }
                brandMerchantControlRecordsDO.setDeleted(1);
                brandMerchantControlRecordsMapper.updateById(brandMerchantControlRecordsDO);
            });
        }
    }

    private void separateReleaseControl(MerchantControlNotifyModel merchantControlNotifyModel, String brandId) {
        List<String> subAccountNoList = Lists.newArrayList(merchantControlNotifyModel.getMerchantIds().split(","));
        List<String> accountNumberList = Lists.newArrayList(merchantControlNotifyModel.getOutMerchantIds().split(","));
        LambdaQueryWrapper<BrandMerchantControlRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BrandMerchantControlRecordsDO::getBrandId, brandId);
        wrapper.in(BrandMerchantControlRecordsDO::getMerchantSn, accountNumberList);
        wrapper.in(BrandMerchantControlRecordsDO::getSubAccountNo, subAccountNoList);
        wrapper.eq(BrandMerchantControlRecordsDO::getDeleted, 0);
        List<BrandMerchantControlRecordsDO> brandMerchantControlRecords = brandMerchantControlRecordsMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(brandMerchantControlRecords)) {
            brandMerchantControlRecords.forEach(brandMerchantControlRecordsDO -> {
                LambdaQueryWrapper<SeparateAccountDO> separateAccountDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                separateAccountDOLambdaQueryWrapper.eq(SeparateAccountDO::getBrandId, brandId);
                separateAccountDOLambdaQueryWrapper.eq(SeparateAccountDO::getAccountNumber, brandMerchantControlRecordsDO.getMerchantSn());
                separateAccountDOLambdaQueryWrapper.eq(SeparateAccountDO::getDeleted, 0);
                SeparateAccountDO accountDO = separateAccountDOMapper.selectOne(separateAccountDOLambdaQueryWrapper);
                if (Objects.nonNull(accountDO)) {
                    accountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.OPENED.getStatus());
                    accountDO.setAccountOpenFailureReason("");
                    accountDO.setMtime(new Date());
                    separateAccountDOMapper.updateById(accountDO);
                    super.updateUnionAccount(separateAccountDOLambdaQueryWrapper, accountDO);
                }
                brandMerchantControlRecordsDO.setDeleted(1);
                brandMerchantControlRecordsMapper.updateById(brandMerchantControlRecordsDO);
            });
        }
    }

    private void separateControl(MerchantControlNotifyModel merchantControlNotifyModel, String brandId){
        LambdaQueryWrapper<BrandDO> brandLambdaQueryWrapper = new LambdaQueryWrapper<>();
        brandLambdaQueryWrapper.eq(BrandDO::getBrandId, brandId);
        brandLambdaQueryWrapper.eq(BrandDO::getDeleted, 0);
        BrandDO brandDO = brandMapper.selectOne(brandLambdaQueryWrapper);
        if (Objects.isNull(brandDO)) {
            log.error("品牌不存在。brandId = {}", brandId);
            return;
        }
        List<String> subAccountNos = Lists.newArrayList(merchantControlNotifyModel.getMerchantIds().split(","));
        List<String> accountNumbers = Lists.newArrayList(merchantControlNotifyModel.getOutMerchantIds().split(","));
        LambdaQueryWrapper<SeparateAccountDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SeparateAccountDO::getBrandId, brandId);
        wrapper.in(SeparateAccountDO::getAccountNumber, accountNumbers);
        wrapper.in(SeparateAccountDO::getSubAccountNo, subAccountNos);
        wrapper.eq(SeparateAccountDO::getDeleted, 0);
        List<SeparateAccountDO> separateAccountDOList = separateAccountDOMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(separateAccountDOList)) {
            separateAccountDOList.forEach(separateAccountDO -> {
                BrandMerchantControlRecordsDO brandMerchantControlRecordsDO = new BrandMerchantControlRecordsDO();
                brandMerchantControlRecordsDO.setBrandId(brandId);
                brandMerchantControlRecordsDO.setChangeReason(this.getChangeReason(merchantControlNotifyModel.getChangeReason()));
                brandMerchantControlRecordsDO.setControlCategory(MerchantControlNotifyModel.ControlCategoryEnum.getName(merchantControlNotifyModel.getControlCategory()));
                brandMerchantControlRecordsDO.setControlNotifyType(ControlNotifyTypeEnum.getName(merchantControlNotifyModel.getControlNotifyType()));
                brandMerchantControlRecordsDO.setControlOrderNo(merchantControlNotifyModel.getControlOrderNo());
                brandMerchantControlRecordsDO.setControlStrategy(MerchantControlNotifyModel.ControlStrategyEnum.getName(merchantControlNotifyModel.getControlStrategy()));
                brandMerchantControlRecordsDO.setEffectiveTime(TimeConverterHelper.dateToTimestamp(merchantControlNotifyModel.getEffectiveTime(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT));
                brandMerchantControlRecordsDO.setMerchantSn(separateAccountDO.getAccountNumber());
                brandMerchantControlRecordsDO.setOutTradeNo(merchantControlNotifyModel.getOutTradeNo());
                brandMerchantControlRecordsDO.setSubAccountNo(separateAccountDO.getSubAccountNo());
                brandMerchantControlRecordsDO.setUpgradeUrl(merchantControlNotifyModel.getUpgradeUrl());
                brandMerchantControlRecordsDO.setPunishDate(TimeConverterHelper.dateToTimestamp(merchantControlNotifyModel.getPunishDate(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT));
                brandMerchantControlRecordsDO.setCanUpgrade(MerchantControlNotifyModel.CanUpgradeEnum.getOrdinal(merchantControlNotifyModel.getCanUpgrade()));
                brandMerchantControlRecordsMapper.insert(brandMerchantControlRecordsDO);
                separateAccountDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_CONTROLLED.getStatus());
                separateAccountDO.setAccountOpenFailureReason(this.getChangeReason(merchantControlNotifyModel.getChangeReason()));
                separateAccountDO.setMtime(new Date());
                separateAccountDOMapper.updateById(separateAccountDO);
                super.updateUnionAccount(new LambdaQueryWrapper<>(), separateAccountDO);
            });
        }
    }

    private void control(MerchantControlNotifyModel merchantControlNotifyModel, String brandId){
        LambdaQueryWrapper<BrandDO> brandLambdaQueryWrapper = new LambdaQueryWrapper<>();
        brandLambdaQueryWrapper.eq(BrandDO::getBrandId, brandId);
        brandLambdaQueryWrapper.eq(BrandDO::getDeleted, 0);
        BrandDO brandDO = brandMapper.selectOne(brandLambdaQueryWrapper);
        if (Objects.isNull(brandDO)) {
            log.error("品牌不存在。brandId = {}", brandId);
            return;
        }
        List<String> merchantIds = Lists.newArrayList(merchantControlNotifyModel.getMerchantIds().split(","));
        List<String> merchantSnList = Lists.newArrayList(merchantControlNotifyModel.getOutMerchantIds().split(","));
        LambdaQueryWrapper<BrandMerchantDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BrandMerchantDO::getBrandId, brandId);
        wrapper.in(BrandMerchantDO::getMerchantSn, merchantSnList);
        wrapper.in(BrandMerchantDO::getSubAccountNo, merchantIds);
        wrapper.eq(BrandMerchantDO::getDeleted, 0);
        List<BrandMerchantDO> brandMerchantList = brandMerchantMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(brandMerchantList)) {
            brandMerchantList.forEach(brandMerchantDO -> {
                BrandMerchantControlRecordsDO brandMerchantControlRecordsDO = new BrandMerchantControlRecordsDO();
                brandMerchantControlRecordsDO.setBrandId(brandId);
                brandMerchantControlRecordsDO.setChangeReason(this.getChangeReason(merchantControlNotifyModel.getChangeReason()));
                brandMerchantControlRecordsDO.setControlCategory(MerchantControlNotifyModel.ControlCategoryEnum.getName(merchantControlNotifyModel.getControlCategory()));
                brandMerchantControlRecordsDO.setControlNotifyType(ControlNotifyTypeEnum.getName(merchantControlNotifyModel.getControlNotifyType()));
                brandMerchantControlRecordsDO.setControlOrderNo(merchantControlNotifyModel.getControlOrderNo());
                brandMerchantControlRecordsDO.setControlStrategy(MerchantControlNotifyModel.ControlStrategyEnum.getName(merchantControlNotifyModel.getControlStrategy()));
                brandMerchantControlRecordsDO.setEffectiveTime(TimeConverterHelper.dateToTimestamp(merchantControlNotifyModel.getEffectiveTime(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT));
                brandMerchantControlRecordsDO.setMerchantSn(brandMerchantDO.getMerchantSn());
                brandMerchantControlRecordsDO.setOutTradeNo(merchantControlNotifyModel.getOutTradeNo());
                brandMerchantControlRecordsDO.setSubAccountNo(brandMerchantDO.getSubAccountNo());
                brandMerchantControlRecordsDO.setUpgradeUrl(merchantControlNotifyModel.getUpgradeUrl());
                brandMerchantControlRecordsDO.setPunishDate(TimeConverterHelper.dateToTimestamp(merchantControlNotifyModel.getPunishDate(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT));
                brandMerchantControlRecordsDO.setCanUpgrade(MerchantControlNotifyModel.CanUpgradeEnum.getOrdinal(merchantControlNotifyModel.getCanUpgrade()));
                brandMerchantControlRecordsMapper.insert(brandMerchantControlRecordsDO);
                brandMerchantDO.setAccountOpenStatus(BrandMerchantAccountOpenStatusEnum.TO_BE_CONTROLLED.getStatus());
                brandMerchantDO.setAccountOpenFailureReason(this.getChangeReason(merchantControlNotifyModel.getChangeReason()));
                brandMerchantDO.setUpdatedTime(new Date());
                brandMerchantMapper.updateById(brandMerchantDO);
                defaultEventPublisher.publish(MerchantOpenStatusEvent.builder().merchantId(brandMerchantDO.getMerchantId()).params(MerchantOpenStatusEvent.getEventParams(brandDO, brandMerchantDO)).build());
            });
        }
    }

    private String getChangeReason(String changeReason){
        if (StringUtils.isBlank(changeReason)) {
            return "";
        }
        List<String> reasons = Lists.newArrayList(changeReason.split("\\|\\|"));
        StringBuilder sb = new StringBuilder();
        reasons.forEach(reason -> {
            if (StringUtils.isNotBlank(reason)) {
                String desc = MerchantControlNotifyModel.ChangeReasonEnum.getDesc(reason);
                if (StringUtils.isNotBlank(desc)) {
                    sb.append(desc).append("\\|");
                }
            }
        });
        return sb.toString();
    }
}
