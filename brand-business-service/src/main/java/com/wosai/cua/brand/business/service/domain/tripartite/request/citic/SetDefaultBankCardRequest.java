package com.wosai.cua.brand.business.service.domain.tripartite.request.citic;

import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO;
import com.wosai.cua.brand.business.service.domain.tripartite.request.TripartiteSystemCallRequest;
import com.wosai.cua.brand.business.service.domain.tripartite.request.enums.FunctionEnum;
import com.wosai.cua.brand.business.service.helper.TimeConverterHelper;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.config.citic.CiticBankConfigModule;
import com.wosai.upay.bank.model.bizbankaccount.BizBankAccountRes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@XmlRootElement(name = "ROOT")
@XmlAccessorType(XmlAccessType.FIELD)
public class SetDefaultBankCardRequest extends BaseCiticRequest implements TripartiteSystemCallRequest {

    /**
     * 用户编号
     * 是否必填：是
     * 银行给用户分配的编号，银行保证唯一性； 后续交易均使用该编号唯一定位用户。
     */
    @XmlElement(name = "USER_ID")
    private String userId;

    @XmlElement(name = "SETTLE_ACCT")
    private String settleAcct;

    @XmlElement(name = "SETTLE_ACCT_NM")
    private String settleAcctNm;

    @Override
    public FunctionEnum getFunctionEnum() {
        return FunctionEnum.CITIC_SET_DEFAULT_BANK_CARD;
    }

    public SetDefaultBankCardRequest() {
        super();
        super.setTransCode("********");
    }

    public static SetDefaultBankCardRequest builder(CiticBankConfigModule configModule, BrandMerchantModule brandMerchantModule, BizBankAccountRes bankAccount){
        SetDefaultBankCardRequest request = new SetDefaultBankCardRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(brandMerchantModule.getMemberId());
        request.setSettleAcct(bankAccount.getNumber());
        request.setSettleAcctNm(bankAccount.getHolder());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + brandMerchantModule.getMerchantSn().substring(brandMerchantModule.getMerchantSn().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        return request;
    }

    public static SetDefaultBankCardRequest builder(CiticBankConfigModule configModule, SeparateAccountDO separateAccountDO, SeparateAccountSettlementCardDO card){
        SetDefaultBankCardRequest request = new SetDefaultBankCardRequest();
        request.setMerchantId(configModule.getMerchantId());
        request.setUserId(separateAccountDO.getSubAccountNo());
        request.setSettleAcct(card.getCardNumber());
        request.setSettleAcctNm(card.getHolder());
        request.setReqSsn(configModule.getMerchantId() + TimeConverterHelper.dateFormat(new Date(), TimeConverterHelper.UNSIGNED_ABBREVIATION_FORMAT_2) + separateAccountDO.getAccountNumber().substring(separateAccountDO.getAccountNumber().length() - 8));
        request.setLaasSsn(UUID.randomUUID().toString().replace("-", ""));
        return request;
    }
}
