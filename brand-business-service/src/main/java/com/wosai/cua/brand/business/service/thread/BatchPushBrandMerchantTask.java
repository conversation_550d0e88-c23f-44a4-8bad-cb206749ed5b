package com.wosai.cua.brand.business.service.thread;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.cua.brand.business.service.domain.dao.BrandMerchantMapper;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.helper.MerchantBrandPushHelper;
import com.wosai.cua.brand.business.service.helper.ThreadPoolHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量上送商户的品牌信息
 */
@Service
@Slf4j
public class BatchPushBrandMerchantTask {

    @Autowired
    private MerchantBrandPushHelper merchantBrandPushHelper;

    @Autowired
    private BrandMerchantMapper brandMerchantMapper;

    public void batchPush() {
        long lastId = 0L;
        int batchSize = 500;
        while (true) {
            List<BrandMerchantDO> rows = brandMerchantMapper.pageBrandMerchantById(lastId, batchSize);
            if (CollectionUtils.isEmpty(rows)) {
                return;
            }
            List<String> merchantIds = rows.stream().map(BrandMerchantDO::getMerchantId).distinct().collect(Collectors.toList());
            long batchStartId = rows.get(0).getId();

            try {
                Future<?> future = ThreadPoolHelper.submit(() -> processBatch(merchantIds));
                future.get(20, TimeUnit.SECONDS); // 阻塞等待任务完成，异常会抛出
                log.info("批次[起始id={}, size={}]处理成功", batchStartId, rows.size());
            } catch (Exception e) {
                log.error("批次处理失败，起始id：{}", batchStartId, e);
                break;
            }
            lastId = rows.get(rows.size() - 1).getId();
        }
    }

    public void processBatch(List<String> merchantIds) {
        try {
            merchantBrandPushHelper.pushBrandMerchant(merchantIds);
        } catch (Exception e) {
            log.error("批量上送失败，exception:{}", e.getMessage(), e);
            throw new CommonInvalidParameterException("品牌商户批量上送火山失败");
        }
    }

}
