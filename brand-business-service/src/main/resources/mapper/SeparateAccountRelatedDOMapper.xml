<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.SeparateAccountRelatedDOMapper">

    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.SeparateAccountRelatedDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="relatedSn" column="related_sn" jdbcType="VARCHAR"/>
        <result property="ext" column="ext" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_number,type,
        related_sn,ext,deleted,ctime,
        mtime
    </sql>
</mapper>
