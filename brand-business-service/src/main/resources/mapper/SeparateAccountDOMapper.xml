<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.SeparateAccountDOMapper">
    <resultMap id="BaseResultMap" type="com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="brandId" column="brand_id" jdbcType="VARCHAR"/>
        <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="idType" column="id_type" jdbcType="VARCHAR"/>
        <result property="idNumber" column="id_number" jdbcType="VARCHAR"/>
        <result property="legalPersonName" column="legal_person_name" jdbcType="VARCHAR"/>
        <result property="legalPersonIdType" column="legal_person_id_type" jdbcType="VARCHAR"/>
        <result property="legalPersonId" column="legal_person_id" jdbcType="VARCHAR"/>
        <result property="legalPersonPhone" column="legal_person_phone" jdbcType="VARCHAR"/>
        <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
        <result property="contractPhone" column="contract_phone" jdbcType="VARCHAR"/>
        <result property="contractIdType" column="contract_id_type" jdbcType="VARCHAR"/>
        <result property="contractId" column="contract_id" jdbcType="VARCHAR"/>
        <result property="accountOpenStatus" column="account_open_status" jdbcType="VARCHAR"/>
        <result property="accountOpenFailureReason" column="account_open_failure_reason" jdbcType="VARCHAR"/>
        <result property="accountOpenedTime" column="account_opened_time" jdbcType="TIMESTAMP"/>
        <result property="settleCardStatus" column="settle_card_status" jdbcType="VARCHAR"/>
        <result property="ext" column="ext" jdbcType="VARCHAR"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        brand_id,
        account_number,
        account_name,
        type,
        id_type,
        id_number,
        legal_person_name,
        legal_person_id_type,
        legal_person_id,
        legal_person_phone,
        contract_name,
        contract_phone,
        contract_id_type,
        contract_id,
        account_open_status,
        account_open_failure_reason,
        account_opened_time,
        settle_card_status,
        ext,
        strategy_id,
        deleted,
        ctime,
        mtime
    </sql>
</mapper>
