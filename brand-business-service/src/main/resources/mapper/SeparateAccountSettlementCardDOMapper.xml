<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.cua.brand.business.service.domain.dao.SeparateAccountSettlementCardDOMapper">
    <resultMap id="BaseResultMap"
               type="com.wosai.cua.brand.business.service.domain.entity.SeparateAccountSettlementCardDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="holder" column="holder" jdbcType="VARCHAR"/>
        <result property="idType" column="id_type" jdbcType="TINYINT"/>
        <result property="identity" column="identity" jdbcType="VARCHAR"/>
        <result property="cardNumber" column="card_number" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="branchName" column="branch_name" jdbcType="VARCHAR"/>
        <result property="clearingNumber" column="clearing_number" jdbcType="VARCHAR"/>
        <result property="openingNumber" column="opening_number" jdbcType="VARCHAR"/>
        <result property="thirdBankCardId" column="third_bank_card_id" jdbcType="VARCHAR"/>
        <result property="cellphone" column="cellphone" jdbcType="VARCHAR"/>
        <result property="defaultStatus" column="default_status" jdbcType="INTEGER"/>
        <result property="activeStatus" column="active_status" jdbcType="INTEGER"/>
        <result property="activationTime" column="activation_time" jdbcType="TIMESTAMP"/>
        <result property="activeFailReason" column="active_fail_reason" jdbcType="VARCHAR"/>
        <result property="ext" column="ext" jdbcType="VARCHAR"/>
        <result property="ctime" column="ctime" jdbcType="TIMESTAMP"/>
        <result property="mtime" column="mtime" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        account_number,
        type,
        holder,
        id_type,
        identity,
        card_number,
        bank_name,
        branch_name,
        clearing_number,
        opening_number,
        third_bank_card_id,
        cellphone,
        default_status,
        active_status,
        activation_time,
        active_fail_reason,
        ext,
        ctime,
        mtime,
        deleted,
        version,
        extend,
        extra
    </sql>
</mapper>
