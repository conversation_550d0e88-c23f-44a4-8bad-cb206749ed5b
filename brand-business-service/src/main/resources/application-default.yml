spring:
  redis-cluster:
    host: r-8vbm3pb0p4sw7cnuk5.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    password: sbnRrdGf@l^9HM7I
    database: 0
    pool:
      max-active: 16
      max-idle: 16
      min-idle: 0
      max-wait: 1500
  datasource:
    master:
      url: tk-brand-business-brand_business-5126?useTimezone=true&serverTimezone=GMT%2B8
      initialSize: 3
      maxActive: 15
      maxIdle: 15
      minIdle: 3
      test-while-idle: true
      validation-query: select 1 from dual
      validation-interval: 60000
      time-between-eviction-runs-millis: 60000
      driver-class-name: com.mysql.cj.jdbc.Driver
    slave:
      url: tk-brand-business-brand_business-5126?useTimezone=true&serverTimezone=GMT%2B8
      initialSize: 3
      maxActive: 15
      maxIdle: 15
      minIdle: 3
      test-while-idle: true
      validation-query: select 1 from dual
      validation-interval: 60000
      time-between-eviction-runs-millis: 60000
      driver-class-name: com.mysql.cj.jdbc.Driver

# redisson
redisson:
  redis:
    host: r-8vbm3pb0p4sw7cnuk5.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    password: sbnRrdGf@l^9HM7I
    database: 0

xxl:
  job:
    admin-addresses: http://ft-xxl-job-admin.beta.iwosai.com
    log-retention-days: 30
    app-name: ${spring.application.name}
    port: 9999
    access-token: ${ACCESS_TOKEN}
    log-path: /var/logs/ft-xxl-job

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

core-business: http://core-business.beta.iwosai.com/
merchant-center: http://merchant-center.beta.iwosai.com/
merchant-bank-service: http://merchant-bank-service-11842.beta.iwosai.com/
merchant-user-service: http://merchant-user-service.beta.iwosai.com/
uc-user-service: http://uc-user-service.beta.iwosai.com/
core-crypto-service: http://core-crypto.beta.iwosai.com/
bank-info: http://bank-info-service.beta.iwosai.com/
crow-api: http://***************:18081/
upay-transaction: http://upay-transaction.beta.iwosai.com/
sp-workflow-service: http://sp-workflow-service.beta.iwosai.com/
merchant-business-open: http://merchant-business-open.beta.iwosai.com/
trade-manage-service: http://trade-manage-service.beta.iwosai.com/
sales-system-poi: http://sales-system-poi.beta.iwosai.com/
merchant-contract-job: http://merchant-contract-job.beta.iwosai.com/
pay-business-open: http://pay-business-open.beta.iwosai.com/
merchant-contract-access: http://merchant-contract-access.beta.iwosai.com/
sales-system-service: http://sales-system-service.beta.iwosai.com/
short-url-service: http://short-url.beta.iwosai.com/
marketing-prepaid-card: http://marketing-saas-prepaid-card.beta.iwosai.com/
profit-sharing-proxy: http://profit-sharing-proxy.beta.iwosai.com/
shouqianba-tools-service: http://shouqianba-tools-service.beta.iwosai.com/

appid:
  indirect: 6a50e156-7222-41a9-99e1-43d87a0dfc9a
  paymentHub: 1a185007-9cf2-4267-982c-dde4216c4305
  brandPaymentHub: 72dde1df-624d-48e6-ba6f-ad2aad79da52
devCode:
  brandPaymentHub: NK9TWCCLTUHZ

#维金接口请求地址
vygin-service-address: http://testv.iwosai.com/mgs/service.do
#网商银行接口请求地址
#ant.mybank.merchantprod.merch.applet.register.query
#ant.mybank.bkcloudfunds.vostro.batchquery
#ant.mybank.bkcloudbatch.batch.create
#ant.mybank.bkcloudbatch.batch.query
#ant.mybank.bkmerchantprod.merch.applet.pre.register
#ant.mybank.merchantprod.merchant.arrangement.audit
#ant.mybank.merchantprod.merchant.arrangement.info.query
#ant.mybank.bkcloudfunds.order.withhold.apply
#ant.mybank.bkcloudfunds.merchant.account.unfreeze.apply
#ant.mybank.bkcloudfunds.merchant.balance.unfreeze.query
#ant.mybank.bkcloudfunds.protocol.withhold.query
#ant.mybank.bkcloudfunds.protocol.withhold.refund.apply
#ant.mybank.bkcloudfunds.protocol.withhold.refund.query
#以上 head头 AppId  I   大写，请求mybank-service-address
mybank-service-address: http://bkgwdev.dl.alipaydev.com/open/lite/api/common/request.htm
#  ant.mybank.bkcloudfunds.withdraw.apply
#  ant.mybank.bkcloudfunds.withdraw.applyconfirm
#  ant.mybank.bkcloudfunds.withdraw.query
#  ant.mybank.bkcloudfunds.bill.pay
#  ant.mybank.merchantprod.merch.register.query
#  ant.mybank.bkcloudfunds.merchant.scene.balance.query
#  ant.mybank.bkcloudfunds.account.balance.query
#  ant.mybank.bkcloudfunds.recon.query
#  ant.mybank.bkcloudfunds.account.open
#  ant.mybank.bkcloudfunds.account.query
#  以上 head头 Appid  i   小写，请求mybank-fcsupergw-address
mybank-fcsupergw-address: https://fcsupergw.mybank.dl.alipaydev.com/open/api/common/request2.htm
# 中信银行接口请求地址
citic-service-address: https://tseb.laastg.test.citicbank.com:38443/api/public
# 富友接口请求地址
fuiou-service-address: https://richOperationFront-test.fuioupay.com

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
logging:
  level:
    com.wosai.cua.brand.business.service.domain.service.VfinanceInterfaceService: DEBUG
    com.wosai.cua.brand.business.service.domain.service.FeiShuMessageRobotInvokeService: DEBUG

uc:
  secret: c09d2add-0cd4-40e5-a071-ab78ad77a376

core:
  crypto:
    client:
      access_id: ea5c64cb-5c3f-40a9-a4db-b2ad015c823f
      access_secret: b863a586f354485ba92c4d15041850f4

tmp:
  folder: tmp/

mybatis-plus:
  global-config:
    db-config:
      id-type: auto
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

brand:
  kafka:
    bootstrap-servers: ***************:9092,***************:9092,***************:9092
    schema:
      registry:
        url: http://192.168.103.171:8081,http://192.168.103.172:8081,http://192.168.103.173:8081
    base:
      topic: events_CUA_base-brand-business
    data-center:
      topic: analytics_data_volcengine_push
    old:
      bootstrap-servers: 192.168.101.89:9092,192.168.100.52:9092,192.168.101.90:9092
      schema:
        registry:
          url: http://192.168.101.89:8081,http://192.168.100.52:8081,http://192.168.101.90:8081

#飞书相关配置
open:
  feishu:
    appid: cli_a637d2a389f3d013
    appSecret: 0F6c0EwdFGQgObOdUMHCQfACJWvjQjFJ
    message_robot_secret: KDbIvIMPyjwlKGBa1yrSEc
    message_robot_url: https://open.feishu.cn/open-apis/bot/v2/hook/8dc18ef8-e91c-4e21-9abc-369859da63e4

fuiou:
  open:
    account:
      check_type: 2


app-id:
  merchant: ********

aggregation:
  open:
    notify-url: http://brand-business/internal/callback/aggregationOpenNotify
databus:
  consumer:
    topic: databus_CUA_merchant_basic_allin
consumer:
  init: true