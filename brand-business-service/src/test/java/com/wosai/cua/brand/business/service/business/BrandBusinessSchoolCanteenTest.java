package com.wosai.cua.brand.business.service.business;

import com.google.common.collect.Sets;
import com.wosai.cua.brand.business.api.dto.request.CancelMerchantAffiliationForSchoolCanteenRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.CreateMerchantAffiliationForSchoolCanteenRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.common.CommonResult;
import com.wosai.cua.brand.business.api.enums.ApplicationScenarioEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import com.wosai.cua.brand.business.service.externalservice.contractjob.MerchantContractJobClient;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeRequest;
import com.wosai.cua.brand.business.service.externalservice.contractjob.model.PaymentModeChangeResult;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.MerchantCenterClient;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryRequest;
import com.wosai.cua.brand.business.service.externalservice.merchantcenter.model.MerchantInfoQueryResult;
import com.wosai.cua.brand.business.service.module.brand.BrandMerchantModule;
import com.wosai.cua.brand.business.service.module.brand.BrandModule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BrandBusinessSchoolCanteenTest {

    @InjectMocks
    private BrandBusiness brandBusiness;

    @Mock
    private com.wosai.cua.brand.business.service.domain.service.BrandDomainService brandDomainService;

    @Mock
    private MerchantCenterClient merchantCenterClient;

    @Mock
    private MerchantContractJobClient merchantContractJobClient;

    private CreateMerchantAffiliationForSchoolCanteenRequestDTO createRequest;
    private CancelMerchantAffiliationForSchoolCanteenRequestDTO cancelRequest;

    @Before
    public void setUp() {
        createRequest = new CreateMerchantAffiliationForSchoolCanteenRequestDTO();
        createRequest.setMainMerchantSn("main_merchant_sn");
        createRequest.setMerchantSn("sub_merchant_sn_1");

        cancelRequest = new CancelMerchantAffiliationForSchoolCanteenRequestDTO();
        cancelRequest.setMainMerchantSn("main_merchant_sn");
        cancelRequest.setMerchantSns(Sets.newHashSet("sub_merchant_sn_1", "sub_merchant_sn_2"));
        cancelRequest.setPaymentMode(PaymentModeEnum.MERCHANT_MODE); // MERCHANT_MODE
    }

    @Test
    public void testCreateMerchantAffiliationForSchoolCanteen_MainBrandMerchantIsNull() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id")).thenReturn(null);

        MerchantInfoQueryResult subMerchantInfo1 = new MerchantInfoQueryResult();
        subMerchantInfo1.setMerchantId("sub_merchant_id_1");
        subMerchantInfo1.setMerchantSn("sub_merchant_sn_1");
        subMerchantInfo1.setName("Sub Merchant 1");
        subMerchantInfo1.setContactName("Contact 1");
        subMerchantInfo1.setContactCellphone("12345678901");

        MerchantInfoQueryResult subMerchantInfo2 = new MerchantInfoQueryResult();
        subMerchantInfo2.setMerchantId("sub_merchant_id_2");
        subMerchantInfo2.setMerchantSn("sub_merchant_sn_2");
        subMerchantInfo2.setName("Sub Merchant 2");
        subMerchantInfo2.setContactName("Contact 2");
        subMerchantInfo2.setContactCellphone("12345678902");

        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(argThat(
                req -> "sub_merchant_sn_1".equals(req.getMerchantSn()))))
                .thenReturn(subMerchantInfo1);
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(argThat(
                req -> "sub_merchant_sn_2".equals(req.getMerchantSn()))))
                .thenReturn(subMerchantInfo2);

        PaymentModeChangeResult paymentModeChangeResult = new PaymentModeChangeResult();
        paymentModeChangeResult.setCode(200);
        when(merchantContractJobClient.changePaymentMode(any(PaymentModeChangeRequest.class)))
                .thenReturn(paymentModeChangeResult);

        // Act
        brandBusiness.createMerchantAffiliationForSchoolCanteen(createRequest);

        // Verify
        verify(brandDomainService, times(1)).createBrand(any(BrandModule.class));
        verify(brandDomainService, times(2)).createBrandMerchant(any(BrandMerchantModule.class));
        verify(merchantContractJobClient, times(2)).changePaymentMode(any(PaymentModeChangeRequest.class));
    }

    @Test(expected = BrandBusinessException.class)
    public void testCreateMerchantAffiliationForSchoolCanteen_MainBrandMerchantNotAdmin() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setMerchantType(MerchantTypeEnum.BRAND_OWNER.getMerchantType()); // Not BRAND_ADMIN
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        // Act
        brandBusiness.createMerchantAffiliationForSchoolCanteen(createRequest);
    }

    @Test(expected = BrandBusinessException.class)
    public void testCreateMerchantAffiliationForSchoolCanteen_MainBrandNotUniversityCafeteria() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setMerchantType(MerchantTypeEnum.BRAND_ADMIN.getMerchantType());
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        BrandModule brandModule = new BrandModule();
        brandModule.setApplicationScenario(ApplicationScenarioEnum.DEFAULT); // Not UNIVERSITY_CAFETERIA
        when(brandDomainService.getBrandModuleByBrandId(anyString()))
                .thenReturn(brandModule);

        // Act
        brandBusiness.createMerchantAffiliationForSchoolCanteen(createRequest);
    }

    @Test
    public void testCreateMerchantAffiliationForSchoolCanteen_SubMerchantAlreadyAssociatedWithDifferentBrand() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setMerchantType(MerchantTypeEnum.BRAND_ADMIN.getMerchantType());
        mainBrandMerchant.setBrandId("brand_id_1");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        BrandModule brandModule = new BrandModule();
        brandModule.setApplicationScenario(ApplicationScenarioEnum.UNIVERSITY_CAFETERIA);
        when(brandDomainService.getBrandModuleByBrandId(anyString()))
                .thenReturn(brandModule);

        // Sub merchant already associated with different brand
        BrandMerchantModule subBrandMerchant = new BrandMerchantModule();
        subBrandMerchant.setBrandId("brand_id_2"); // Different brand
        when(brandDomainService.getBrandMerchantInfoByMerchantId("sub_merchant_id_1"))
                .thenReturn(subBrandMerchant);

        MerchantInfoQueryResult subMerchantInfo = new MerchantInfoQueryResult();
        subMerchantInfo.setMerchantId("sub_merchant_id_1");
        subMerchantInfo.setMerchantSn("sub_merchant_sn_1");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(argThat(
                req -> "sub_merchant_sn_1".equals(req.getMerchantSn()))))
                .thenReturn(subMerchantInfo);

        // Act & Assert
        try {
            brandBusiness.createMerchantAffiliationForSchoolCanteen(createRequest);
            fail("Expected BrandBusinessException");
        } catch (BrandBusinessException e) {
            // Expected
        }
    }

    @Test
    public void testCancelMerchantAffiliationForSchoolCanteen_Success() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setBrandId("brand_id");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        List<BrandMerchantModule> brandMerchants = new ArrayList<>();
        BrandMerchantModule subMerchant1 = new BrandMerchantModule();
        subMerchant1.setMerchantSn("sub_merchant_sn_1");
        subMerchant1.setMerchantId("sub_merchant_id_1");
        subMerchant1.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandMerchants.add(subMerchant1);

        BrandMerchantModule subMerchant2 = new BrandMerchantModule();
        subMerchant2.setMerchantSn("sub_merchant_sn_2");
        subMerchant2.setMerchantId("sub_merchant_id_2");
        subMerchant2.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandMerchants.add(subMerchant2);

        when(brandDomainService.getBrandMerchantByBrandId("brand_id"))
                .thenReturn(brandMerchants);

        PaymentModeChangeResult paymentModeChangeResult = new PaymentModeChangeResult();
        paymentModeChangeResult.setCode(200);
        when(merchantContractJobClient.changePaymentMode(any(PaymentModeChangeRequest.class)))
                .thenReturn(paymentModeChangeResult);

        // Act
        Map<String, CommonResult> result = brandBusiness.cancelMerchantAffiliationForSchoolCanteen(cancelRequest);

        // Verify
        assertEquals(2, result.size());
        assertTrue(result.get("sub_merchant_sn_1").isSuccess());
        assertTrue(result.get("sub_merchant_sn_2").isSuccess());
        verify(merchantContractJobClient, times(2)).changePaymentMode(any(PaymentModeChangeRequest.class));
        verify(brandDomainService, times(2)).updateBrandMerchant(any(BrandMerchantModule.class));
    }

    @Test
    public void testCancelMerchantAffiliationForSchoolCanteen_SubMerchantNotFound() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setBrandId("brand_id");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        List<BrandMerchantModule> brandMerchants = new ArrayList<>();
        BrandMerchantModule subMerchant = new BrandMerchantModule();
        subMerchant.setMerchantSn("sub_merchant_sn_1"); // Only one merchant
        subMerchant.setMerchantId("sub_merchant_id_1");
        subMerchant.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandMerchants.add(subMerchant);

        when(brandDomainService.getBrandMerchantByBrandId("brand_id"))
                .thenReturn(brandMerchants);

        // Act
        Map<String, CommonResult> result = brandBusiness.cancelMerchantAffiliationForSchoolCanteen(cancelRequest);

        // Verify
        assertEquals(2, result.size());
        assertFalse(result.get("sub_merchant_sn_2").isSuccess()); // This one should fail
        assertEquals("品牌商户不存在", result.get("sub_merchant_sn_2").getMessage());
        verify(merchantContractJobClient, times(1)).changePaymentMode(any(PaymentModeChangeRequest.class));
    }

    @Test(expected = BrandBusinessException.class)
    public void testCancelMerchantAffiliationForSchoolCanteen_MainBrandNotFound() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(null); // Main brand not found

        // Act
        brandBusiness.cancelMerchantAffiliationForSchoolCanteen(cancelRequest);
    }

    @Test
    public void testCancelMerchantAffiliationForSchoolCanteen_PaymentModeChangeFails() {
        // Arrange
        MerchantInfoQueryResult mainMerchantInfo = new MerchantInfoQueryResult();
        mainMerchantInfo.setMerchantId("main_merchant_id");

        BrandMerchantModule mainBrandMerchant = new BrandMerchantModule();
        mainBrandMerchant.setBrandId("brand_id");
        when(merchantCenterClient.queryLatestMerchantInfoByMerchantIdOrMerchantSn(any(MerchantInfoQueryRequest.class)))
                .thenReturn(mainMerchantInfo);
        when(brandDomainService.getBrandMerchantInfoByMerchantId("main_merchant_id"))
                .thenReturn(mainBrandMerchant);

        List<BrandMerchantModule> brandMerchants = new ArrayList<>();
        BrandMerchantModule subMerchant = new BrandMerchantModule();
        subMerchant.setMerchantSn("sub_merchant_sn_1");
        subMerchant.setMerchantId("sub_merchant_id_1");
        subMerchant.setPaymentMode(PaymentModeEnum.MERCHANT_MODE.getCode());
        brandMerchants.add(subMerchant);

        when(brandDomainService.getBrandMerchantByBrandId("brand_id"))
                .thenReturn(brandMerchants);

        PaymentModeChangeResult paymentModeChangeResult = new PaymentModeChangeResult();
        paymentModeChangeResult.setCode(200);
        paymentModeChangeResult.setMsg("Payment mode change failed");
        when(merchantContractJobClient.changePaymentMode(any(PaymentModeChangeRequest.class)))
                .thenReturn(paymentModeChangeResult);

        // Act
        Map<String, CommonResult> result = brandBusiness.cancelMerchantAffiliationForSchoolCanteen(cancelRequest);

        // Verify
        assertFalse(result.get("sub_merchant_sn_1").isSuccess());
        assertTrue(result.get("sub_merchant_sn_1").getMessage().contains("Payment mode change failed"));
    }
}
