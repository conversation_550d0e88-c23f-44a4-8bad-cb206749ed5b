package com.wosai.cua.brand.business.service.domain.dao;

import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.entity.SeparateAccountDO;
import com.wosai.cua.brand.business.service.domain.service.SeparateAccountDOService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@SpringBootTest
@RunWith(SpringRunner.class)
public class AccountAllocationTests {

    @Autowired
    private SeparateAccountDOService separateAccountDOService;

    @Autowired
    private SeparateAccountDOMapper separateAccountDOMapper;

    @Test
    public void testCreateAccountAllocation() {
        SeparateAccountDO separateAccountDO = new SeparateAccountDO();
        separateAccountDO.setAccountNumber("123456");
        separateAccountDO.setBrandId("123123");
        separateAccountDO.setAccountOpenedTime(new Date());
        separateAccountDO.setAccountOpenStatus("HAVE_NOT_OPENED");
        separateAccountDO.setExt(JSON.toJSONString(separateAccountDO));
        separateAccountDOMapper.insert(separateAccountDO);
    }
}
