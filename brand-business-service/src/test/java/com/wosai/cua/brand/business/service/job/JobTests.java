package com.wosai.cua.brand.business.service.job;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class JobTests {
    @Autowired
    private SeparateAccountInfoInitJobHandler separateAccountInfoInitJobHandler;

    @Autowired
    private UpdateCiticMerchantInfoJobHandler updateCiticMerchantInfoJobHandler;

    @Test
    public void test() {
        updateCiticMerchantInfoJobHandler.updateCiticMerchantInfo();
    }


}
