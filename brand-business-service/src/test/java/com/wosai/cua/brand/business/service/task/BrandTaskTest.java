package com.wosai.cua.brand.business.service.task;


import com.alibaba.fastjson2.JSON;
import com.wosai.cua.brand.business.service.domain.entity.BrandMerchantDO;
import com.wosai.cua.brand.business.service.domain.entity.other.QueryMerchantConditionsDO;
import com.wosai.cua.brand.business.service.thread.BatchPushBrandMerchantTask;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class BrandTaskTest {

    @Autowired
    private BatchPushBrandMerchantTask batchPushBrandMerchantTask;

    @Test
    public void testPageQueryFunctions(){
        List<String> merchantIds = new ArrayList<>();
        merchantIds.add("9ec35d83-9345-437f-9a9c-c253ec65b1c6");
        merchantIds.add("b9fc12fb29c6-7899-5e11-003b-83dbbccc");
        merchantIds.add("67700410-0d15-405c-b173-bde188a56cb6");
        batchPushBrandMerchantTask.processBatch(merchantIds);
    }
}
