package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

/**
 * 应用场景枚举
 * 0-默认，1-高校食堂，2-团餐，3-品牌连锁
 */
@Getter
public enum ApplicationScenarioEnum {
    DEFAULT("DEFAULT", "默认"),
    UNIVERSITY_CAFETERIA("UNIVERSITY_CAFETERIA", "高校食堂"),
    GROUP_MEAL("GROUP_MEAL", "团餐"),
    BRAND_CHAIN("BRAND_CHAIN", "品牌连锁");

    private final String code;
    private final String description;

    ApplicationScenarioEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ApplicationScenarioEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ApplicationScenarioEnum scenario : ApplicationScenarioEnum.values()) {
            if (scenario.getCode().equals(code)) {
                return scenario;
            }
        }
        return null;
    }
}