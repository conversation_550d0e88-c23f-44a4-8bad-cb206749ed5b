package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.app.AppActiveSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppBaseSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppCreateSeparateAccountSettleCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteOrSetDefaultSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppSeparateAccountSettleCardResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * APP  分账结算银行卡管理
 */
@JsonRpcService(value = "rpc/app/brand/separate/account/settle/card")
@Validated
public interface AppSeparateAccountSettleCardFacade {

    /**
     * 获取分账结算银行卡列表
     *
     * @param request 请求对象
     * @return 分账结算银行卡列表
     */
    List<AppSeparateAccountSettleCardResponseDTO> getSeparateAccountSettleCardList(@Valid AppBaseSeparateAccountDTO request);

    /**
     * 新增分账结算银行卡
     *
     * @param request 创建分账结算银行卡请求对象
     * @return 是否成功
     */
    boolean createSeparateAccountSettleCard(@Valid AppCreateSeparateAccountSettleCardDTO request);

    /**
     * 删除分账结算银行卡
     *
     * @param request 删除分账结算银行卡请求对象
     * @return 是否成功
     */
    boolean deleteSeparateAccountSettleCard(@Valid AppDeleteOrSetDefaultSeparateAccountCardDTO request);

    /**
     * 设置默认分账结算银行卡
     *
     * @param request 设置默认分账结算银行卡请求对象
     * @return 是否成功
     */
    boolean setDefaultSeparateAccountSettleCard(@Valid AppDeleteOrSetDefaultSeparateAccountCardDTO request);

    /**
     * 激活分账结算银行卡
     *
     * @param request 激活分账结算银行卡请求对象
     * @return 是否成功
     */
    boolean activateSeparateAccountSettleCard(@Valid AppActiveSeparateAccountCardDTO request);
}
