package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AppDeleteSeparateAccountCardDTO {

    @JsonProperty("merchant_id")
    @NotBlank(message = "商户ID不能为空")
    private String tokenMerchantId;

    @NotBlank(message = "分账账户编号不能为空")
    private String accountNumber;
}
