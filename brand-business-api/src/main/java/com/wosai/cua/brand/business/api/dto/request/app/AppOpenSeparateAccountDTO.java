package com.wosai.cua.brand.business.api.dto.request.app;

import com.wosai.cua.brand.business.api.enums.AccountTypeEnum;
import com.wosai.cua.brand.business.api.enums.BrandBusinessExceptionEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import com.wosai.cua.brand.business.api.exception.BrandBusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

@Data
public class AppOpenSeparateAccountDTO {
    /**
     * 品牌ID
     */
    @NotBlank(message = "品牌ID不能为空")
    private String brandId;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    private String accountName;

    /**
     * 子账号类型：FRANCHISE-加盟、BRAND_OWNED-品牌自有、BRAND_SELF_OPERATED-品牌自营、SUPPLIER-供应
     */
    @NotNull(message = "子账号类型不能为空")
    private SeparateAccountTypeEnum accountType;

    /**
     * 类型：PERSONAL-个人、INDIVIDUAL_BUSINESS-个体工商户、COMPANY-企业
     */
    @NotNull(message = "类型不能为空")
    private AccountTypeEnum type;

    /**
     * 证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照，99-统一社会信用代码
     */
    @NotBlank(message = "证件类型不能为空")
    private String idType;

    /**
     * 证件编号
     */
    @NotBlank(message = "证件编号不能为空")
    private String idNumber;

    /**
     * 法人姓名
     */
    @NotBlank(message = "法人姓名不能为空")
    private String legalPersonName;

    /**
     * 法人证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照
     */
    @NotBlank(message = "法人证件类型不能为空")
    private String legalPersonIdType;

    /**
     * 法人证件编号
     */
    @NotBlank(message = "法人证件编号不能为空")
    private String legalPersonId;

    /**
     * 法人手机
     */
    @NotBlank(message = "法人手机不能为空")
    private String legalPersonPhone;

    /**
     * 联系人姓名
     */
    private String contractName;

    /**
     * 联系人手机号
     */
    private String contractPhone;

    /**
     * 联系人证件类型：01-身份证、02-香港居民通行证、03-澳门居民通行证、04-台胞证、05-外国护照
     */
    private String contractIdType;

    /**
     * 联系人证件编号
     */
    private String contractId;

    /**
     *  收钱吧收单门店ID
     */
    private String sqbStoreId;

    /**
     *  收单商户ID
     */
    private String merchantId;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 美团门店ID
     */
    private String meiTuanStoreId;

    /**
     * 饿了么门店ID
     */
    private String elmStoreId;

    /**
     * 抖音门店ID
     */
    private String dyStoreId;

    /**
     * 结算卡信息（富友必填）
     */
    private SettlementCardDTO settlementCard;

    @Data
    public static class SettlementCardDTO {
        /**
         * 结算卡类型：1-对私、2-对公
         */
        private Integer type;

        /**
         * 户名
         */
        private String holder;

        /**
         * 卡号
         */
        private String cardNumber;

        /**
         * 开户行行号
         */
        private String openingNumber;

        /**
         * 手机号
         */
        private String cellphone;

        /**
         * 银行名称
         */
        private String bankName;

        /**
         * 分支行名称
         */
        private String branchBank;
    }

    public boolean checkParams(){
        switch (type) {
            case PERSONAL:
                if (StringUtils.isBlank(idNumber)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "个人类型身份证号不能为空");
                }
                if (StringUtils.isBlank(legalPersonPhone)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "个人类型手机号不能为空");
                }
                break;
            case INDIVIDUAL_BUSINESS:
            case COMPANY:
                if (StringUtils.isBlank(legalPersonName)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人姓名不能为空");
                }
                if (StringUtils.isBlank(legalPersonId)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人证件号不能为空");
                }
                if (StringUtils.isBlank(legalPersonIdType)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "法人证件类型不能为空");
                }
                if (StringUtils.isBlank(legalPersonPhone)) {
                    throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "签约手机号不能为空");
                }

                break;
        }
        return true;
    }

    public boolean checkSettlementCardParams(String fundManagementCompanyCode){
        if (StringUtils.isBlank(fundManagementCompanyCode)){
            return true;
        }
        if (FundManagementCompanyEnum.FUIOU.getFundManagementCompanyCode().equals(fundManagementCompanyCode)){
            if (settlementCard == null){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "结算卡信息不能为空");
            }
            if (Objects.isNull(settlementCard.getType())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "结算卡类型不能为空");
            }
            if (StringUtils.isBlank(settlementCard.getHolder())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "结算卡户名不能为空");
            }
            if (StringUtils.isBlank(settlementCard.getCardNumber())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "结算卡卡号不能为空");
            }
            if (StringUtils.isBlank(settlementCard.getCellphone())){
                throw new BrandBusinessException(BrandBusinessExceptionEnum.PARAMS_ERROR, "结算卡手机号不能为空");
            }
        }
        return true;
    }
}
