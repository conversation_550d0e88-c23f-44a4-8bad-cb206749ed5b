package com.wosai.cua.brand.business.api.dto.response.app;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppOpenSeparateAccountResponseDTO {
    /**
     * 开户结果:SUCCESS-开通成功，PROCESSING-处理中，FAIL-开通失败
     */
    private String result;

    /**
     * 开户失败原因
     */
    private String failReason;

    /**
     * 分账账户编号
     */
    private String accountNumber;
}
