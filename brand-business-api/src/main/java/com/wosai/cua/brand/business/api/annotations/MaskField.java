package com.wosai.cua.brand.business.api.annotations;


import com.wosai.cua.brand.business.api.enums.MaskFieldEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface MaskField {
    MaskFieldEnum fieldType() default MaskFieldEnum.DEFAULT;
}
