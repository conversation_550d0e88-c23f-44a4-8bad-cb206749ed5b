package com.wosai.cua.brand.business.api.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PageSeparateAccountInfoResponseDTO {
    /**
     * 总记录数
     */
    private Long total;
    /**
     * 当前页记录
     */
    private List<SeparateAccountInfoResponseDTO> records;

    public PageSeparateAccountInfoResponseDTO(Long total, List<SeparateAccountInfoResponseDTO> records) {
        this.total = total;
        this.records = records;
    }
}
