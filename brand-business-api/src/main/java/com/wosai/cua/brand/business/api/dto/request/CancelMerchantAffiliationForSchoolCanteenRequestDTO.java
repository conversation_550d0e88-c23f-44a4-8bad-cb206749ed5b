package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/8/21
 */
@Data
public class CancelMerchantAffiliationForSchoolCanteenRequestDTO {

    @NotEmpty(message = "主商户商户号不能为空")
    private String mainMerchantSn;
    @NotEmpty(message = "子商户号列表不能为空")
    private Set<String> merchantSns;
    @NotNull(message = "支付模式不能为空")
    private PaymentModeEnum paymentMode;
}
