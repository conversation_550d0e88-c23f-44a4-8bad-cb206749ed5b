package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageQueryBrandMerchantsDTO {
    /**
     * 当前页
     */
    private Integer page;

    /**
     * 每页展示条数
     */
    private Integer pageSize;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 品牌类型集合，参照枚举类
     *
     * @see MerchantTypeEnum
     */
    private List<String> merchantTypes;

    private List<String> types;

    /**
     * 品牌id集合
     */
    private List<String> brandIds;

    /**
     * 品牌编号集合
     */
    private List<String> brandSnList;

    /**
     * 提现策略id集合（要查没有关联过提现策略的商户传0）
     */
    private List<Long> strategyIdList;

    /**
     * 是否需要分页
     */
    private Boolean needPaging;

    /**
     * 账户状态
     *
     * @see BrandMerchantAccountOpenStatusEnum
     */
    private String accountOpenStatus;

    /**
     * 外部商户号
     */
    private String outMerchantNo;

    /**
     * 美团门店编号
     */
    private String meiTuanStoreSn;

    /**
     * 饿了么门店编号
     */
    private String elmStoreSn;

    /**
     * 抖音门店编号
     */
    private String dyStoreSn;

    /**
     * 银行卡状态
     * @see BankCardActivateStatusEnum
     */
    private String bankCardActivateStatus;
}
