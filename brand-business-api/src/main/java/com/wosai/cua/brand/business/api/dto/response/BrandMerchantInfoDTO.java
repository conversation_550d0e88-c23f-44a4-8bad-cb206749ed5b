package com.wosai.cua.brand.business.api.dto.response;


import com.wosai.cua.brand.business.api.dto.brand.BrandMerchantExtraDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.enums.MerchantDockingModeEnum;
import com.wosai.cua.brand.business.api.enums.MerchantTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BrandMerchantInfoDTO {
    /**
     * 品牌id
     */
    private String brandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 品牌编号
     */
    private String brandSn;
    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户经营名称
     */
    private String merchantBusinessName;

    /**
     * 商户类型
     * @see MerchantTypeEnum
     */
    private String merchantType;

    private String merchantTypeDesc;

    /**
     * 商户对接模式
     */
    private MerchantDockingModeEnum merchantDockingMode;

    private Integer paymentMode;
    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 商户联系人姓名
     */
    private String merchantContactName;
    /**
     * 商户联系人电话
     */
    private String merchantContactPhone;

    /**
     * 商户联系人邮箱
     */
    private String merchantContactEmail;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 街道地址
     */
    private String streetAddress;

    /**
     * 行业id
     */
    private String industry;

    /**
     * 行业名称
     */
    private String industryName;
    /**
     * 收钱吧门店号
     */
    private String sqbStoreId;

    /**
     * 收钱吧门店编号
     */
    private String sqbStoreSn;
    /**
     * 美团门店编号
     */
    private String meiTuanStoreSn;
    /**
     * 美团门店状态
     */
    private String meiTuanStoreStatus;
    /**
     * 饿了么门店编号
     */
    private String elmStoreSn;

    /**
     * 抖音门店编号
     */
    private String dyStoreSn;
    /**
     * 外部商户编号
     */
    private String outMerchantNo;

   /**
    * 合作方id
     * @deprecated partnerId在1.4.0之后的版本将废弃，该字段1.4.0后将视为配置，存放在config中
     */
    @Deprecated
    private String partnerId;
    /**
     * 提现策略
     */
    private WithdrawStrategyInfoDTO withdrawStrategyInfo;
    /**
     * 提现银行卡信息
     */
    private BrandMerchantBankCardResponseDTO brandMerchantBankCard;
    /**
     * 商户执照信息
     */
    private MerchantBusinessLicenseInfoDTO merchantBusinessLicenseInfo;
    /**
     * 维金账号信息
     * @deprecated 后续将废弃，后续版本将放在merchantAccount对象中
     */
    @Deprecated
    private VfinanceInfoDTO vfinanceInfo;

    private String type;

    private String typeDesc;

    /**
     * 商户账户开通状态
     * @see com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum
     */
    private String accountOpenStatus;

    /**
     * 商户账户开通状态描述
     */
    private String accountOpenStatusDesc;

    /**
     * 商户账户开通失败原因
     */
    private String accountOpenFailureReason;

    /**
     * 品牌配置
     */
    private BrandConfigDTO config;

    /**
     * 商户账号信息
     */
    private MerchantAccountDTO merchantAccount;

    /**
     * 商户额外信息
     */
    private BrandMerchantExtraDTO extra;

    /**
     * 激活链接
     */
    private String activationUrl;

    private String bankCardActivateStatus;

    private Date accountOpenedTime;

    private Date createdTime;

    private Date associatedTime;

    /**
     * 是否开通收付通：0-否，1-是
     */
    private Integer sftTag;
}
