package com.wosai.cua.brand.business.api.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 创建二级品牌商户请求DTO
 * 
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class CreateSecondaryBrandMerchantRequestDTO {
    
    /**
     * 品牌id
     */
    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    /**
     * 商户编号
     */
    @NotBlank(message = "商户编号不能为空")
    private String merchantSn;
} 