package com.wosai.cua.brand.business.api.enums;

import java.util.Arrays;
import java.util.Iterator;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum BankAccountTypeEnum {
    /**
     *
     */
    PERSONAL(1,"对私"),
    COMPANY(2,"对公")
    ;

    private final Integer accountType;

    private final String desc;


    BankAccountTypeEnum(Integer accountType, String desc) {
        this.accountType = accountType;
        this.desc = desc;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByAccountType(Integer accountType){
        if (Objects.isNull(accountType)){
            return null;
        }
        BankAccountTypeEnum[] values = BankAccountTypeEnum.values();
        Iterator<BankAccountTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BankAccountTypeEnum accountTypeEnum = iterator.next();
            if (accountTypeEnum.accountType.equals(accountType)){
                return accountTypeEnum.desc;
            }
        }
        return null;
    }

    public static Integer getTypeByDesc(String desc){
        BankAccountTypeEnum[] values = BankAccountTypeEnum.values();
        Iterator<BankAccountTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            BankAccountTypeEnum accountTypeEnum = iterator.next();
            if (accountTypeEnum.desc.equals(desc)){
                return accountTypeEnum.accountType;
            }
        }
        return null;
    }
}
