package com.wosai.cua.brand.business.api.dto.brand;

import com.alibaba.fastjson2.JSON;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 品牌基础数据传输对象
 *
 * <AUTHOR>
 * @date 2025/6/27 14:41
 */
@Data
public class BrandBaseDTO {

    /**
     * 集团号
     */
    private String groupId;

    /**
     * 外部品牌号
     */
    private String clientSn;

    /**
     * 服务商ID
     */
    private String vendorId;

    /**
     * 所属组织ID
     */
    private String organizationId;

    /**
     * 维护人ID
     */
    private String maintainerId;

    /**
     * extra
     */
    private String extra;

    /**
     * 支付条件在 extra 中的 key
     */
    public static final String PAYMENT_TERM_KEY = "payment_term";

    /**
     * 支付方案在 extra 中的 key
     */
    public static final String PAYMENT_SOLUTION_KEY = "payment_solution";


    /**
     * 设置支付条件
     * extra转为map格式，添加key是 "payment_term" 的值，入参是支付条件的json字符串
     *
     * @param paymentTermJson 支付条件的json字符串
     */
    public void setPaymentTerm(Object paymentTermJson) {
        Map<String, Object> extraMap;
        if (this.extra != null && !this.extra.trim().isEmpty()) {
            try {
                extraMap = JSON.parseObject(this.extra, Map.class);
            } catch (Exception e) {
                extraMap = new HashMap<>();
            }
        } else {
            extraMap = new HashMap<>();
        }
        extraMap.put(PAYMENT_TERM_KEY, paymentTermJson);
        this.extra = JSON.toJSONString(extraMap);
    }



    /**
     * 设置支付方案
     * extra转为map格式，添加key是 "payment_solution" 的值，入参是支付方案的json字符串
     *
     * @param paymentSchemeJson 支付方案的json字符串
     */
    public void setPaymentScheme(Object paymentSchemeJson) {
        Map<String, Object> extraMap;
        if (this.extra != null && !this.extra.trim().isEmpty()) {
            try {
                extraMap = JSON.parseObject(this.extra, Map.class);
            } catch (Exception e) {
                extraMap = new HashMap<>();
            }
        } else {
            extraMap = new HashMap<>();
        }
        extraMap.put(PAYMENT_SOLUTION_KEY, paymentSchemeJson);
        this.extra = JSON.toJSONString(extraMap);
    }


    /**
     * 同时设置支付条件和支付方案
     *
     * @param paymentTermJson 支付条件的json字符串
     * @param paymentSchemeJson 支付方案的json字符串
     */
    public void setPaymentInfo(Object paymentTermJson, Object paymentSchemeJson) {
        Map<String, Object> extraMap;
        if (this.extra != null && !this.extra.trim().isEmpty()) {
            try {
                extraMap = JSON.parseObject(this.extra, Map.class);
            } catch (Exception e) {
                extraMap = new HashMap<>();
            }
        } else {
            extraMap = new HashMap<>();
        }

        if (paymentTermJson != null) {
            extraMap.put(PAYMENT_TERM_KEY, paymentTermJson);
        }
        if (paymentSchemeJson != null) {
            extraMap.put(PAYMENT_SOLUTION_KEY, paymentSchemeJson);
        }

        this.extra = JSON.toJSONString(extraMap);
    }

    /**
     * 获取支付条件
     *
     * @return 支付条件的json字符串，如果不存在则返回null
     */
    public Object getPaymentTerm() {
        if (this.extra == null || this.extra.trim().isEmpty()) {
            return null;
        }
        try {
            Map<String, Object> extraMap = JSON.parseObject(this.extra, Map.class);
            return extraMap.get(PAYMENT_TERM_KEY);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取支付方案
     *
     * @return 支付方案的json字符串，如果不存则返回null
     */
    public Object getPaymentScheme() {
        if (this.extra == null || this.extra.trim().isEmpty()) {
            return null;
        }
        try {
            Map<String, Object> extraMap = JSON.parseObject(this.extra, Map.class);
            return extraMap.get(PAYMENT_SOLUTION_KEY);
        } catch (Exception e) {
            return null;
        }
    }


}
