package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum SeparateAccountIdTypeEnum {

    /**
     * 身份证
     */
    ID_CARD("01", "身份证"),

    /**
     * 香港居民通行证
     */
    HK_PASS("02", "香港居民通行证"),

    /**
     * 澳门居民通行证
     */
    MO_PASS("03", "澳门居民通行证"),

    /**
     * 台胞证
     */
    TW_PASS("04", "台胞证"),

    /**
     * 外国护照
     */
    FOREIGN_PASSPORT("05", "外国护照"),

    /**
     * 统一社会信用代码
     */
    UNIFIED_SOCIAL_CREDIT_CODE("99", "统一社会信用代码");

    private final String code;
    private final String desc;

    SeparateAccountIdTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过证件类型编码获取描述
     * @param code 证件类型编码
     * @return 证件类型描述，如果未找到对应的编码则返回null
     */
    public static String getDescByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SeparateAccountIdTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }
}
