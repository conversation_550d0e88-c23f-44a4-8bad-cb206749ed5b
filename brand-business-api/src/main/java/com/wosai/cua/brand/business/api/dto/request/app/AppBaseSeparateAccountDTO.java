package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AppBaseSeparateAccountDTO {

    @NotBlank(message = "分账账户编号不能为空")
    private String accountNumber;

    @NotBlank(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String tokenMerchantId;

}
