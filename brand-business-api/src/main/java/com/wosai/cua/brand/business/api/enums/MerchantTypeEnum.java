package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Iterator;

/**
 * 品牌商户类型枚举
 * <AUTHOR>
 */

public enum MerchantTypeEnum {
    /**
     * 商户类型：FRANCHISEE-加盟商、SUPPLIER-供应商、BRAND_OWNER-品牌商、BRAND_OPERATED_STORES-品牌自营门店、SERVICE_PROVIDER_SQB-服务商（收钱吧）
     */
    FRANCHISEE("FRANCHISEE","加盟商"),
    SUPPLIER("SUPPLIER","供应商"),
    BRAND_OWNER("BRAND_OWNER","品牌商"),
    BRAND_OPERATED_STORES("BRAND_OPERATED_STORES","品牌自营门店"),
    SERVICE_PROVIDER_SQB("SERVICE_PROVIDER_SQB","服务商（收钱吧）"),
    SERVICE_PROVIDER("SERVICE_PROVIDER","服务商"),
    /**
     * 品牌管理商户只负责管理该品牌的相关操作，不与其他业务挂钩，只是一个管理的商户，商户下的用户具有相应的角色以及权限
     */
    BRAND_ADMIN("BRAND_ADMIN","品牌管理商户"),

    AGENT("AGENT","代理商")
    ;

    /**
     * 商户类型
     */
    @Getter
    private final String merchantType;
    /**
     * 描述
     */
    @Getter
    private final String desc;

    MerchantTypeEnum(String merchantType, String desc) {
        this.merchantType = merchantType;
        this.desc = desc;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByMerchantType(String merchantType){
        MerchantTypeEnum[] values = MerchantTypeEnum.values();
        Iterator<MerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            MerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.merchantType.equals(merchantType)){
                return merchantTypeEnum.desc;
            }
        }
        return null;
    }

    public static MerchantTypeEnum getEnumByMerchantType(String merchantType){
        MerchantTypeEnum[] values = MerchantTypeEnum.values();
        Iterator<MerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            MerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.merchantType.equals(merchantType)){
                return merchantTypeEnum;
            }
        }
        return null;
    }

    public static MerchantTypeEnum getEnumByDesc(String desc){
        MerchantTypeEnum[] values = MerchantTypeEnum.values();
        Iterator<MerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            MerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.desc.equals(desc)){
                return merchantTypeEnum;
            }
        }
        return null;
    }

    public static String getMerchantTypeByDesc(String desc){
        if (StringUtils.isEmpty(desc)){
            return null;
        }
        MerchantTypeEnum[] values = MerchantTypeEnum.values();
        Iterator<MerchantTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            MerchantTypeEnum merchantTypeEnum = iterator.next();
            if (merchantTypeEnum.desc.equals(desc)){
                return merchantTypeEnum.merchantType;
            }
        }
        return null;
    }
}
