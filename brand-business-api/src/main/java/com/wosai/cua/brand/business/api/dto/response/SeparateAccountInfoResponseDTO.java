package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wosai.cua.brand.business.api.annotations.MaskField;
import com.wosai.cua.brand.business.api.enums.SeparateAccountTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class SeparateAccountInfoResponseDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 品牌ID
     */
    private String brandId;
    /**
     * 品牌编号
     */
    private String brandSn;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 账户编号
     */
    private String accountNumber;

    /**
     * 账户类型：PERSONAL-个人、INDIVIDUAL_BUSINESS-个体工商户、COMPANY-企业
     */
    private String type;

    /**
     * 账户类型
     */
    private SeparateAccountTypeEnum accountType;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户状态
     */
    private String accountStatus;

    /**
     * 账户状态描述
     */
    private String accountStatusDesc;

    /**
     * 绑定银行卡状态
     */
    private String bindCardStatus;

    /**
     * 绑定银行卡状态描述
     */
    private String bindCardStatusDesc;

    /**
     * 绑定银行卡号
     */
    @MaskField
    private String bindBankCardNumber;

    /**
     * 绑定银行账号名称
     */
    private String bindBankAccountName;

    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 账户类型:  1-对私账户 2-对公账户
     */
    private Integer bankAccountType;

    /**
     * 账户类型描述
     */
    private String bankAccountTypeDesc;

    /**
     * 绑定银行卡id
     */
    private String thirdBankCardId;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 法人名称
     */
    private String legalPersonName;

    /**
     * 关联商户编号
     */
    private List<String> relatedMerchantSnList;

    /**
     * 关联门店编号
     */
    private List<String> relatedStoreSnList;

    /**
     * 关联的外部商户号
     */
    private List<String> relatedOutMerchantNoList;

    /**
     * 关联的美团门店id
     */
    private List<String> relatedMeiTuanStoreIdList;

    /**
     * 关联的饿了么门店id
     */
    private List<String> relatedElmStoreIdList;

    /**
     * 关联的抖音门店id
     */
    private List<String> relatedDyStoreIdList;

    /**
     * 账户子账户编号
     */
    private String subAccountNo;

    /**
     * 会员编号
     */
    private String memberId;

    /**
     * 结算策略ID
     */
    private Long strategyId;

    /**
     * 激活链接
     */
    private String activationUrl;

    /**
     * 激活短链接
     */
    private String activationShortUrl;
}
