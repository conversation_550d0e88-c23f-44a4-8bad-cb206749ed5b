package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.PageSeparateAccountInfoRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.QuerySeparateAccountRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.PageSeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.SeparateAccountInfoResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 分账账户应用端接口
 */
@JsonRpcService(value = "rpc/brand/separate/account")
@Validated
public interface SeparateAccountFacade {

    /**
     * 分页查询分账账户信息
     * @param request 请求参数
     * @return 分页查询结果
     */
    PageSeparateAccountInfoResponseDTO pageSeparateAccountInfo(@Valid PageSeparateAccountInfoRequestDTO request);

    /**
     * 根据账户编号查询分账账户信息
     * @param request 账户编号
     * @return 分账账户信息
     */
    SeparateAccountInfoResponseDTO getSeparateAccountInfoByAccountNumber(QuerySeparateAccountRequestDTO request);
}
