package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/8/20
 */
@Data
public class CreateMerchantAffiliationForSchoolCanteenRequestDTO {

    @NotEmpty(message = "主商户商户号不能为空")
    private String mainMerchantSn;
    @NotEmpty(message = "子商户商户号不能为空")
    private String merchantSn;
    @NotNull(message = "支付模式不能为空")
    private PaymentModeEnum paymentMode;
}
