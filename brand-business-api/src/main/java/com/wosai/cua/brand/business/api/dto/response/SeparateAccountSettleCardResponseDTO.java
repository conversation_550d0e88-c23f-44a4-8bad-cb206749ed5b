package com.wosai.cua.brand.business.api.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class SeparateAccountSettleCardResponseDTO {
    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 分账账户编号
     */
    private String accountNumber;
    /**
     * 结算卡持有方名称
     */
    private String holder;
    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 账号类型
     */
    private Integer accountType;

    /**
     * 账号类型描述
     */
    private String accountTypeDesc;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行名称
     */
    private String branchName;
    /**
     * 背景图
     */
    private String bankBackPicture;
    /**
     * 银行图标
     */
    private String bankIcon;

    /**
     * 激活状态
     */
    private Integer activateStatus;
    /**
     * 激活状态描述
     */
    private String activateStatusDesc;
    /**
     * 是否是默认卡
     */
    private Boolean defaultCard;

    /**
     * 激活失败原因
     */
    private String activateFailReason;

    /**
     * 预留手机号
     */
    private String mobile;

    /**
     * 激活时间
     */
    private Date activationTime;

    /**
     * 三方银行卡id
     */
    private String thirdBankCardId;
}
