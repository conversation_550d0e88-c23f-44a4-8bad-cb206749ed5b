package com.wosai.cua.brand.business.api.facade.app;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.app.AppDeleteSeparateAccountCardDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppOpenSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.app.AppPageSeparateAccountInfoRequestDTO;
import com.wosai.cua.brand.business.api.dto.response.PageSeparateAccountInfoResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.app.AppOpenSeparateAccountResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 分账账户应用端接口
 */
@JsonRpcService(value = "rpc/app/brand/separate/account")
@Validated
public interface AppSeparateAccountFacade {

    /**
     * 分页查询分账账户信息
     * @param request 请求参数
     * @return 分页查询结果
     */
    PageSeparateAccountInfoResponseDTO pageSeparateAccountInfo(@Valid AppPageSeparateAccountInfoRequestDTO request);

    /**
     * 创建分账账户
     * @param request 创建分账账户请求参数
     * @return 创建结果
     */
    AppOpenSeparateAccountResponseDTO openSeparateAccount(@Valid AppOpenSeparateAccountDTO request);

    /**
     * 删除分账账户
     * @param request 删除分账账户请求参数
     * @return 删除结果
     */
    boolean deleteSeparateAccount(@Valid AppDeleteSeparateAccountCardDTO request);
}
