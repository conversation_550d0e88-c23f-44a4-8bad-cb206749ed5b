package com.wosai.cua.brand.business.api.dto.request;

import com.wosai.cua.brand.business.api.enums.BankCardActivateStatusEnum;
import com.wosai.cua.brand.business.api.enums.BrandMerchantAccountOpenStatusEnum;
import lombok.Data;

import java.util.List;

@Data
public class PageSeparateAccountInfoRequestDTO {
    /**
     * 页码
     */
    private Integer page;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 开户账户状态
     */
    private BrandMerchantAccountOpenStatusEnum openStatus;

    /**
     * 银行卡激活状态
     */
    private BankCardActivateStatusEnum bankCardActivateStatus;

    /**
     * 已选ID列表
     */
    private List<Long> selectedIdList;

    /**
     * 需要排除的数据id
     */
    private List<Long> excludeIdList;
}
