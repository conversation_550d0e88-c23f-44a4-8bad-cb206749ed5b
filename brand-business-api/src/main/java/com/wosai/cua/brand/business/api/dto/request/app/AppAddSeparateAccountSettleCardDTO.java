package com.wosai.cua.brand.business.api.dto.request.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class AppAddSeparateAccountSettleCardDTO {
    /**
     * 商户id
     */
    @NotBlank(message = "商户di必传")
    @JsonProperty("merchant_id")
    private String tokenMerchantId;

    @NotBlank(message = "账户编号不能为空")
    private String accountNumber;

    /**
     * 账户类型 1 对私 2 对公
     */
    @NotNull(message = "type不能为空")
    private Integer type;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号bankCardNo不能为空")
    private String bankCardNo;

    /**
     * 开户行号
     */
    @NotBlank(message = "开户行号openingNumber，不能为空")
    private String openingNumber;

    @NotBlank(message = "账户持有人名称holder不能为空")
    private String holder;

    private Boolean setDefault;

    @NotBlank(message = "银行预留手机号不能为空")
    private String reservedMobileNumber;
}
