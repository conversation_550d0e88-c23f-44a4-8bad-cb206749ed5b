package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum BrandMerchantAccountOpenStatusEnum {
    CLOSED_ACCOUNT("CLOSED_ACCOUNT","已销户"),
    HAVE_NOT_OPENED("HAVE_NOT_OPENED", "未开通"),
    IN_OPENING("IN_OPENING", "开通中"),
    OPENED("OPENED", "已开通"),
    OPEN_FAILURE("OPEN_FAILURE", "开通失败"),
    UNDER_REVIEW("UNDER_REVIEW", "审核中"),
    TO_BE_ACTIVATED("TO_BE_ACTIVATED", "待激活"),
    TO_BE_CONTROLLED("TO_BE_CONTROLLED", "被管控"),
    INVALID("INVALID","已失效"),
    PENDING("PENDING","待生效"),
    DECLINED("DECLINED", "已拒绝"),
    REMOVED("REMOVED", "已解除"),
    EFFECTIVE("EFFECTIVE", "已生效");

    private final String status;
    private final String desc;

    BrandMerchantAccountOpenStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static BrandMerchantAccountOpenStatusEnum getByStatus(String status) {
        for (BrandMerchantAccountOpenStatusEnum brandMerchantAccountOpenStatusEnum : values()) {
            if (brandMerchantAccountOpenStatusEnum.getStatus().equals(status)) {
                return brandMerchantAccountOpenStatusEnum;
            }
        }
        return null;
    }

    public static String getStatusDescription(String status){
        if (status == null) {
            return null;
        }
        for (BrandMerchantAccountOpenStatusEnum brandMerchantAccountOpenStatusEnum : values()) {
            if (brandMerchantAccountOpenStatusEnum.getStatus().equals(status)) {
                return brandMerchantAccountOpenStatusEnum.getDesc();
            }
        }
        return null;
    }

}
