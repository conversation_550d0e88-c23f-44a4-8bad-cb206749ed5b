package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Iterator;

@Getter
public enum AccountTypeEnum {
    /**
     *
     */
    PERSONAL("PERSONAL","个人/小微商户"),

    INDIVIDUAL_BUSINESS("INDIVIDUAL_BUSINESS","个体工商户"),

    COMPANY("COMPANY","企业")
    ;
    private final String type;

    private final String desc;

    AccountTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getTypeByDesc(String desc){
        if (StringUtils.isEmpty(desc)){
            return null;
        }
        AccountTypeEnum[] values = AccountTypeEnum.values();
        Iterator<AccountTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            AccountTypeEnum accountTypeEnum = iterator.next();
            if (accountTypeEnum.desc.equals(desc)){
                return accountTypeEnum.type;
            }
        }
        return null;
    }

    public static String getTypeDescByType(String type){
        if (StringUtils.isEmpty(type)){
            return null;
        }
        AccountTypeEnum[] values = AccountTypeEnum.values();
        Iterator<AccountTypeEnum> iterator = Arrays.stream(values).iterator();
        while (iterator.hasNext()){
            AccountTypeEnum accountTypeEnum = iterator.next();
            if (accountTypeEnum.type.equals(type)){
                return accountTypeEnum.desc;
            }
        }
        return null;
    }
}
