package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SeparateAccountTypeEnum {

    /**
     * 加盟
     */
    FRANCHISE("FRANCHISE", "加盟"),

    /**
     * 品牌自有
     */
    BRAND_OWNED("BRAND_OWNED", "品牌自有"),

    /**
     * 品牌自营
     */
    BRAND_SELF_OPERATED("BRAND_SELF_OPERATED", "品牌自营"),

    /**
     * 供应
     */
    SUPPLIER("SUPPLIER", "供应");

    private final String code;
    private final String desc;

    SeparateAccountTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过类型编码获取描述
     * @param code 类型编码
     * @return 类型描述，如果未找到对应的编码则返回null
     */
    public static String getDescByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SeparateAccountTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static SeparateAccountTypeEnum getSeparateAccountTypeEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SeparateAccountTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static SeparateAccountTypeEnum getSeparateAccountTypeEnumByMerchantTypeEnum(MerchantTypeEnum merchantTypeEnum) {
        if (Objects.isNull(merchantTypeEnum)){
            return SeparateAccountTypeEnum.FRANCHISE;
        }
        if (merchantTypeEnum.equals(MerchantTypeEnum.BRAND_OWNER)){
            return SeparateAccountTypeEnum.BRAND_OWNED;
        }
        if (merchantTypeEnum.equals(MerchantTypeEnum.FRANCHISEE)){
            return SeparateAccountTypeEnum.FRANCHISE;
        }
        if (merchantTypeEnum.equals(MerchantTypeEnum.BRAND_OPERATED_STORES)){
            return SeparateAccountTypeEnum.BRAND_SELF_OPERATED;
        }
        if (merchantTypeEnum.equals(MerchantTypeEnum.SUPPLIER)){
            return SeparateAccountTypeEnum.SUPPLIER;
        }
        return SeparateAccountTypeEnum.FRANCHISE;
    }
}
