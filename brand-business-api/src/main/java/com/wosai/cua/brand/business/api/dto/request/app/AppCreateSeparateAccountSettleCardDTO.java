package com.wosai.cua.brand.business.api.dto.request.app;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class AppCreateSeparateAccountSettleCardDTO extends AppBaseSeparateAccountDTO{
    /**
     * 账户类型 1 对私 2 对公
     */
    @NotNull(message = "type不能为空")
    private Integer type;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号bankCardNo不能为空")
    private String bankCardNo;

    /**
     * 开户行号
     */
    private String openingNumber;

    @NotBlank(message = "账户持有人名称holder不能为空")
    private String holder;

    private Boolean setDefault;

    @NotBlank(message = "银行预留手机号不能为空")
    private String mobile;

    private String bankName;

    private String branchName;
}
