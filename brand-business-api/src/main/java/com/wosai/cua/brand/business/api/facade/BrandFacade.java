package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.brand.ConfigDTO;
import com.wosai.cua.brand.business.api.dto.brand.SubAccountOpenDetailDTO;
import com.wosai.cua.brand.business.api.dto.brand.TemplateConfigDTO;
import com.wosai.cua.brand.business.api.dto.request.*;
import com.wosai.cua.brand.business.api.dto.request.brand.BaseBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandPaymentHubOpenRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandSaveSmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.brand.BrandSmsTemplateConfigRequest;
import com.wosai.cua.brand.business.api.dto.request.brand.CancelEnterRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandForOpenIndirectApplyRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandForReconciliationApplyRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.CreateBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.DeleteBrandDTO;
import com.wosai.cua.brand.business.api.dto.request.brand.ModifyBrandRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CheckAccountDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.CreateBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.ImportBrandMerchantForAuditRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.brandmerchant.RemoveBrandMerchantAssociationRequestDTO;
import com.wosai.cua.brand.business.api.dto.request.merchant.ModifyBrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantsQueryResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandPaymentModeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.ChangeAcquirerCheckResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.CreateMerchantResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.GenerateKeyResponse;
import com.wosai.cua.brand.business.api.dto.response.ModifyBrandResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.PageBrandMerchantsDTO;
import com.wosai.cua.brand.business.api.dto.response.PaymentModeChangeResponseDTO;
import com.wosai.cua.brand.business.api.dto.response.brand.BrandConfigDTO;
import com.wosai.cua.brand.business.api.dto.response.common.CommonResult;
import com.wosai.cua.brand.business.api.dto.response.common.MultiCommonResult;
import com.wosai.cua.brand.business.api.enums.CheckAccountResultEnum;
import com.wosai.cua.brand.business.api.enums.FundManagementCompanyEnum;
import com.wosai.cua.brand.business.api.enums.MerchantBrandTypeEnum;
import com.wosai.cua.brand.business.api.dto.request.CreateMerchantAffiliationForSchoolCanteenRequestDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 品牌相关方法
 *
 * <AUTHOR>
 */
@JsonRpcService(value = "rpc/brand")
@Validated
public interface BrandFacade {

    /**
     * 创建品牌
     *
     * @param createBrandRequest 创建信息
     * @return 品牌编号&品牌id
     */
    CreateBrandResponseDTO createBrand(@Valid CreateBrandRequestDTO createBrandRequest);

    /**
     * 创建品牌 - 对账申请
     * @param createBrandForReconciliationApplyRequestDTO 创建品牌的参数
     * @return 品牌编号&品牌ID
     */
    CreateBrandResponseDTO createBrandForReconciliationApply(@Valid CreateBrandForReconciliationApplyRequestDTO createBrandForReconciliationApplyRequestDTO);

    /**
     * 创建品牌 - 新增品牌开通间连扫码
     * @param  createBrandForBusinessOpenApplyRequest 业务开通申请，创建品牌的参数
     * @return 品牌编号&品牌ID
     */
    CreateBrandResponseDTO createBrandForOpenIndirectApply(@Valid CreateBrandForOpenIndirectApplyRequestDTO createBrandForBusinessOpenApplyRequest);

    /**
     * 开通收付通品牌
     * @return 品牌编号&品牌ID
     */
    CreateBrandResponseDTO openBrandPaymentHubForAudit(@Valid BrandPaymentHubOpenRequestDTO brandPaymentHubOpenRequest);

    /**
     * 存量品牌开通收付通 - 给存量未开通收付通的品牌新增收付通相关参数
     * @param existBrandOpenPaymentHubRequest 请求参数
     * @return 品牌编号&品牌ID
     */
    void existBrandOpenPaymentHub(@Valid ExistBrandOpenPaymentHubRequestDTO existBrandOpenPaymentHubRequest);


    /**
     * 从审批中导入品牌和商户的关联关系。可能是新增或者是删除
     * @param createBrandMerchantForAuditRequest 品牌和商户关联关系的参数
     *
     */
    void importBrandMerchantForAudit(@Valid ImportBrandMerchantForAuditRequestDTO createBrandMerchantForAuditRequest);
    /**
     * 根据商户id查询品牌信息
     *
     * @param queryBrandsDto 查询对象
     * @return 品牌简要集合
     */
    List<BrandSimpleInfoDTO> getBrandInfoListByMerchantId(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据商户id集合查询品牌信息
     *
     * @param queryBrandsDto 查询对象
     * @return 品牌信息集合
     */
    List<BrandSimpleInfoDTO> getBrandInfoListByMerchantIds(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据品牌id查询品牌详细信息
     *
     * @param queryBrandsDto 查询对象
     * @return 品牌详细信息
     */
    BrandDetailInfoDTO getBrandDetailInfoByBrandId(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据品牌SN查询品牌详细信息
     * @param queryBrandsDto 查询对象
     * @return 品牌详细信息
     */
    BrandDetailInfoDTO getBrandDetailInfoByBrandSn(QueryBrandsDTO queryBrandsDto);

    /**
     * 根据外部品牌号和服务商，查询品牌详细信息
     *
     * @param clientSn 外部品牌号
     * @param vendorId 服务商id
     * @return 品牌详细信息
     */
    BrandDetailInfoDTO getBrandDetailInfoByClientSnAndVendorId(String clientSn, String vendorId);

    /**
     * 分页查询品牌简易信息
     *
     * @param pageQueryBrandsDto 查询条件对象
     * @return 品牌列表
     */
    PageBrandInfoDTO pageBrandInfoList(PageQueryBrandsDTO pageQueryBrandsDto);

    /**
     * 删除品牌
     *
     * @param deleteBrandDto 删除品牌对象
     * @return 删除结果
     */
    Boolean deleteBrand(@Valid DeleteBrandDTO deleteBrandDto);

    /**
     * 批量删除品牌下商户
     *
     * @param deleteBrandMerchantDto 删除品牌下商户对象
     * @return 删除结果
     */
    int deleteBrandMerchant(@Valid DeleteBrandMerchantDTO deleteBrandMerchantDto);

    /**
     * 批量添加品牌下商户
     *
     * @param batchCreateBrandMerchantRequest 批量请求对象
     * @return 添加条数
     */
    int batchCreateBrandMerchant(@Valid BatchCreateBrandMerchantRequestDTO batchCreateBrandMerchantRequest);

    /**
     * 品牌下添加单个商户
     *
     * @param createBrandMerchant 品牌商户关联对象
     * @return 创建结果
     */
    CreateBrandMerchantResponseDTO createBrandMerchant(@Valid CreateBrandMerchantRequestDTO createBrandMerchant);

    /**
     * 创建品牌二级商户（简化版本）
     * 默认商家模式
     * 
     * @param createSecondaryBrandMerchant 二级品牌商户创建请求对象（只包含merchantSn和brandId）
     * @return 创建结果
     */
    CreateBrandMerchantResponseDTO createSecondaryBrandMerchant(@Valid CreateSecondaryBrandMerchantRequestDTO createSecondaryBrandMerchant);


    /**
     * 分页查询品牌下商户
     *
     * @param pageQueryBrandMerchantsDto 查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantsDTO pageQueryBrandMerchants(@Valid PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto);

    /**
     * 分页查询品牌下商户的简要信息
     * @param pageQueryBrandMerchantsDto 查询条件对象
     * @return 查询结果
     */
    PageBrandMerchantsDTO pageQuerySimpleBrandMerchants(@Valid PageQueryBrandMerchantsDTO pageQueryBrandMerchantsDto);

    /**
     * 查询品牌下所有商户和品牌下子品牌下所有商户
     * @param queryBrandsDTO 查询条件对象
     * @return 查询结果
     */
    List<BrandMerchantDTO> getAllBrandMerchantsByBrandId(@Valid QueryBrandsDTO queryBrandsDTO);

    /**
     * 获取品牌下所有商户和品牌下子品牌下所有商户
     * @param queryBrandsDTO 获取品牌下所有商户和品牌下子品牌下所有商户的参数
     * @return 获取结果
     */
    List<BrandMerchantDTO> getAllBrandMerchantsByBrandSn(@Valid QueryBrandsDTO queryBrandsDTO);

    /**
     * 编辑品牌信息
     *
     * @param modifyBrandRequestDto 编辑品牌请求对象
     * @return 编辑返回的对象
     */
    ModifyBrandResponseDTO modifyBrand(@Valid ModifyBrandRequestDTO modifyBrandRequestDto);

    /**
     * 根据门店id或者商户编号查询品牌商户信息
     *
     * @param queryBrandMerchantInfo 查询条件
     * @return 查询结果
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByStoreIdOrMerchantSn(QueryBrandMerchantInfoDTO queryBrandMerchantInfo);

    /**
     * 根据已有的品牌商户信息，创建子账号
     *
     * @param createBrandMerchantChildAccount 请求对象
     * @return 返回结果
     */
    Boolean createBrandMerchantChildAccount(@Valid CreateBrandMerchantChildAccountDTO createBrandMerchantChildAccount);

    /**
     * 根据merchantId获取品牌商户信息
     * @param merchantIdDto 商户ID请求对象
     * @return 品牌商户信息
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByMerchantId(@Valid MerchantIdDTO merchantIdDto);

    /**
     * 根据memberId获取品牌商户信息
     *
     * @param memberIdDto 请求对象
     * @return 品牌商户信息
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByMemberId(@Valid MemberIdDTO memberIdDto);

    /**
     * 根据outMerchantNo获取品牌商户信息
     *
     * @param outMerchantNoDto 外部商户号
     * @return 品牌商户信息
     */
    BrandMerchantInfoDTO getBrandMerchantInfoByOutMerchantNo(@Valid OutMerchantNoDTO outMerchantNoDto);

    /**
     * 关联提现策略
     *
     * @param relevanceWithdrawStrategy 关联提现策略请求
     * @return 关联成功数量
     */
    int relevanceBrandWithdrawStrategy(RelevanceWithdrawStrategyDTO relevanceWithdrawStrategy);

    /**
     * 创建商户
     *
     * @param createMerchant 创建商户对象
     * @return 创建结果
     */
    CreateMerchantResponseDTO createMerchant(CreateMerchantDTO createMerchant);

    /**
     * 登记member信息（平安-维金专用）
     *
     * @param signInMember 请求对象
     */
    void signInMember(@Valid SignInMemberDTO signInMember);

    /**
     * 重新注册会员信息
     *
     * @param brandMerchantRequest 请求对象
     * @return 返回结果
     */
    CreateMerchantResponseDTO reRegisterMemberInfo(ModifyBrandMerchantRequestDTO brandMerchantRequest);

    /**
     * 重新获取会员id(维金使用)
     *
     * @param signInMember
     */
    void reGetMemberId(@Valid SignInMemberDTO signInMember);

    /**
     * 登记
     *
     * @param appRequest 请求参数
     * @return boolean
     */
    Boolean registerBehaviorRecord(RegisterBehaviorDTO appRequest);

    /**
     * 获取渠道配置
     *
     * @param channelId   渠道id
     * @param channelType 渠道类型，详见：
     * @return 渠道配置：根据具体渠道类型返回对应配置。
     * @see FundManagementCompanyEnum
     */
    BrandConfigDTO getBrandConfigByChannelId(String channelId, FundManagementCompanyEnum channelType);

    /**
     * 处理平安配置(版本上线执行后下次上线删除)
     */
    @Deprecated
    void dealPabConfig();

    /**
     * 创建品牌配置
     *
     * @param config 配置对象
     * @return 创建结果
     */
    Boolean createBrandConfig(ConfigDTO config);

    /**
     * 编辑品牌商户信息
     *
     * @param modifyBrandMerchant 编辑品牌商户信息对象
     * @return 编辑结果
     */
    Boolean modifyBrandMerchant(ModifyBrandMerchantDTO modifyBrandMerchant);

    /**
     * 查询该商户是否是品牌商户
     * @param merchantIdDTO 商户ID
     * @return 品牌商户类型
     */
    MerchantBrandTypeEnum queryMerchantBrandType(@Valid MerchantIdDTO merchantIdDTO);

    /**
     * 判断是否允许切换收单机构
     * @param changeAcquirerCheckRequestDTO 请求参数
     * @return 校验结果
     */
    ChangeAcquirerCheckResponseDTO checkChangeAcquirer(@Valid ChangeAcquirerCheckRequestDTO changeAcquirerCheckRequestDTO);

    /**
     * 切换支付模式
     * @param paymentModeChangeRequestDTO 请求参数
     * @return 切换结果
     */
    PaymentModeChangeResponseDTO changePaymentMode(@Valid PaymentModeChangeRequestDTO paymentModeChangeRequestDTO);

    /**
     * 查询品牌支付模式-进件时选择入网规则组专用
     * @param merchantId 商户ID
     * @return 支付模式等信息 如果该商户不是品牌商户则会返回Null
     */
    BrandPaymentModeResponseDTO queryBrandPaymentModeForContract(@NotBlank(message = "商户ID不能为空") String merchantId);

    /**
     * 取消/撤销入驻
     * @return boolean
     */
    Boolean cancelEnter(CancelEnterRequestDTO cancelEnterRequest);

    /**
     * 查询品牌下商户的简要信息
     * @param merchantId 商户ID 子商户ID或主商户ID都可以
     * @return 品牌下所有的商户信息，如果不存在返回null
     */
    BrandMerchantsQueryResponseDTO queryBrandMerchants(@NotBlank(message = "商户ID不能为空") String merchantId);


    /**
     * 目前支持富友
     * @param queryBrandMerchantInfo 查询条件
     * @return 状态
     */
    SubAccountOpenDetailDTO querySubAccountOpenStatus(QueryBrandMerchantInfoDTO queryBrandMerchantInfo);

    /**
     * 单个解除商户和品牌的关联关系,需要切换支付模式回商户模式
     * @param requestDTO 品牌ID 商户 ID
     * @return 是否解除成功
     */
    CommonResult singleRemoveBrandMerchantAssociation(@Valid RemoveBrandMerchantAssociationRequestDTO requestDTO);

    /**
     * 单个创建商户和品牌的关联关系,需要切换支付模式
     * @param requestDTO 请求参数
     * @return 是否创建成功
     */
    CommonResult singleCreateBrandMerchantAssociation(@Valid CreateBrandMerchantAssociationRequestDTO requestDTO);

    /**
     * 特殊处理品牌商户开通账户
     * @param request 品牌ID
     * @return 结果
     */
    CommonResult specialTreatmentBrandMerchantOpenAccount(@Valid BaseBrandRequestDTO request);

    /**
     * 保存品牌服务通知模板配置
     * @return 结果
     */
    Boolean saveBrandSmsTemplateConfig(@Valid BrandSaveSmsTemplateConfigRequest request);

    /**
     * 获取品牌服务通知模板配置
     * @return 结果
     */
    List<TemplateConfigDTO> getBrandSmsTemplateConfig(@Valid BrandSmsTemplateConfigRequest request);

    /**
     * 品牌商户绑定银行卡
     * @param request 品牌ID
     * @return 结果
     */
    CommonResult handleBrandMerchantBindCard(@Valid BaseBrandRequestDTO request);

    /**
     * 批量开通子账号（异步）
     * @param request 品牌ID
     * @return 结果
     */
    CommonResult batchOpenSubAccounts(@Valid BaseBrandRequestDTO request);

    /**
     * 校验品牌商户账户
     * @return 校验结果
     */
    CheckAccountResultEnum checkMerchantAccount(@Valid CheckAccountDTO checkAccountDto);

    /**
     * 根据品牌id获取商户基础信息
     * @param request 请求对象
     * @return 查询结果
     */
    List<BrandMerchantDTO> getMerchantSimpleListByBrandId(@Valid BaseBrandRequestDTO request);

    /**
     * 生成品牌密钥
     * @return 密钥
     */
    GenerateKeyResponse generateBrandKey(@Valid GenerateBrandKeyRequest request);

    /**
     * 获取品牌公钥
     * @return 密钥
     */
    GenerateKeyResponse getBrandKey(@Valid BaseBrandRequestDTO request);

    /**
     * 启用收付通
     * @param request 入参
     */
    void enableSft(@Valid BaseBrandRequestDTO request);

    /**
     * 高校食堂 创建主商户和子商户的关联关系
     * @param request 请求参数
     * @return 关联结果
     */
    CommonResult createMerchantAffiliationForSchoolCanteen(@Valid CreateMerchantAffiliationForSchoolCanteenRequestDTO request);

    /**
     * 高校食堂 取消主商户和子商户的关联关系
     * @param request
     * @return
     */
    MultiCommonResult cancelMerchantAffiliationForSchoolCanteen(@Valid CancelMerchantAffiliationForSchoolCanteenRequestDTO request);
}
