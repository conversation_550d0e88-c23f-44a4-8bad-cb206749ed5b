package com.wosai.cua.brand.business.api.facade;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.cua.brand.business.api.dto.request.BaseSeparateAccountDTO;
import com.wosai.cua.brand.business.api.dto.response.SeparateAccountSettleCardResponseDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * APP  分账结算银行卡管理
 */
@JsonRpcService(value = "rpc/brand/separate/account/settle/card")
@Validated
public interface SeparateAccountSettleCardFacade {

    /**
     * 获取默认的分账结算银行卡
     *
     * @return 默认的分账结算银行卡
     */
    SeparateAccountSettleCardResponseDTO getDefaultSeparateAccountSettleCard(@Valid BaseSeparateAccountDTO request);
}
