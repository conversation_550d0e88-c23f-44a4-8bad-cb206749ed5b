package com.wosai.cua.brand.business.api.enums;

import lombok.Getter;

@Getter
public enum SeparateAccountRelateTypeEnum {

    /**
     * 商户id
     */
    MERCHANT_ID("MERCHANT_ID", "商户id"),

    /**
     * 商户编号
     */
    MERCHANT_SN("MERCHANT_SN", "商户编号"),

    /**
     * 收单门店编号
     */
    STORE_SN("STORE_SN", "收单门店编号"),

    /**
     * 收单门店id
     */
    STORE_ID("STORE_ID", "收单门店id"),

    /**
     * 外部商户
     */
    OUT_MERCHANT("OUT_MERCHANT", "外部商户"),

    /**
     * 资管机构子账号
     */
    SUB_ACCOUNT("SUB_ACCOUNT", "资管机构子账号"),

    /**
     * 第三方机构会员id
     */
    MEMBER_ID("MEMBER_ID", "第三方机构会员id"),

    /**
     * 美团门店
     */
    MT_STORE("MT_STORE", "美团门店"),

    /**
     * 饿了么门店
     */
    ELM_STORE("ELM_STORE", "饿了么门店"),

    /**
     * 抖音门店
     */
    DY_STORE("DY_STORE", "抖音门店");

    private final String type;
    private final String desc;

    SeparateAccountRelateTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
