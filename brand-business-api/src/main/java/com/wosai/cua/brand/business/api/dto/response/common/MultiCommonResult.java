package com.wosai.cua.brand.business.api.dto.response.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MultiCommonResult {

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 错误信息
     */
    private String message;

    private Map<String, CommonResult> results;

    public static MultiCommonResult success(Map<String, CommonResult> results) {
        return new MultiCommonResult(true, null, results);
    }

    public static MultiCommonResult fail(String message) {
        return new MultiCommonResult(false, message, null);
    }

}
